diff --git a/node_modules/react-native-calendars/src/calendar/day/index.js b/node_modules/react-native-calendars/src/calendar/day/index.js
index 24a1ce1..0dac8d1 100644
--- a/node_modules/react-native-calendars/src/calendar/day/index.js
+++ b/node_modules/react-native-calendars/src/calendar/day/index.js
@@ -12,6 +12,12 @@ function areEqual(prevProps, nextProps) {
     const prevPropsWithoutMarkDates = omit(prevProps, 'marking');
     const nextPropsWithoutMarkDates = omit(nextProps, 'marking');
     const didPropsChange = some(prevPropsWithoutMarkDates, function (value, key) {
+        if (typeof nextPropsWithoutMarkDates[key] === 'function') {
+            return false;
+        }
+        if (typeof nextPropsWithoutMarkDates[key] === 'object') {
+            return false;
+        }
         return value !== nextPropsWithoutMarkDates[key];
     });
     const isMarkingEqual = isEqual(prevProps.marking, nextProps.marking);
