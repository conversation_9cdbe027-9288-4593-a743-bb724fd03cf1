/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 */
import 'react-native-gesture-handler';
import codePush from "react-native-code-push";
import { NotifierWrapper } from 'react-native-notifier';

import React, { Component }  from 'react';
import {
  AppRegistry,
  StyleSheet,
  View,
  Text,
  TextInput,
  Platform,
  StatusBar,
  Image,
  LogBox
} from 'react-native';
import TrackPlayer from 'react-native-track-player';
// import messaging from '@react-native-firebase/messaging';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
// import { createReduxContainer, createReactNavigationReduxMiddleware, createNavigationReducer } from 'react-navigation-redux-helpers';
import { createStore, applyMiddleware, compose } from 'redux';
import thunk from 'redux-thunk';
import { Provider } from 'react-redux';
import { autoRehydrate, persistStore } from 'redux-persist'
import AsyncStorage from '@react-native-community/async-storage';
var {Actions} = require('react-native-router-flux');
import LottieView from 'lottie-react-native';
const axios = require('axios')
import { NavigationContainer } from '@react-navigation/native';
import { navigationRef } from './RootNavigation.js';
var Define = require('./src/Define');
var Util = require('./src/Util/Util');
var AppStateActions_MiddleWare = require( './src/actions/AppStateActions_MiddleWare');
Util.enableDebug();
import { typography } from './src/Util/typography';
import notifee, { AndroidImportance,AndroidStyle,EventType } from '@notifee/react-native';
var RDActions = require( './src/actions/RDActions');

typography();

// NOTE : must create global variable first of all
var {globalVariableManager} = require('./src/components/modules/GlobalVariableManager');
globalVariableManager.init();

Text.defaultProps = Text.defaultProps || {};
Text.defaultProps.maxFontSizeMultiplier = 1.2;
Text.defaultProps.allowFontScaling = false;
TextInput.defaultProps = TextInput.defaultProps || {};
TextInput.defaultProps.maxFontSizeMultiplier = 1.2;
TextInput.defaultProps.allowFontScaling = false;

var App = require('./src/containers/App');
import {createReducer, setStoreInstance} from './src/reducers';

// const createStoreWithMiddleware = applyMiddleware(thunk)(createStore);
// var store = createStoreWithMiddleware(todoApp);
// variable
const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F5FCFF',
  },
  welcome: {
    fontSize: 20,
    textAlign: 'center',
    margin: 10,
  },
  instructions: {
    textAlign: 'center',
    color: '#333333',
    marginBottom: 5,
  },
});

class WowWorker extends Component {
  constructor() {
    super();
    this.state = {
      loading: true,
      checkForUpdateCodePush: false
    };
    // const middlewareNav = createReactNavigationReduxMiddleware(state => state.nav);
    const enhancer = compose (
      applyMiddleware(thunk),
      autoRehydrate(),
      // applyMiddleware(middlewareNav)
    );
    this.store = createStore(createReducer(), enhancer);
    setStoreInstance(this.store)
    // console.disableYellowBox = true;
    // LogBox.ignoreAllLogs(true)
    LogBox.ignoreLogs(['Deprecation in \'navigationOptions\'']);
    Define.init(() => {
      persistStore(
        this.store,
        {storage: AsyncStorage, whitelist: ['AppSetting', 'User', 'Notify']},
        () => {this.setState({loading: false})}
      )
    })
  }
  getActiveRouteName = (state) => {
    const route = state.routes[state.index];
    if (route.state) {
      return this.getActiveRouteName(route.state);
    }
    return route.name;
  };
  render() {
    if (this.state.loading || this.state.checkForUpdateCodePush) {
      return (
        <View style={{ flex: 1, backgroundColor: '#015CBC', paddingTop: Platform.OS === 'ios' ? 0 : 0 }}>
          <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
            <Image
              source={Define.assets.Images.splashscreen}
              style={{ height: '100%', width: Define.constants.widthScreen }}
            />
          </View>
        </View>
      )
    } else {
      return (
        <Provider store={this.store}>
          <NavigationContainer
              ref={navigationRef}
             onStateChange={(state) => {
              if (state) {
                const currentScreen = this.getActiveRouteName(state);
                globalVariableManager.reduxManager.dispatch({type: 'NAVIGATION_FOCUS', routeName: currentScreen})
                const routes = state.routes.map(route => route.name);
                globalVariableManager.reduxManager.dispatch({type: 'STACK_CHANGE', stacks: routes})
              }
            }}>
            <GestureHandlerRootView style={{flex: 1}}>
              <NotifierWrapper>
                  <App wakeByLocation={this.props.UIApplicationLaunchOptionsLocationKey} />
              </NotifierWrapper>
            </GestureHandlerRootView>
          </NavigationContainer>
        </Provider>
      )
    }
  }

  UNSAFE_componentWillMount() {
  }

  getUpdateMetadata = () => {
    codePush.getUpdateMetadata()
      .then((update) => {
        let versionCodePush = '';
        if(update?.label) {
          const oldVersion = Platform.OS === 'ios' ? 126 : 10;
          const newVersion = Number(update?.label.replace('v', ''));
          versionCodePush = `v${oldVersion + newVersion}` || '';
        }
        Define.constants.versionCodePush = versionCodePush
      })
      .catch(() => {})
  }

  componentDidMount() {
    this.getUpdateMetadata();
    // const url = `${Define.constants.serverAddr}/api/v1.0/app/config-code-push`
    // axios
    //   .post(url,
    //     {
    //       appName: 'customer',
    //       platform: Platform.OS,
    //       nativeVersion: Define.constants.nativeVersion
    //     }
    //   )
    //   .then((result) => {
    //     if (result.status === 200 && result.data.code === 200) {
    //       if (!result || !result?.data?.data?.versionCodePush) {
    //         this.setState({ checkForUpdateCodePush: false });
    //       } else {
    //         this.getUpdateMetadata(result.data.data.versionCodePush);
    //       }
    //     } else {
    //       this.setState({ checkForUpdateCodePush: false });
    //     }
    //   })
    //   .catch((err) => {
    //     this.setState({ checkForUpdateCodePush: false });
    //   })
  }

}
if(Platform.OS === 'android') {
  messaging().setBackgroundMessageHandler(async (remoteMessage) => {
  });
    notifee.onForegroundEvent(({ type, detail }) => {
    });

    notifee.onBackgroundEvent(async ({ type, detail }) => {
      if (type === EventType.PRESS && detail?.pressAction?.id) {
        const notification = detail?.notification?.data
        try{
          notification.extras = JSON.parse(notification.extras);
        }catch(ex){};
        if(notification && notification.link) {
          globalVariableManager.navigatorManager.handleNavigator(notification.link,notification.extras);
        }
        let _idSellect = -1;
        for (let i = 0; i < globalVariableManager.reduxManager.state.Notify.arrays.length; i++) {
          if(notification && notification.sentTime && globalVariableManager.reduxManager.state.Notify.arrays[i].sentTime === notification.sentTime) {
            _idSellect = globalVariableManager.reduxManager.state.Notify.arrays[i]._id;
            break;
          }
        }
        if(_idSellect !== -1) {
            globalVariableManager.reduxManager.dispatch(RDActions['Notify']['removeOnRequest']({id:_idSellect}));
        }
      }
    });
}

const updateDialog = {
  appendReleaseDescription: false,
  descriptionPrefix: " Description: ",
  mandatoryContinueButtonLabel: "Cài đặt",
  mandatoryUpdateMessage: "Bản cập nhật mới cải tiến sửa lỗi ứng dụng. Bạn vui lòng cho phép ứng dụng cài đặt. Sau đó chờ trong giây lát. Xin cảm ơn.",
  optionalIgnoreButtonLabel: "Bỏ qua",
  optionalInstallButtonLabel: "Cài đặt",
  optionalUpdateMessage: "Bản cập nhật mới cải tiến sửa lỗi ứng dụng. Bạn vui lòng cho phép ứng dụng cài đặt. Sau đó chờ trong giây lát. Xin cảm ơn.",
  title: "Cập nhật ứng dụng"
}
TrackPlayer.registerPlaybackService(() => require('./src/components/modules/serviceTrackPlayer.js'));
AppRegistry.registerComponent('WowWorker', () => codePush({ checkFrequency: codePush.CheckFrequency.MANUAL, rollbackRetryOptions: {delayInHours: 12, maxRetryAttempts: 5} })(WowWorker));
