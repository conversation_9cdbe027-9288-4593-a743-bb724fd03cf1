/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 * @flow
 */

import React,{Component}  from 'react';
import {
  AppRegistry,
  NativeModules,
  View,
  AsyncStorage,
  Platform,
  Text,
  TextInput
} from 'react-native';
import { NotifierWrapper } from 'react-native-notifier';
import CodePush from "react-native-code-push";

import { createStore,applyMiddleware ,compose } from 'redux';
import thunk from 'redux-thunk';
import { Provider } from 'react-redux';
import { autoRehydrate, persistStore } from 'redux-persist'
import { createReduxContainer, createReactNavigationReduxMiddleware, createNavigationReducer } from 'react-navigation-redux-helpers';
import {createReducer, setStoreInstance} from './reducers';

import { typography } from './Util/typography';

typography();

//
Text.defaultProps = Text.defaultProps || {};
Text.defaultProps.maxFontSizeMultiplier = 1.2;
TextInput.defaultProps = TextInput.defaultProps || {};
TextInput.defaultProps.maxFontSizeMultiplier = 1.2;

var Util = require('./Util/Util');


  // NOTE : must create global variable first of all
  var {globalVariableManager}= require('./components/modules/GlobalVariableManager');

  globalVariableManager.init();
  Util.enableDebug();
  var App = require('./containers/App');
  var todoApp = require('./reducers');
  var Define = require('./Define');

    // variable

  class SanShip extends Component {
    constructor() {
      super();

      this.state = {
          loading: true,
      };

      const middlewareNav = createReactNavigationReduxMiddleware(state => state.nav);
      const enhancer = compose (
        applyMiddleware(thunk),
        autoRehydrate(),
        applyMiddleware(middlewareNav)
      );
      this.store = createStore(createReducer(), enhancer);
      setStoreInstance(this.store)
      console.disableYellowBox = true;
      Define.init(() => {
        persistStore(
          this.store,
          {storage: AsyncStorage, whitelist: ['AppSetting', 'User', 'Notify']},
          () => {this.setState({loading: false})}
        )
      })
    }

    componentDidMount() {
      CodePush.sync({ installMode: CodePush.InstallMode.IMMEDIATE });
    }

    render() {
      if(this.state.loading){
        return (<View/>)
      }else{
        return (
            <Provider store={this.store}>
              <NotifierWrapper>
                <App/>
              </NotifierWrapper>
            </Provider>
          )
      }
    }
  }


  AppRegistry.registerComponent('SanShip', () => CodePush({ checkFrequency: CodePush.CheckFrequency.MANUAL })(SanShip));
