
//LIB
import React from 'react';
import {
  View,
  NativeModules,
  DeviceEventEmitter,
  ToastAndroid,
  BackHandler,
  AppState,
  NativeAppEventEmitter,
  AlertIOS,
  Platform,
  Dimensions,
  InteractionManager,
  UIManager,
  TouchableOpacity,
  Text,
  Linking,
  ActivityIndicator,
  Vibration,
  Image,
  PermissionsAndroid,
  Keyboard,
  SafeAreaView,
  Alert,
  Animated,
  StatusBar
} from 'react-native';
UIManager.setLayoutAnimationEnabledExperimental && UIManager.setLayoutAnimationEnabledExperimental(true);
import {injectAsyncReducer} from '../reducers'
// import { createReduxContainer, createReactNavigationReduxMiddleware, createNavigationReducer } from 'react-navigation-redux-helpers';
import CodePush from "react-native-code-push";
import { createStackNavigator } from '@react-navigation/stack';
const Stack = createStackNavigator();
// import CallKitManager from '../components/modules/CallkitManager';
import IncomingLinkingManager from '../components/modules/IncomingLinkingManager';

import * as Animatable from 'react-native-animatable';
import { connect } from 'react-redux'
import {Scene, Reducer, Router, Switch, TabBar, Modal, Schema, Actions} from 'react-native-router-flux'
const RouterWithRedux = connect()(Router);
import { TransitionPresets } from 'react-navigation-stack';
// import messaging from '@react-native-firebase/messaging';
var SensorManager = NativeModules.SensorManager;
var Spinner = require('react-native-spinkit');

import HeyUIcon from '../components/elements/HeyUIcon/HeyUIcon';
import PenguinLinearIcon from '../components/elements/PenguinLinearIcon/PenguinLinearIcon';
import PenguinBoldIcon from '../components/elements/PenguinBoldIcon/PenguinBoldIcon';
let _ = require('lodash')
// var SQLite = require('react-native-sqlite-storage');
// SQLite.DEBUG(true);
// SQLite.enablePromise(true);
var createReactClass = require('create-react-class');
const ms = require('ms');

import BackgroundLocationManager from '../components/modules/BackgroundLocationManager';
var RNIntent = NativeModules.RNIntent;
var UtilNative = NativeModules.Util;
var DeviceInfo = require('react-native-device-info');
import PushNotificationIOS from "@react-native-community/push-notification-ios";
import NetInfo from "@react-native-community/netinfo";
import SplashScreen from "react-native-lottie-splash-screen";
import RNBootSplash from "react-native-bootsplash"
import LottieView from 'lottie-react-native';
import LinearGradient from 'react-native-linear-gradient';
// import VoipPushNotification from 'react-native-voip-push-notification';
import notifee, { AndroidImportance,AndroidStyle,EventType } from '@notifee/react-native';

import ReactNativeBiometrics from 'react-native-biometrics';

// action
var RDActionsTypes = require( '../actions/RDActionsTypes');
var RDActions = require( '../actions/RDActions');

var TempActions_MiddleWare = require( '../actions/TempActions_MiddleWare');
var AppStateActions_MiddleWare = require( '../actions/AppStateActions_MiddleWare');
var UserActions_MiddleWare = require( '../actions/UserActions_MiddleWare');
var FeedsSystemActions_MiddleWare = require( '../actions/FeedsSystemActions_MiddleWare');
//Component
var Debug = require('../Util/Debug');
var Util = require('../Util/Util');
var NotifyUtil = require('../Util/notify');
var Themes = require('../Themes');
var Define = require('../Define');
var Include = require('../Include');


var {globalVariableManager} = require('../components/modules/GlobalVariableManager');
var {androidNotifyChannelManager} = require('../components/modules/AndroidNotifyChannelManager');
if(Platform.OS === 'android') {
  androidNotifyChannelManager.init()
}
var locationManager = require('../components/modules/LocationManager');
var SocketOrderManager = require('../components/modules/SocketOrder');
var SocketFeedOrderSystem = require('../components/modules/SocketFeedOrderSystem');
var {PopupManager,popupActions,popupConst} = require('../components/popups/PopupManager');
import Toast, {DURATION} from 'react-native-easy-toast'
// import { SafeAreaView } from 'react-native-safe-area-context';
// var SCTVFilmsSideMenu = require('../components/elements/SCTVFilmsSideMenu');
// screens
import LoginScreen from '../components/screens/LoginScreen'
import WebviewScreen from '../components/screens/WebviewScreen'
import NotifyScreen from '../components/screens/NotifyScreen';
import MainContainer from '../components/screens/MainContainer';
import MainScreen from '../components/screens/MainScreen';
import ChangePassAccountScreen from '../components/screens/ChangePassAccountScreen';
import DetailNotificationScreen from '../components/screens/DetailNotificationScreen';
import ProfileScreen from '../components/screens/ProfileScreen';
import AccountScreen from '../components/screens/AccountScreen';
import LoginAccountScreen from '../components/screens/LoginAccountScreen';
import InstructionsForUseScreen from '../components/screens/InstructionsForUseScreen';
import LoginFaceIDScreen from '../components/screens/LoginFaceIDScreen';

var screenList=[
  DetailNotificationScreen,
  ProfileScreen,
  ChangePassAccountScreen,
  AccountScreen,
  MainContainer,
  MainScreen,
  LoginScreen,
  NotifyScreen,
  WebviewScreen,
  LoginAccountScreen,
  InstructionsForUseScreen,
  LoginFaceIDScreen,
];
//popups
import DefaultPopup from '../components/popups/DefaultPopup'
import NotifyPopup from '../components/popups/NotifyPopup'

// import WebRTC from '../components/popups/WebRTC'

//variable


var App = createReactClass({
  refToast: React.createRef(),
  refAnimatable: React.createRef(),
  lastBackgroundTime: null,
  requireFaceIDLogin: false,
  biometricKeysExist: null, // Track biometric key existence
  backgroundTimeout: null,   // Track background timer
  getInitialState() {
    return {
      hasSetApiGoogleMap: Platform.OS === 'android',
      wakeByLocation: this.props.wakeByLocation,
      openError: false
    }
  },
  webRTCAppear:false,
  changeWebRTCAppear:function(flag){
    var self = this;
    self.webRTCAppear = flag;
  },
  hideContentState:false,
  hideContent:function(flag=true){
    var self =this;
    if (flag) {
      // InteractionManager.runAfterInteractions(() => {
        self.refs.contentView.transitionTo({opacity:0},200)
      // })
      // self.refs.tutorialView.transitionTo({opacity:0.8},1200)
    }
    else{
      self.refs.contentView.transitionTo({opacity:1},200)
      // self.refs.tutorialView.transitionTo({opacity:0},600)
    }

    self.hideContentState=flag;
  },
  showToast: function(text, time=1000) {
    if(this.refs.toast) {
      this.refs.toast.show(text, time);
    }
  },
  getRegion: function() {
    return new Promise((resolve, reject) => {
      const {dispatch, appSetting, user} = this.props;
      const memberToken = _.get(user, 'memberInfo.member.memberToken', '');
        if(!appSetting.mode || !appSetting.regionNew || !memberToken || (Date.now() - appSetting.lastTimeGetRegion <= 300000 )) {
          return resolve();
        }
        locationManager
          .getCurrentLocation()
          .then((location) => {
            return dispatch(UserActions_MiddleWare.getRegionByLatLng({location}))
          })
          .then((res) => {
            resolve();
          })
          .catch((err) => {
            if(user && user.addressPicked && user.addressPicked.location) {
              dispatch(UserActions_MiddleWare.getRegionByLatLng({location: user.addressPicked.location}))
            }
            resolve();
          })
    })
  },
  getUser: function() {
    const {user, dispatch} = this.props;

    if(user.memberInfo._id) {
      dispatch(UserActions_MiddleWare.get())
    }
  },
  checkBiometricKeys: async function() {
    try {
      const rnBiometrics = new ReactNativeBiometrics();
      const { keysExist } = await rnBiometrics.biometricKeysExist();
      this.biometricKeysExist = keysExist;
    } catch (e) {
      this.biometricKeysExist = false;
    }
  },
  handleAppStateChange:function(currentAppState){
    var self = this;
    const { dispatch,state,navigator, appSetting} = this.props;
    Debug.log('handleAppStateChange ' + currentAppState , Debug.level.USER_TRACKER);

    switch (currentAppState) {
      case 'active': {
        setTimeout(() => {
          if (this.state.wakeByLocation) {
            this.setState({
              wakeByLocation: false
            })
          }
          if (!locationManager.isRequestingPermision) {
            this.getRegion()
              .then(this.getConfig)
              .then(() => {
                this.getConfigReview();
                this.getUser();

                SocketOrderManager.destroy();
                SocketOrderManager.reCheck();
              })
          } else {
            this.getConfig()
              .then(() => {
                this.getConfigReview();
                this.getUser();

                SocketOrderManager.destroy();
                SocketOrderManager.reCheck();
              })
          }

          this.handleCodePush();
          dispatch(AppStateActions_MiddleWare.getConfigForTag())
          // dispatch(AppStateActions_MiddleWare.configRoutingShop());
        }, 100);
        break;
      }
      case 'background':{
        SocketOrderManager.destroy();
        break;
      }
      case 'inactive':{
        break;
      }
      default:
    }
  },
  screenList:[],
  defaultLeftButton() {
    return (
      <TouchableOpacity
        style={Themes.current.screen.leftButtonWrapNavBar}
        onPress={()=>{
          this.props.navigation.goBack()
        }}>
            <HeyUIcon name={'fi-sr-arrow-left'} size={24} color={'#012548'}/>
      </TouchableOpacity>
    )
  },
  defaultRightButton() {
    return (
      <View style={Themes.current.screen.rightButtonWrapNavBar}>
        {/* <ButtonWrap onPress={()=>{
          Actions.NotifyScreen({
            type: 'push'
          })
        }}>
          <Icon name='notifications' style={{fontSize: 27, lineHeight: 36, color: '#fff'}} />
          {this.props.notify.list.length > 0 ?
            <View
              pointerEvents={'none'}
              style={{position: 'absolute', top: Platform.OS === 'ios' ? 0 : StatusBar.currentHeight, right: -5, width: 15, height: 15, borderRadius: 7.5, backgroundColor: '#e74c3c', alignItems: 'center', justifyContent: 'center'}}>
                <Include.Text numberOfLines={1} style={{color: '#fff', backgroundColor: 'transparent'}}>{this.props.notify.list.length}</Include.Text>
            </View>
          : null}
        </ButtonWrap> */}
      </View>
    )
  },
  createScreen:function(){
    var self = this;
    self.screenList= screenList.map((current)=>{
      var currentTemp = current;
      if (current.WrappedComponent) {
        currentTemp = current.WrappedComponent;
      }
      if(!currentTemp.renderBackButton) {
        currentTemp.renderBackButton = self.defaultLeftButton;
      }
      if(!currentTemp.renderRightButton) {
        currentTemp.renderRightButton = self.defaultRightButton;
      }

      if (Platform.OS === 'ios') {
        return (
          <Scene
            key={currentTemp.componentName}
            title={currentTemp.componentName}
            component={current}
            renderLeftButton={currentTemp.renderBackButton}
            renderRightButton={currentTemp.renderRightButton}
            {...TransitionPresets.SlideFromRightIOS}
            {...currentTemp.sceneConfig}
            onBack={() => {
              Actions.pop()
            }}
            navigationBarStyle={{
              ...Themes.current.screen.NavBar
            }}
            backButtonTextStyle={{ resizeMode: 'stretch', height: 19, top: -4 }}
            bodyStyle={Themes.current.screen.bodyViewWrap}
            rootView={self}
            headerBackground={() => (
              <View
                style={{ flex: 1, backgroundColor: '#fff', shadowColor: '#d1d1d1', shadowOpacity: 0.5, shadowOffset: {height: 1, width: 0}, shadowRadius: 1 }}
              />
            )}
          />
        )
      }

      return(
        <Scene
          key={currentTemp.componentName}
          title={currentTemp.componentName}
          component={current}
          renderLeftButton={currentTemp.renderBackButton}
          renderRightButton={currentTemp.renderRightButton}
          {...TransitionPresets.SlideFromRightIOS}
          {...currentTemp.sceneConfig}
          onBack={() => {
            Actions.pop()
          }}
          navigationBarStyle = {{
            ...Themes.current.screen.NavBar
          }}
          headerForceInset={{ top: 'never' }}
          backButtonTextStyle = {{resizeMode: 'stretch', height: 19, top: -4}}
          bodyStyle={Themes.current.screen.bodyViewWrap}
          rootView={self}
          headerBackground={() => (
            <View
              style={{ flex: 1, backgroundColor: '#fff', elevation: 1 }}
            />
          )}
        />
      )
    })

    this.AppNavigator = Actions.create(
      <Scene key="root" hideNavBar>
        {self.screenList}
      </Scene>,
    )

    const initialState = this.AppNavigator.router.getStateForAction(this.AppNavigator.router.getActionForPathAndParams('LoginAccountScreen'));
    const navReducer = (state = initialState, action) => {
      const nextState = this.AppNavigator.router.getStateForAction(action, state);
      // Simply return the original `state` if `nextState` is null or undefined.
      return nextState || state;
    };

    injectAsyncReducer('nav', navReducer)

    // this.ReduxNavigator = createReduxContainer(this.AppNavigator, 'root');

    const mapStateToProps = state => ({
      state: state.nav,
    })
    this.RouterWithRedux = connect(mapStateToProps)(Router);
  },

  preProcessWhenStartDone:false,
  preProcessWhenStart : function(){
    Debug.log('preProcessWhenStart');
    var self = this;
    var {dispatch,user}= self.props;

    self.preProcessWhenStartDone = true;


    self.processDeepLinkFromNotify();
    self.processDeepLinkFromWeb();
    self.processDeepLinkFromApp();
  },
  processDeepLinkFromNotifyDone:false,
  startDeepLink:'',
  startExtras:{},
  backFlag:false,
  processDeepLinkFromNotify:function(){
    var self =this;
    // check intent (start from link or notify)
    if (self.processDeepLinkFromNotifyDone) {
      return;
    }

    Debug.log('Process deeplink when start app from notity')
    try {
    } catch (e) {}
    self.processDeepLinkFromNotifyDone = true;
  },
  processDeepLinkFromWebDone:false,
  processDeepLinkFromWeb:function(){
    var self =this;
    if (self.processDeepLinkFromWebDone) {
      return;
    }
    Linking.getInitialURL().then(url => {
      if(url){
        IncomingLinkingManager.init(url)
      }
    })

    this.listenerLinking = Linking.addEventListener('url', (event) => {
      if(event.url){
        IncomingLinkingManager.init(event.url)
      }
    });
    self.processDeepLinkFromWebDone = true;
  },
  processDeepLinkFromAppDone: false,
  processDeepLinkFromApp:
  function() {
    if (this.processDeepLinkFromAppDone) {
      return;
    }
    Linking.getInitialURL()
    .then(url => {
      if(url){
        const data = url.replace("iochongbangvietnam://", "")
        try {
          const obj = JSON.parse(decodeURIComponent(data));
          globalVariableManager.navigatorManager.handleNavigator(obj.link, obj.extras || {});
        } catch (e) {}
      }
    })

    this.listenerLinking = Linking.addEventListener('url', (event) => {
      if(event.url){
        const data = event.url.replace("iochongbangvietnam://", "")
        try {
          const obj = JSON.parse(decodeURIComponent(data))
          globalVariableManager.navigatorManager.handleNavigator(obj.link, obj.extras || {});
        } catch (e) {}
      }
    });
    this.processDeepLinkFromAppDone = true;
  },
  fetchDataFromNotify(notify) {
    const {dispatch, appSetting} = this.props;

    let link, id;
    if (Platform.OS === 'ios') {
      link = _.get(notify, '_data.link', '');
      id = _.get(notify, '_data.extras.id', '');
    } else {
      link = _.get(notify, 'link', '');
      id = _.get(notify, 'extras.id', '');
    }

    // Check has new message
    if(this.props.navigator.currentScreen.name === 'OrderCreatedScreen'
    || this.props.navigator.currentScreen.name === 'OrderSystemScreen'
    || this.props.navigator.currentScreen.name === 'DetailOrderSystemScreen'
    || this.props.navigator.currentScreen.name === 'DetailOrderSystemScreenForShop') {
      let conversation, senderId;
      if (Platform.OS === 'ios') {
        conversation = _.get(notify, '_data.extras.conversation', '');
        senderId = _.get(notify, '_data.extras.senderId', '');
      } else {
        conversation = _.get(notify, 'extras.conversation', '');
        senderId = _.get(notify, 'extras.senderId', '');
      }

      if(conversation && senderId) {
        dispatch(FeedsSystemActions_MiddleWare.getNewOrderMessage({receiverId:[senderId],mode:_.get(appSetting, 'mode', '')}));
      }
    }

    if((link === 'OrderCreatedScreen' && id)
    || (link === 'OrderSystemScreen' && id)) {
      if(link === 'OrderCreatedScreen') {
        dispatch(FeedsSystemActions_MiddleWare.get({id,type: 2, canShowShipperAuthen: 1}));
      } else {
        dispatch(FeedsSystemActions_MiddleWare.get({id, type: 0}));
        // dispatch(FeedsSystemActions_MiddleWare.listInstanceOrder())
      }
    }

    if(link === 'ProfileScreen') {
      dispatch(UserActions_MiddleWare.get());
    }
  },
  getConfigReview() {
    const {appSetting, dispatch} = this.props;

    return dispatch(AppStateActions_MiddleWare.getConfigReview())
  },
  getConfig() {
    const {appSetting, dispatch} = this.props;

    const role = _.get(appSetting, 'mode', '');
    return dispatch(AppStateActions_MiddleWare.getConfig({role}))
  },
  notifyManager(notify, isInitial) {
    if (this.calledNotifyManager) {
      this.calledNotifyManager = false;
      return;
    }
    this.calledNotifyManager = true;

    const {dispatch, appSetting} = this.props;
    this.fetchDataFromNotify(notify);

    if (notify._data.link === 'WebRTC') {
      popupActions.setRenderContentAndShow(WebRTC, {
        roomId: notify._data.extras.roomId,
        userId: notify._data.extras.userId,
        video: notify._data.extras.video,
        cameraType: notify._data.extras.cameraType,
        callFrom: notify._data.extras.callFrom
      });

      setTimeout(() => {
        this.calledNotifyManager = false;
      }, 1000);

      return;
    } else if (notify._data.link === 'NewOrderPopup') {
      if (_.get(appSetting, 'mode', '') === 'shipper') {
        setTimeout(() => {
          popupActions.setRenderContentAndShow(NewOrderPopup, {
            id: notify._data.extras.id
          });
        }, 500);
      }

      return;
    } else if (notify._data.link === 'NewOrderBikePopup') {
      if (_.get(appSetting, 'mode', '') === 'shipper') {
        setTimeout(() => {
          popupActions.setRenderContentAndShow(NewOrderBikePopup, {
            id: notify._data.extras.id
          });
        }, 500);
      }

      return;
    }

    const notiObj = {
      _id: Date.now(),
      notifiedAt: Date.now(),
      title: _.get(notify, '_alert.title', ''),
      description: _.get(notify, '_alert.body', ''),
      icon: _.get(notify, '_data.icon', ''),
      link: _.get(notify, '_data.link', ''),
      extras: _.get(notify, '_data.extras', {}),
    }

    dispatch(RDActions['Notify']['addOnRequest'](notiObj));

    if(!isInitial && AppState.currentState === 'active') {
      // popupActions.setRenderContentAndShow(NotifyPopup, {notiObj})
      if (notiObj.title || notiObj.description) {
        NotifyUtil.pushNotify(notiObj);
      }
    } else {
      setTimeout(() => {
        globalVariableManager.navigatorManager.handleNavigator(notiObj.link, notiObj.extras)
      }, 500);
    }

    setTimeout(() => {
      this.calledNotifyManager = false;
    }, 1000);
  },
  heightKeyboard: 0,
  keyboardWillShow: function(e) {
    this.heightKeyboard = e.endCoordinates.height;
    this.setState({
      keyboardShow: true
    })
  },
  keyboardWillHide: function() {
    this.heightKeyboard = 0;
    this.setState({
      keyboardShow: false
    })
  },
  handleException: function (error, isFatal) {
    const {dispatch, navigator} = this.props;
    dispatch(AppStateActions_MiddleWare.sendCrashLog({
      appState: {
        stackScreen: navigator.stack,
        stackPopup: popupActions.getPopupStack()
      },
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
        fileName: error.fileName,
        lineNumber: error.lineNumber,
        columnNumber: error.columnNumber
      }
    }))

    if(isFatal) {
      this.showToast("Đã có lỗi xảy với ứng dụng. HeyU sẽ khắc phục sớm nhất có thể. Xin cảm ơn", 2000);
    }
  },

  onRegisterNotification: function(token){
    const {dispatch, user} = this.props;
    Define.config.token = token;
    Define.config.waitToken = false;
    const memberToken = _.get(user, 'memberInfo.member.memberToken', '');
    if(memberToken) {
      dispatch(UserActions_MiddleWare.sentNotifyToken({memberToken: memberToken}));
    }
  },

  UNSAFE_componentWillMount : function(){
    var self = this;
    var { dispatch,state,appState, user,appSetting} = this.props;

    globalVariableManager.reduxManager.setDispatchAndState(dispatch,state);
    globalVariableManager.rootView = self;

    if (Platform.OS === 'android') {
      this.keyboardDidShowSubscription = Keyboard.addListener('keyboardDidShow', this.keyboardWillShow)
      this.keyboardDidHideSubscription = Keyboard.addListener('keyboardDidHide', this.keyboardWillHide)
    }else{
      this.keyboardDidShowSubscription = Keyboard.addListener('keyboardWillShow', this.keyboardWillShow)
      this.keyboardDidHideSubscription = Keyboard.addListener('keyboardWillHide', this.keyboardWillHide)
    }

    if(Platform.OS === 'ios') {
      dispatch(AppStateActions_MiddleWare.getApiKeyGoogle())
        .then((result) => {
          UtilNative.setApiKeyGoogleMap(result.res.data.apiKey)
          self.setState({
            hasSetApiGoogleMap: true
          });
        })
        .catch((err) => {
          UtilNative.setApiKeyGoogleMap(Define.constants.apiKeyGoogleMap)
          self.setState({
            hasSetApiGoogleMap: true
          });
        });
    }


    if (!Define.constants.debug) {
      ErrorUtils.setGlobalHandler(this.handleException)
    }

    if(Platform.OS === 'android') {
      //key
      this.back = BackHandler.addEventListener('hardwareBackPress',
         () => {
           appState = self.props.appState;
           var {navigator } = self.props;
           if (self.hideContentState) {
             globalVariableManager.rootView.hideContent(false);
           }
           if (popupActions.popPopup()) {
             return true;
           }
           else if(popupActions.getPopupStackSize(0)>0){
             popupActions.popPopup(0,true,0);
             return true;
           }
           else if (!(appState.currentState === RDActionsTypes.AppState.constants.APP_STATE_LIST.LOADING)) {
             appSetting = globalVariableManager.reduxManager.state.AppSetting
             if (navigator.currentScreen.name !== 'MainContainer') {
              //  if(this.props.navigation.goBack()) {
              //    return true;
              //  }else{
              //     if (!self.backFlag) {
              //       self.backFlag = true;
              //       ToastAndroid.show('Nhấn Back một lần nữa để thoát ứng dụng', ToastAndroid.SHORT);
              //       setTimeout(()=>{
              //         self.backFlag = false;
              //       },2000)
              //     }else{
              //       ToastAndroid.show('Cám ơn bạn đã sử dụng HeyU', ToastAndroid.LONG);
              //       BackHandler.exitApp();
              //     }
              //    return true;
              //  }
             }
             else{
                if (!self.backFlag) {
                  self.backFlag = true;
                  ToastAndroid.show('Nhấn Back một lần nữa để thoát ứng dụng', ToastAndroid.SHORT);
                  setTimeout(()=>{
                    self.backFlag = false;
                  },2000)
                }else{
                  ToastAndroid.show('Cám ơn bạn đã sử dụng HeyU', ToastAndroid.LONG);
                  BackHandler.exitApp();
                }
                  return true;
              }
            }
            else{
                if(Platform.OS === 'android'){
                    ToastAndroid.show('Cám ơn bạn đã sử dụng HeyU', ToastAndroid.SHORT);
                  }
                BackHandler.exitApp();
               return true;
            }
           });
    } else {
      if (this.props.appSetting.openedNativeNotiPopup) {
        PushNotificationIOS.requestPermissions();
        // VoipPushNotification.requestPermissions();
      }

      PushNotificationIOS.setApplicationIconBadgeNumber(0);
      PushNotificationIOS.addEventListener('register', token => {
        Define.config.token = token;
        Define.config.waitToken = false;

        setTimeout(() => {
          const memberToken = _.get(this.props.user, 'memberInfo.memberToken', '');
          dispatch(UserActions_MiddleWare.sentNotifyToken({memberToken: memberToken, notify_token: token}));

        }, 3000);
      });

      PushNotificationIOS
        .getInitialNotification()
        .then((notify) => {
          if(notify) {
            this.notifyManager(notify, true);
          }
        });

      PushNotificationIOS.addEventListener('registrationError', ()=>{});
      PushNotificationIOS.addEventListener('notification', this.notifyManager);
      PushNotificationIOS.addEventListener('localNotification', (notify) => {
        notify._alert = {
          title: 'HeyU',
          body: notify._alert
        }

        this.notifyManager(notify);
      });
    }

    // events
    NetInfo.addEventListener((connectionInfo)=>{
      Debug.log('Connection state change: ' + connectionInfo.type,Debug.level.USER_TRACKER); // NONE , WIFI, MOBILE
      dispatch(RDActions.ServerConnection.changeNetInfoOnRequest(connectionInfo.type));
      if ((!connectionInfo.isInternetReachable && Platform.OS === 'android') || (!connectionInfo.isConnected && Platform.OS === 'ios')) {
        NotifyUtil.pushAlertTopNotify({
          type: 'warning',
          content: 'Lỗi kết nối mạng, vui lòng kiểm tra lại. Xin cảm ơn',
          timeClose: 3000,
        })
      }
    })

    this.appStateSubscription = AppState.addEventListener('change', self.handleAppStateChange);

    // self.createScreen();
  },

  componentWillUnmount : function() {
    this.appStateSubscription.remove();
    if(Platform.OS === 'android') {
      this.messageListener();
      this.onNotificationOpenedListener();
      if (this.back) {
        this.back.remove()
      }
    }
    this.keyboardDidShowSubscription.remove()
    this.keyboardDidHideSubscription.remove()
    popupActions.popAllPopup();
  },

  renderFormAlert: function(title, content, buttonTitle, onPress) {
    return (
      <Animatable.View style={{alignItems: 'center', justifyContent: 'center', flex: 1, backgroundColor: '#226FB7'}}>
        <View style={{backgroundColor: '#fff', borderRadius: 4, margin: 15, paddingTop: 20}}>
          <View>
            <View style={[Themes.current.popup.titleWrap]}>
              <Text allowFontScaling={false} style={Themes.current.text.popupTitle}>{title}</Text>
            </View>
            <View style={{width: '100%', borderBottomWidth: 1, borderBottomColor: '#DCDEE1', padding: 10}}>
              <Text allowFontScaling={false} style={{left: 0, right: 0, color: '#000', margin: 5, alignSelf: 'center', textAlign: 'center'}}>{content}</Text>
            </View>
          </View>
          <TouchableOpacity
            onPress={onPress}
            style={{backgroundColor: '#fff', justifyContent: 'center', height: 45, borderRadius: 4,}}
          >
            <Text allowFontScaling={false} style={{alignSelf: 'center', marginLeft: 0, fontSize: 14, fontWeight: 'bold', color: '#1697B4'}}>{buttonTitle}</Text>
          </TouchableOpacity>
        </View>
      </Animatable.View>
    )
  },
  renderContentAtLoading: function() {
    const {appState, dispatch} = this.props;
    let content = (
      <View style={{ flex: 1, backgroundColor: '#015CBC', paddingTop: Platform.OS === 'ios' ? 0 : 0 }}>
        <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
          <Image
            source={Define.assets.Images.splashscreen}
            style={{ height: '100%', width: Define.constants.widthScreen}}
          />
        </View>
      </View>
    );
    if(appState.config.forceUpdate.status) {
      content = this.renderFormAlert('CẬP NHẬT PHIÊN BẢN MỚI', appState.config.forceUpdate.message, 'CẬP NHẬT', () => {
        if (Platform.OS === 'android') {
          var url = appState.config.forceUpdate.url || 'https://play.google.com/store/apps/details?id=com.iheyu.pro';
            Linking.canOpenURL(url).then(supported => {
              if (!supported) {
                // console.log('Can\'t handle url: ' + url);
              } else {
                return Linking.openURL(url);
              }
            }).catch(err => console.error('An error occurred', err));
        } else {
          Linking.openURL(appState.config.forceUpdate.url || 'https://itunes.apple.com/vn/app/săn-ship/id1179627110?mt=8')
        }
      })
    } else if(appState.config.undermaintain.status) {
      content = this.renderFormAlert('THÔNG BÁO BẢO TRÌ', appState.config.undermaintain.message, 'THỬ LẠI', () => {
             this.getConfig();
      })
    } else if(appState.config.networkError) {
      content = this.renderFormAlert('LỖI MẠNG', 'Vui lòng kiểm tra lại kết nối mạng trước khi thử lại', 'THỬ LẠI', () => {
             this.getConfig();
      })
    } else if(appState.config.systemError) {
      content = this.renderFormAlert('LỖI', 'Hệ thống đang gặp sự cố vui lòng thử lại', 'THỬ LẠI', () => {
             this.getConfig();
      })
    }
    return content;
  },
  renderKeyboardDismiss: function() {
    let content = null;
    if(_.get(this.props,'navigator.currentScreen.name','') === 'ChatScreen') {
      return null
    }
    // if(this.state.keyboardShow) {
    //   content = (
    //     <TouchableOpacity
    //       style={{position: 'absolute', bottom: Platform.OS === 'ios' ? this.heightKeyboard : 0, right: 0, zIndex: 100, flexDirection: 'row', alignItems: 'center', justifyContent:'center', borderTopLeftRadius: 5, backgroundColor: '#c9ccd2', paddingTop:2}}
    //       onPress={() => {
    //         Keyboard.dismiss()
    //       }}>
    //       <View style={{justifyContent:'center', alignItems:'center', marginLeft: 10,}}>
    //         <Image
    //           source={{ uri: 'https://media.heyu.asia/uploads/mobiles/2025-02-13-keyboard1.png' }}
    //           style={{ width: 24, height: 20 }}
    //           tintColor={'#b30000'}
    //         />
    //         <PenguinBoldIcon name='arrow-down' style={{ backgroundColor: 'transparent', marginTop: -6}} color={'#b30000'} size={14}/>
    //       </View>
    //       <Text allowFontScaling={false} style={{ fontSize: 16, fontFamily: Define.constants.fontBold600,  paddingHorizontal: 10, color: '#b30000'}}>Đóng</Text>
    //     </TouchableOpacity>
    //   )
    // }

    return content;
  },
  render:function(){
    var self= this;
    const { dispatch,state,appState, navigator, notify, user} = this.props;
    var content;
    if (appState.currentState === RDActionsTypes.AppState.constants.APP_STATE_LIST.LOADING) {
      return this.renderContentAtLoading();
    } else if (self.requireFaceIDLogin && self.biometricKeysExist && !_.isEqual(user.memberInfo, {}) && user.memberInfo?._id !== '') {
      content = (
        <Stack.Navigator
          initialRouteName="LoginFaceIDScreen"
          screenOptions={{ headerShown: false }}>
          {screenList && screenList.length && screenList.map((screen, index) => {
            var currentTemp = screen;
            if (screen.WrappedComponent) {
              currentTemp = screen.WrappedComponent;
            }
            return (
              <Stack.Screen
                key={currentTemp.componentName}
                title={currentTemp.componentName}
                component={screen}
                renderBackButton={currentTemp.renderBackButton}
                renderRightButton={currentTemp.renderRightButton}
                {...TransitionPresets.SlideFromRightIOS}
                {...currentTemp.sceneConfig}
                onBack={() => {
                  this.props.navigation.goBack()
                }}
                navigationBarStyle={{
                  backgroundColor: '#01cca1',
                  ...currentTemp.sceneConfig.navigationBarStyle
                }}
                backButtonTextStyle={{ resizeMode: 'stretch', height: 19, top: -4 }}
                bodyStyle={Themes.current.screen.bodyViewWrap}
                rootView={self}
                headerBackground={() => (
                  <View
                    style={{ flex: 1, backgroundColor: '#fff', shadowColor: '#d1d1d1', shadowOpacity: 0.5, shadowOffset: { height: 1, width: 0 }, shadowRadius: 1 }}
                  />
                )}
                name={screen.componentName}
              />
            )
          })}
        </Stack.Navigator>
      )
    } else if (!self.requireFaceIDLogin && !self.biometricKeysExist && !_.isEqual(user.memberInfo, {}) && user.memberInfo?._id !== '') {
      content = (
        <Stack.Navigator
          initialRouteName="MainContainer"
          screenOptions={{ headerShown: false }}>
          {screenList && screenList.length && screenList.map((screen, index) => {
            var currentTemp = screen;
            if (screen.WrappedComponent) {
              currentTemp = screen.WrappedComponent;
            }
            if (!currentTemp.renderBackButton) {
              currentTemp.renderBackButton = self.defaultLeftButton;
            }
            if (!currentTemp.renderRightButton) {
              currentTemp.renderRightButton = self.defaultRightButton;
            }
            return (
              <Stack.Screen
                key={currentTemp.componentName}
                title={currentTemp.componentName}
                component={screen}
                renderBackButton={currentTemp.renderBackButton}
                renderRightButton={currentTemp.renderRightButton}
                {...TransitionPresets.SlideFromRightIOS}
                {...currentTemp.sceneConfig}
                onBack={() => {
                  this.props.navigation.goBack()
                }}
                navigationBarStyle={{
                  backgroundColor: '#01cca1',
                  ...currentTemp.sceneConfig.navigationBarStyle
                }}
                backButtonTextStyle={{ resizeMode: 'stretch', height: 19, top: -4 }}
                bodyStyle={Themes.current.screen.bodyViewWrap}
                rootView={self}
                headerBackground={() => (
                  <View
                    style={{ flex: 1, backgroundColor: '#fff', shadowColor: '#d1d1d1', shadowOpacity: 0.5, shadowOffset: { height: 1, width: 0 }, shadowRadius: 1 }}
                  />
                )}
                name={screen.componentName}
              />
            )
          })}
        </Stack.Navigator>
      )
    } else {
      content = (
        <Stack.Navigator
          initialRouteName="LoginAccountScreen"
          screenOptions={{ headerShown: false }}>
          {screenList && screenList.length && screenList.map((screen, index) => {
            var currentTemp = screen;
            if (screen.WrappedComponent) {
              currentTemp = screen.WrappedComponent;
            }
            return (
              <Stack.Screen
                key={currentTemp.componentName}
                title={currentTemp.componentName}
                component={screen}
                renderBackButton={currentTemp.renderBackButton}
                renderRightButton={currentTemp.renderRightButton}
                {...TransitionPresets.SlideFromRightIOS}
                {...currentTemp.sceneConfig}
                onBack={() => {
                  this.props.navigation.goBack()
                }}
                navigationBarStyle={{
                  backgroundColor: '#01cca1',
                  ...currentTemp.sceneConfig.navigationBarStyle
                }}
                backButtonTextStyle={{ resizeMode: 'stretch', height: 19, top: -4 }}
                bodyStyle={Themes.current.screen.bodyViewWrap}
                rootView={self}
                headerBackground={() => (
                  <View
                    style={{ flex: 1, backgroundColor: '#fff', shadowColor: '#d1d1d1', shadowOpacity: 0.5, shadowOffset: { height: 1, width: 0 }, shadowRadius: 1 }}
                  />
                )}
                name={screen.componentName}
              />
            )
          })}
        </Stack.Navigator>
      )
    }
    return(
      <View renderToHardwareTextureAndroid={false} style={Themes.current.screen.appBackground}>
        <Animatable.View
          onStartShouldSetResponderCapture={()=>{
            if (self.hideContentState) {
              globalVariableManager.rootView.hideContent(false);
              return true;
            }
            return false;
          }}
          ref={self.refAnimatable} style={{flex:1}}>
          {content}
        </Animatable.View>
        <View
          pointerEvents={'box-none'}
          style={{position:'absolute', top: 0, bottom: 0, left: 0, right: 0}}
        >
          <SafeAreaView style={{flex: 0, backgroundColor: appState.statusBarColor}} />
          <View pointerEvents={'box-none'} style={{flex: 1, backgroundColor: 'transparent', alignItems: 'center', justifyContent: 'center'}}>
            <PopupManager rootView={self}/>
          </View>
          {!this.state.keyboardShow ? <SafeAreaView style={{flex: 0, backgroundColor: appState.footerColor}} /> : null}
        </View>

        {appState.showLoading ?
          <View style={{position: 'absolute', alignItems:'center', justifyContent:'center', top: 0, left: 0, right: 0, bottom: 0, alignItems: 'center', justifyContent: 'center', backgroundColor:'rgba(0,0,0,0.4)'}}>
            <View style={{backgroundColor:'#fff', width:70, height:70, borderRadius:35, alignItems:'center', justifyContent:'center', elevation:2}}>
              <LottieView
                source={require('../../assets/Animation/animationLoading')} autoPlay loop
                style={{
                  width: 70,
                  height: 70,
                }}/>
              <Image
                source={Define.assets.Images.logocongan}
                resizeMode={'stretch'}
                style={{width: 30, height: 30, position:'absolute', borderRadius: 32}}
              />
            </View>

          </View>
        :null}

        <Toast textStyle={{textAlign: 'center', color: '#fff'}} ref={self.refToast}/>

        {this.renderKeyboardDismiss()}
      </View>
    )
  },
  UNSAFE_componentWillReceiveProps: function (nextProps) {
  },
  getDeviceInfo: function(){
    DeviceInfo.getUniqueId().then((uniqueId) => {
      Define.constants.deviceId = uniqueId
    });

    DeviceInfo.getUserAgent().then((userAgent) => {
      Define.constants.userAgent = userAgent
    });
    DeviceInfo.getDeviceName().then((deviceName) => {
      Define.constants.deviceName = deviceName
    });
    DeviceInfo.getManufacturer().then((manufacturer) => {
      Define.constants.deviceManufacturer = manufacturer
    });
  },
  checkAndRequestPermissionNotify(){
    return new Promise((resolve,reject)=>{
      if (Platform.OS === 'android' && Platform.Version >= 33) {
        PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS)
        .then((ret)=>{
          if (ret) {
            return Promise.resolve(true);
          }else{
            return PermissionsAndroid.request(
              PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS
            )
          }
        })
        .then((granted)=>{
          if (granted) {
            resolve()
          }else{
            reject()
          }
        })
      }else{
        resolve()
      }
    })
  },

  codePushDownloadDidProgress(progress) {
    if (progress.receivedBytes === progress.totalBytes) {
      if (this.isMandatory) {
        Keyboard.dismiss();

        popupActions.setRenderContentAndShow(HotUpdatePopup, {
          title: 'Cập nhật ứng dụng',
          description: `Bản cập nhật mới cải tiến sửa lỗi ứng dụng. Bạn vui lòng bấm "Cập nhật" để được cập nhật ứng dụng mới nhất. Xin cảm ơn.`,
          buttonTitle: 'Cập nhật',
          onPress: () => {
            popupActions.popPopup();

            CodePush.restartApp();
          }
        })
      }
    } else {
      const phones = _.get(globalVariableManager, 'reduxManager.state.User.memberInfo.phones', '');

      if (['0966717874', '0944503017', '0972869272', '0559161196', '0988888888', '0975938998', '0357948523', '0966160607', '0986869779'].includes(phones[0])) {
        globalVariableManager.rootView.showToast(`${Math.round(progress.receivedBytes / progress.totalBytes * 100)}%`)
      }
    }
  },

  handleCodePush() {
    const { navigator } = this.props;
    const listUpdateScreen = ['Trang chủ']
    if(navigator.currentScreen && navigator.currentScreen.name) {
      CodePush.checkForUpdate()
      .then((update) => {
        if (update) {
          this.isMandatory = update.isMandatory;
          if (update.isMandatory || (!update.isMandatory && navigator.currentScreen.name !== 'PetitionCreateScreen')) {
            CodePush.sync({ installMode: CodePush.InstallMode.ON_NEXT_RESUME, mandatoryInstallMode: CodePush.InstallMode.ON_NEXT_RESUME, rollbackRetryOptions: { delayInHours: 1, maxRetryAttempts: 5 } },
              null,
              this.codePushDownloadDidProgress
            );
          }
        }
      })
    }
  },

  componentDidMount: async function() {
    var self = this;
    var {dispatch, appSetting, user} = self.props;
    if (!_.isEqual(user.memberInfo, {}) && user.memberInfo?._id !== '') {
      await self.checkBiometricKeys();
      if (self.biometricKeysExist) {
        self.requireFaceIDLogin = true;
      }
    }
    this.getDeviceInfo();
    this.getUser();
    this.handleCodePush();
    if (appSetting && !appSetting.updatedLocation) {
      dispatch(RDActions.AppSetting.changeUpdatedLocation());

      if (appSetting?.senderInfo?.senderAddr?.nameMain && appSetting?.senderInfo?.senderAddr?.nameSecondary && appSetting?.senderInfo?.senderAddr?.name && !appSetting?.senderInfo?.senderAddr?.name.includes(appSetting?.senderInfo?.senderAddr?.nameMain)) {
        let senderInfo = appSetting?.senderInfo;
        delete senderInfo?.senderAddr?.nameMain
        delete senderInfo?.senderAddr?.nameSecondary

        dispatch(RDActions.AppSetting.addSenderInf(senderInfo));
      }

      if (appSetting?.recentlyOrigin?.length) {
        appSetting.recentlyOrigin.map((recentlyOrigin, index) => {
          if (recentlyOrigin.nameMain && recentlyOrigin.name && !recentlyOrigin.name.includes(recentlyOrigin.nameMain)) {
            let recentlyLocation = recentlyOrigin;
            delete recentlyLocation.nameMain
            delete recentlyLocation.nameSecondary

            dispatch(RDActions.AppSetting.addRecentlyOriginPick(recentlyLocation));
          } else {
            dispatch(RDActions.AppSetting.addRecentlyOriginPick(recentlyOrigin));
          }
        })
      }

      if (appSetting?.recentlyLocation?.length) {
        appSetting.recentlyLocation.map((recentlyLocation, index) => {
          if (recentlyLocation.nameMain && recentlyLocation.name && !recentlyLocation.name.includes(recentlyLocation.nameMain)) {
            let recentlyLocationNew = recentlyLocation;
            delete recentlyLocation.nameMain
            delete recentlyLocation.nameSecondary

            dispatch(RDActions.AppSetting.addRecentlyLocationPick(recentlyLocationNew));
          } else {
            dispatch(RDActions.AppSetting.addRecentlyOriginPick(recentlyLocation));
          }
        })
      }
    }

    if(Platform.OS === 'ios') {
      this
        .getRegion()
        .then(this.getConfig)
        .then(this.getConfigReview)

      this.notifeeSubscription = notifee.onForegroundEvent(({ type, detail }) => {
        const notification = detail?.notification;
        notification._data = notification.data;
        notification._alert = {
          title: notification.title,
          body: notification.body
        }

        this.notifyManager(notification, true);
      });
    } else {
      this.checkAndRequestPermissionNotify()
      .then(() => {
        messaging().getToken()
        .then((token) => {
          Define.config.token = token;
          const memberToken = _.get(user, 'memberInfo.memberToken', '');
          dispatch(UserActions_MiddleWare.sentNotifyToken({memberToken: memberToken, notify_token: token}))
        })
      })
      this.messageListener = messaging().onMessage(async remoteMessage => {

        var notifyFormat={
          _id:'',
          title:'',
          bigImgage:'',
          description:'',
          link:'',
          extras:{},
          createAt:0,
          expiredAt:0,
          notifiedAt: 0
        }
        if (remoteMessage && remoteMessage.data && remoteMessage.data.title) {
          try{
            remoteMessage.data.extras = JSON.parse(remoteMessage.data.extras);
          }catch(ex){};
          remoteMessage.data.notifiedAt = Date.now()
          var temp = Util.dataProtectAndMap(remoteMessage.data, notifyFormat);

          handleNotify(remoteMessage)
          dispatch(RDActions['Notify']['addOnRequest'](temp));

          self.fetchDataFromNotify(remoteMessage.data);
        }
      });

      this.onNotificationOpenedListener = messaging().onNotificationOpenedApp(remoteMessage => {
         if (remoteMessage) {
           if (remoteMessage?.data) {
             const notification = remoteMessage?.data
             try{
               notification.extras = JSON.parse(notification.extras);
             }catch(ex){};
             if(notification && notification.link) {
                globalVariableManager.navigatorManager.handleNavigator(notification.link,notification.extras);
             }
           }
         }
      });

       // Check whether an initial notification is available
       messaging()
         .getInitialNotification()
         .then(remoteMessage => {
           if (remoteMessage) {
             if (remoteMessage?.data) {
               const notification = remoteMessage?.data
               try{
                 notification.extras = JSON.parse(notification.extras);
               }catch(ex){};
               if(notification && notification.link) {
                 setTimeout(() => {
                   globalVariableManager.navigatorManager.handleNavigator(notification.link,notification.extras);
                 },1000)
               }
             }
           }
         });

      this
        .getRegion()
      this.getConfig()
        .then(this.getConfigReview)
    }

    if(this.state.wakeByLocation) {
      BackgroundLocationManager.start();
    }
    dispatch(AppStateActions_MiddleWare.getConfigForTag())
    // dispatch(AppStateActions_MiddleWare.configRoutingShop());
    setTimeout(() => {
      RNBootSplash.hide();
      // SplashScreen.hide();
    }, Platform.OS === 'android' ? 500 : 1000)
  },
  componentDidCatch: function(e) {
    if (!Define.constants.debug && this.state.openError === false) {
      this.setState({ openError: true } , () => {
        Alert.alert(
          'Rất xin lỗi',
          'Đã có lỗi xảy ra trong quá trình hoạt động của ứng dụng. Chúng tôi sẽ khắc phục sớm nhất có thể. Ứng dụng cần được khởi động lại để có thể hoạt động. Xin cảm ơn.',
          [
              {
                  text: "Khởi động lại",
                  onPress: () => {
                    CodePush.restartApp();
                  },
              },
          ],
          { cancelable: false }
        );

        this.handleException(e, true);
      })
    }
  }
})
/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  return {
    appState:state.AppState,
    appSetting:state.AppSetting,
    navigator:state.Navigator,
    serverConnection:state.ServerConnection,
    user: state.User,
    notify: state.Notify,
    notifications: state.Notifications
  }
}

function handleNotify(remoteMessage) {
  let channelId = ''
  let sound = 'default'
  if(!remoteMessage || !remoteMessage.data || !remoteMessage.data.title) {
    return;
  }
  if(remoteMessage && remoteMessage.data && remoteMessage.data.sound) {
    sound = remoteMessage.data.sound
  }
  channelId = `iochongbang_notification_${sound}`
  makeLocalNotify(remoteMessage, channelId, sound)
}

function makeLocalNotify (remoteMessage, channelId, sound) {
  let style = {
    type: AndroidStyle.BIGTEXT, text: remoteMessage?.data?.description
  }
  if(remoteMessage?.data?.image) {
    style = {
      type: AndroidStyle.BIGPICTURE, picture: remoteMessage?.data?.image
    }
  }
  remoteMessage.data.sentTime = Date.now();
  notifee.displayNotification({
    title: remoteMessage?.data?.title,
    body: remoteMessage?.data?.description,
    data: remoteMessage?.data,
    android: {
      channelId,
      sound: sound,
      style,
      pressAction: {
        id: 'default',
        launchActivity: 'default'
      },
    },
  })
}


module.exports = connect(selectActions)(App);
