var _ = require('lodash')
var RDActionsTypes = require('./RDActionsTypes');
import RDActions_MiddleWare from './RDActions_MiddleWare'
// LIB
// components
var Define = require('../Define');
var Debug = require('../Util/Debug');
var Util = require('../Util/Util');
var RDActions = require('./RDActions');
// NOTE : stuck when call getState (when dispatch another action in a action)
/*
 * action creators
 */
class BikeActions_MiddleWare extends RDActions_MiddleWare {
  constructor() {
    super('BikeActions_MiddleWare', true);
    this.init();
  }
  actionsList = {
    modify: {
      query: '/bike/modify',
      serverAddr: Define.constants.serverBikeAddr,
      argFormat: {},
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      showLoading: false,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getDistanceMoney: {
      query: '/bike/get-distance-money',
      serverAddr: Define.constants.serverBikeAddr,
      argFormat: {},
      argMap: {},
      apiVersion: 2,
      limitProcess: 1,
      showLoading: false,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    calculateMoneyFromDistance: {
      query: '/bike/calculate-money-from-distance',
      serverAddr: Define.constants.serverBikeAddr,
      argFormat: {},
      argMap: {},
      apiVersion: 2,
      limitProcess: 1,
      showLoading: false,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    create: {
      query: '/bike/create',
      serverAddr: Define.constants.serverBikeAddr,
      argFormat:{},
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      showLoading: false,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    createNonDes:{
      query: '/bike-non-des/create',
      serverAddr: Define.constants.serverBikeAddr,
      argFormat:{},
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      showLoading: false,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    createForRider:{
      query: '/bike-non-des/create-for-rider',
      serverAddr: Define.constants.serverBikeAddr,
      argFormat:{},
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      showLoading: false,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getOrderType: {
      query: '/order-type/list',
      serverAddr: Define.constants.serverBikeAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      apiVersion: 1,
      limitProcess: 1,
      showLoading: false,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getOrderBike: {
      query: '/bike/get',
      serverAddr: Define.constants.serverBikeAddr,
      argFormat: {},
      argMap: {},
      apiVersion: '1.0',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    takeOrderBike: {
      query: '/bike/take',
      serverAddr: Define.constants.serverBikeAddr,
      argFormat: {
        id: ''
      },
      argMap: {},
      showLoading: true,
      apiVersion: '1.0',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listBikeForRider: {
      query: '/bike/list-for-rider',
      serverAddr: Define.constants.serverBikeAddr,
      argFormat: {
        skip: '',
        query: {}
      },
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: '1.0',
      limitProcess: 1,
      onArg: (arg,getState) => {
        return {
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', ''),
          ...arg
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listBikeForCustomer: {
      query: '/bike/list-for-customer',
      serverAddr: Define.constants.serverBikeAddr,
      argFormat: {
        skip: '',
        query: {}
      },
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: '1.0',
      limitProcess: 1,
      onArg: (arg,getState) => {
        return {
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', ''),
          ...arg
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    retryBike: {
      query: '/bike/retry',
      serverAddr: Define.constants.serverBikeAddr,
      argFormat: {},
      argMap: {},
      showLoading: true,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg,getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    retryBikeNonDes: {
      query: '/bike-non-des/retry',
      serverAddr: Define.constants.serverBikeAddr,
      argFormat: {},
      argMap: {},
      showLoading: true,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg,getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    updateStatusBike: {
      query: '/bike/update-status',
      serverAddr: Define.constants.serverBikeAddr,
      argFormat: {
        id: ''
      },
      argMap: {},
      showLoading: true,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg,getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    updateStatusCar: {
      query: '/bike/update-status-car',
      serverAddr: Define.constants.serverBikeAddr,
      argFormat: {
        id: ''
      },
      argMap: {},
      showLoading: true,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg,getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getTimeWait:{
      query: '/bike/get-time-wait',
      serverAddr: Define.constants.serverBikeAddr,
      argFormat: {
        id: ''
      },
      argMap: {},
      showLoading: true,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg,getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    rejectBikeForCustomer: {
      query: '/bike/reject-for-customer',
      serverAddr: Define.constants.serverBikeAddr,
      argFormat: {},
      argMap: {},
      showLoading: true,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg,getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    rejectPushOrderBike: {
      query: '/bike/reject-push-order',
      serverAddr: Define.constants.serverBikeAddr,
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg,getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    rejectBikeForRider: {
      query: '/bike/reject-for-rider',
      serverAddr: Define.constants.serverBikeAddr,
      argFormat: {},
      argMap: {},
      showLoading: true,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg,getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getChangedOrdersBikeForRider: {
      query: '/bike/get-changed-orders-for-rider',
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: 1,
      serverAddr: Define.constants.serverBikeAddr,
      limitProcess: 10,
      onArg: (arg, getState) => {
        return {
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', ''),
          ...arg
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getChangedOrdersBikeForCustomer: {
      query: '/bike/get-changed-orders-for-customer',
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: 1,
      serverAddr: Define.constants.serverBikeAddr,
      limitProcess: 2,
      onArg: (arg, getState) => {
        return {
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', ''),
          ...arg
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    rating: {
      query: '/member/rating-bike',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverRatingAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    listRatingReason: {
      query: '/member/list-rating-bike-reason',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverRatingAddr,
      showLoading: false,
      apiVersion: '3.0',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    trackCallRider: {
      query: '/bike/tracking-call-rider',
      serverAddr: Define.constants.serverBikeAddr,
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    trackCallCustomer: {
      query: '/bike/tracking-call-customer',
      serverAddr: Define.constants.serverBikeAddr,
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    trackMessageRider: {
      query: '/bike/tracking-message-rider',
      serverAddr: Define.constants.serverBikeAddr,
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    trackMessageCustomer: {
      query: '/bike/tracking-message-customer',
      serverAddr: Define.constants.serverBikeAddr,
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: 1,
      limitProcess:1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listOrderSearching: {
      query:'/bike/list-order-searching',
      serverAddr:Define.constants.serverBikeAddr,
      argFormat:{},
      argMap:{},
      apiVersion:1,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getPromote: {
      query: '/promote-bike/get',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverPromoteAddr,
      showLoading: false,
      apiVersion: '2.0',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          newVersion: 1,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getPromoteCodeById: {
      query: '/promote-bike/get-code-by-id',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverPromoteAddr,
      showLoading: false,
      apiVersion: Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    listPromote: {
      query: '/promote-bike/list',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverPromoteAddr,
      showLoading: false,
      apiVersion: '3.0',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    countPromote: {
      query: '/promote-bike/count',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverPromoteAddr,
      showLoading: false,
      apiVersion: Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getTimeWait: {
      query: '/bike/get-time-wait',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverBikeAddr,
      showLoading: false,
      apiVersion: '1.0',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getMoneyWaitStrategy: {
      query: '/bike/get-money-wait-strategy',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverBikeAddr,
      showLoading: false,
      apiVersion: '1.0',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getConfigTwoWay: {
      query: '/bike/config-two-way',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverBikeAddr,
      showLoading: false,
      apiVersion: '1.0',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getConfigNonDes: {
      query: '/bike-non-des/get-config',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverBikeAddr,
      showLoading: false,
      apiVersion: '1.0',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    updateStatusCarNonDes: {
      query: '/bike-non-des/update-status-car',
      serverAddr: Define.constants.serverBikeAddr,
      argFormat: {
        id: ''
      },
      argMap: {},
      showLoading: true,
      apiVersion: '1.0',
      limitProcess: 1,
      onArg: (arg,getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getConfigAnimateMarker: {
      query: '/bike/config-animate-marker',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverBikeAddr,
      showLoading: false,
      apiVersion: '1.0',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getMoneyStrategy: {
      query: '/bike/get-money-strategy',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverBikeAddr,
      timeout: 3000,
      showLoading: false,
      apiVersion: '1.0',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    updateRoutes: {
      query: '/bike-non-des/update-routes',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverBikeAddr,
      showLoading: false,
      apiVersion: '1.0',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getRoutes: {
      query: '/bike-non-des/get-routes',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverBikeAddr,
      showLoading: false,
      apiVersion: '1.0',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    addProcessing: {
      query: '/bike/add-processing',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverBikeAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    tipRealTimeBike: {
      query: '/bike/tip-realtime',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverBikeAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getConfigReject: {
      query: '/bike/config-reject',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverBikeAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
  }
}
module.exports = new BikeActions_MiddleWare();
