
var _ = require('lodash')
var RDActionsTypes = require('./RDActionsTypes');
import RDActions_MiddleWare from './RDActions_MiddleWare'
import {Actions} from 'react-native-router-flux'

import {
  Platform
} from 'react-native';
// LIB
var axios = require('axios');
var DeviceInfo = require('react-native-device-info');
// components
var Define = require('../Define');
var Debug = require('../Util/Debug');
var Util = require('../Util/Util');
var RDActions = require('./RDActions');
var {globalVariableManager}= require('../components/modules/GlobalVariableManager');

// NOTE : stuck when call getState (when dispatch another action in a action)

/*
 * action creators
 */

class PackageActions_MiddleWare extends RDActions_MiddleWare {
  constructor(){
    super('PackageActions_MiddleWare',true);
    this.init();
  }
  actionsList={
    list: {
      query:'/package/list',
      argFormat:{
      },
      argMap:{},
      limitProcess:1,
      apiVersion:Define.constants.apiVersion,
      onArg:(arg,getState)=>{
        return {
          ...arg,
          memberToken:_.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    }
  }
}


module.exports= new PackageActions_MiddleWare();
