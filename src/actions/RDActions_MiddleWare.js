// @flow
 var _ = require('lodash')
import axios from 'axios';
import { Platform } from 'react-native';
var RDActionsTypes = require('./RDActionsTypes');
// LIB
import { navigationRef } from '../../RootNavigation';

// components
var Define = require('../Define');
var Debug = require('../Util/Debug');
var Util = require('../Util/Util');
var NotifyUtil = require('../Util/notify');

var RDActions = require('./RDActions');

// NOTE : stuck when call getState (when dispatch another action in a action)

/*
 * action creators
 */

class RDActions_MiddleWare {
  name=''
  sortName=''
  logRes=true
  actionsList={}
  constructor(name,logRes){
    this.name = name;
    this.sortName = name.slice(0,name.indexOf('Actions'));
    this.logRes = logRes;
  }

  init(){
    var self = this;
    Object.keys(self.actionsList).forEach((key)=>{
      var obj = self.actionsList[key];
      obj.serverAddr = obj.serverAddr || Define.constants.serverAddr;
      // CREATE MIDDLEWARE FUNCTION
      self[key] = function(arg={},setState = true) {
        var actionName = key;
        var query = obj.query;
        var argTemp = Util.dataProtectAndMap(arg, obj.argFormat);

        var preTextLog = self.name+':'+actionName+':';
        return (dispatch, getState) => {
          //
          var retFromPreProcess;
          if (obj.preProcess) {
            retFromPreProcess = obj.preProcess(argTemp,getState);
          }
          if (retFromPreProcess) {
            return retFromPreProcess;
          }
          var req = {};
          // req config
          if (obj.onArg) {
            req = obj.onArg(argTemp,getState);
          }else{
            req=argTemp;
          }

          if(typeof req.forceShowLoading === 'boolean') {
            obj.showLoading = req.forceShowLoading;
            delete req.forceShowLoading;
          }

          var extras = {};
          if (obj.onExtras) {
            extras = obj.onExtras(arg, getState);
          }

          req.regionName = _.get(getState(),'AppSetting.regionNew', '');
          req.modeApp = _.get(getState(),'AppSetting.mode', '');
          req.platform = Platform.OS;
          req.nativeVersion = Define.constants.nativeVersion;
          req.versionCodePush = Define.constants.versionCodePush;
          req.tShoot = Date.now();
          req.appName = Define.constants.appName

          if (_.get(getState(), 'User.memberInfo.memberToken', '')) {
            req.memberToken = _.get(getState(), 'User.memberInfo.memberToken', '');
          }
          if (_.get(getState(), 'SBHStore.storeInf._id', '') && obj.serverAddr === Define.constants.serverStoreAddr) {
            req.myStoreId = _.get(getState(), 'SBHStore.storeInf._id', '')
          }
          Debug.log(preTextLog+':'+query+':'+JSON.stringify(req));
          var promise = new Promise((resolve,reject)=>{
            var data ={
              extras,
              arg: argTemp
            };
            // check limitProcess
            if ( obj.limitProcess !==0 && getState()[this.sortName] && getState()[this.sortName][actionName]  &&
              getState()[this.sortName][actionName].loading >= obj.limitProcess) {
              Debug.log(preTextLog+':reach limitProcess:' + getState()[this.sortName][actionName].loading);
              data={
                arg:argTemp,
                extras,
                query:query,
                err:'react limitProcess',
              }
              reject(data)
              return;
            }

              if(setState) {dispatch(RDActions[this.sortName][actionName+'OnRequest'](data)); }
              if(obj.showLoading) {dispatch(RDActions['AppState']['showLoadingOnRequest']({show:true}))}
              var methode=()=>{};
              var methodeText='get';
              if (obj.methode === 'get') {
                methode = axios.get;
                methodeText='get';
              }else{
                methode = axios.post
                methodeText='post';
              }

              let preLinkApi = '';
              // let apiVersion = obj.apiVersion ? obj.apiVersion : Define.constants.apiVersion;
              let apiVersion = obj.apiVersion;
              switch (apiVersion) {
                case 1:{
                  preLinkApi = '/api/v1.0';
                  break;
                }
                case 2:{
                  preLinkApi = '/api/v2.0';
                  break;
                }
                default:{
                  if (typeof(apiVersion) === 'string') {
                    preLinkApi = '/api/v'+apiVersion;
                  }else{
                    preLinkApi = '/api/v1.0';
                  }

                }
              }

              let serverAddrKey = obj.serverAddr;
              let serverAddr = Define.servers[serverAddrKey] || serverAddrKey;

              let fullRequestUrl = `${serverAddr}${preLinkApi}${query}`;
              if(req.serverAddr) {
                fullRequestUrl = req.serverAddr;
                delete req.serverAddr
              }
              let source = axios.CancelToken.source();
              if(Platform.OS === 'android') {
                setTimeout(() => {
                  source.cancel('API Request timeout');
                }, obj.timeout || 10000);
              }

              methode(fullRequestUrl, req,
                {
                  headers: {
                    'x-access-token' : Define.constants.serverApiToken ,
                    ...obj.headers,
                    goal: Util.getGoal(`${preLinkApi}${query}`,JSON.stringify(req)),
                    token: _.get(getState(), 'User.memberInfo.memberToken', '')
                  },
                  timeout: obj.timeout || 10000,
                  cancelToken: source.token
                })
                .then((response)=>{
                  if(obj.showLoading) {dispatch(RDActions['AppState']['showLoadingOnRequest']({show: false}))}
                  Debug.log(preTextLog+':callback:'+fullRequestUrl+':'+JSON.stringify(req));
                  if(response.data.code === 200) {
                    var res = response.data;
                    Debug.log(preTextLog +'res:');
                    if (self.logRes) {
                      Debug.log(res);
                    }
                    data={
                      arg:argTemp,
                      res:res,
                      extras,
                      query
                    }
                    var onDoneRet = true;
                    if (obj.onDone) {onDoneRet = obj.onDone.call(self, dispatch,getState,data);}
                    if (onDoneRet) {
                      if(setState) {dispatch(RDActions[this.sortName][actionName+'OnResult'](RDActionsTypes.constants.REQUEST_SUBTYPE.SUCCESS,data));}
                      resolve(data);
                    }else{
                      if(setState) {dispatch(RDActions[this.sortName][actionName+'OnResult'](RDActionsTypes.constants.REQUEST_SUBTYPE.ERROR,data));}
                      reject(data);
                    }
                  } else if(response.data.code === 1993) {
                    dispatch(RDActions.User.requireLogin())
                    NotifyUtil.pushAlertTopNotify({
                      type: 'warning',
                      content: 'Phiên đăng nhập của bạn đã hết hạn, vui lòng đăng nhập lại.',
                      timeClose: 3000,
                    })
                    Util.checkLogin()
                  } else{
                    return Promise.reject(response)
                  }
                })
                .catch((err)=>{
                  if(obj.showLoading) {dispatch(RDActions['AppState']['showLoadingOnRequest']({show: false}))}

                  Debug.log(preTextLog+':err:'+fullRequestUrl+':'+JSON.stringify(req),Debug.level.ERROR);

                  // Debug.log(preTextLog +'err:',Debug.level.ERROR);
                  // Debug.log(err);
                  data={
                    arg:argTemp,
                    // err:util.inspect(err),
                    // err:'err',
                    err:JSON.stringify(err),
                    errObj:err,
                    extras,
                    query
                  }
                  var onErrorRet = true;
                  if (obj.onError) {onErrorRet = obj.onError(dispatch,getState,data);}
                  if (onErrorRet) {
                    if(setState) {dispatch(RDActions[this.sortName][actionName+'OnResult'](RDActionsTypes.constants.REQUEST_SUBTYPE.ERROR,data));}
                    reject(data);
                  }else{
                    if(setState) {dispatch(RDActions[this.sortName][actionName+'OnResult'](RDActionsTypes.constants.REQUEST_SUBTYPE.SUCCESS,data));}
                    resolve(data);
                  }
                })
          })
          return promise;
        }
      };
    })
    return self;
  }
}


export default RDActions_MiddleWare;
