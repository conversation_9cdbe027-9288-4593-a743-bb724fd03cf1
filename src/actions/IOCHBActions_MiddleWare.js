var _ = require('lodash');
var RDActionsTypes = require('./RDActionsTypes');
import RDActions_MiddleWare from './RDActions_MiddleWare';
const DeviceInfo = require('react-native-device-info');

// LIB

// components
var Define = require('../Define');
var Debug = require('../Util/Debug');
var Util = require('../Util/Util');
var RDActions = require('./RDActions');

// NOTE : stuck when call getState (when dispatch another action in a action)

/*
 * action creators
 */

class IOCHBActions_MiddleWare extends RDActions_MiddleWare {
  constructor() {
    super('IOCHBActions_MiddleWare', true);
    this.init();
  }
  actionsList = {
    updateNumberOfUser: {
      query: '/admin/mission/update-number-of-user',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      showLoading: true,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    completeMission: {
      query: '/admin/mission/complete',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      showLoading: true,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    cancelMission: {
      query: '/admin/mission/cancel',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      showLoading: true,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    notifyMission: {
      query: '/admin/mission/notify',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      showLoading: true,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getMissionLog: {
      query: '/admin/mission-log/list',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    assignOfficer: {
      query: '/admin/mission/assign-officer',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      showLoading: true,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listPersonnel: {
      query: '/admin/user/list',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    createMission: {
      query: '/admin/mission/create',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      showLoading: true,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    updateMission: {
      query: '/admin/mission/update',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      showLoading: true,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listUnit: {
      query: '/admin/unit/list',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listMission: {
      query: '/admin/mission/list',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getMission: {
      query: '/admin/mission/get',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    acceptMission: {
      query: '/mission/accept',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      showLoading: true,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    rejectMission: {
      query: '/mission/reject',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    startMission: {
      query: '/admin/mission/start',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listReportOtherIncident: {
      query: '/statistics/reports-incidents-other',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listLatestIncident: {
      query: '/statistics/latest-incidents',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listCategoryReportByArea: {
      query: '/admin/reports-by-area/category',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 10,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listReportByCategory: {
      query: '/admin/reports-by-category/area',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 10,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listCategoryStatistic: {
      query: '/admin/reports-by-category/list-category',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 10,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listReportIncidentHighlight: {
      query: '/statistics/reports-incidents-highlight',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listGroupedSchedule: {
      query: '/schedule/grouped',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listStatisticOnDutyOfficer: {
      query: '/statistics/on-duty-officers',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listStatisticTotalOfficer: {
      query: '/statistics/officer-summary',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listStatisticAttendance: {
      query: '/statistics/attendance',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listAttendanceStatistic: {
      query: '/attendance/statistics',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listStatisticDocumentSummary: {
      query: '/statistics/documents-summary',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listStatisticReportSummary: {
      query: '/statistics/reports-summary',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listStatisticReportByArea: {
      query: '/statistics/reports-by-area',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    attendanceCheckin: {
      query: '/attendance/checkin',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    attendanceHistory: {
      query: '/attendance/history',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    attendanceStatus: {
      query: '/attendance/status',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    attendanceStatistic: {
      query: '/attendance/statistics',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    leaveRequestList: {
      query: '/leave-request/list',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    leaveRequestCreate: {
      query: '/leave-request/create',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    leaveRequestDetail: {
      query: '/leave-request/detail',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getWorkSchedule: {
      query: '/work-schedule/get',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },

    reportTemplates: {
      query: '/report/templates',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    reportCreate: {
      query: '/report/create',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    reportList: {
      query: '/report/list',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listArea: {
      query: '/admin/area/list',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    detailReportDocumentSummary: {
      query: '/admin/detail-reports-summary/document',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    detailReportIdentitySummary: {
      query: '/admin/detail-reports-summary/identity',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    detailReportProtectionSummary: {
      query: '/admin/detail-reports-summary/protection',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    detailReportLicensePlateSummary: {
      query: '/admin/detail-reports-summary/license-plate',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    reportStatistics: {
      query: '/report/statistics',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    statusOverView: {
      query: '/attendance/status-overview',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    checkinSuddenAttendance: {
      query: '/sudden-attendance/checkin',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getReport: {
      query: '/report/get',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    reportUpdate: {
      query: '/report/update',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    handbookCategory: {
      query: '/handbook/list-category',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listHandbook: {
      query: '/handbook/list',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getHandbook: {
      query: '/handbook/get',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
  };
}

module.exports = new IOCHBActions_MiddleWare();
