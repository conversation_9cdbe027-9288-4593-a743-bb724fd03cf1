var _ = require('lodash');
var RDActionsTypes = require('./RDActionsTypes');
import RDActions_MiddleWare from './RDActions_MiddleWare';

// LIB

// components
var Define = require('../Define');
var Debug = require('../Util/Debug');
var Util = require('../Util/Util');
var RDActions = require('./RDActions');

// NOTE : stuck when call getState (when dispatch another action in a action)

/*
 * action creators
 */

class CleaningActions_MiddleWare extends RDActions_MiddleWare {
  constructor() {
    super('CleaningActions_MiddleWare', true);
    this.init();
  }
  actionsList = {
    getNearest: {
      query: '/staff/get-nearest',
      argFormat: {},
      argMap: {},
      showLoading: false,
      limitProcess: 1,
      serverAddr: Define.constants.serverHeyClean,
      apiVersion: '1.0',
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getOrderType: {
      query: '/order-type/get',
      argFormat: {},
      argMap: {},
      showLoading: false,
      limitProcess: 1,
      serverAddr: Define.constants.serverHeyClean,
      apiVersion: '1.0',
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getConfig: {
      query: '/order/get-config',
      argFormat: {},
      argMap: {},
      showLoading: false,
      limitProcess: 1,
      serverAddr: Define.constants.serverHeyClean,
      apiVersion: '1.0',
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getDistanceMoney: {
      query: '/order/calculate-money',
      argFormat: {},
      argMap: {},
      showLoading: false,
      limitProcess: 1,
      serverAddr: Define.constants.serverHeyClean,
      apiVersion: '1.0',
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    create: {
      query: '/order/create',
      argFormat: {},
      argMap: {},
      showLoading: false,
      limitProcess: 1,
      serverAddr: Define.constants.serverHeyClean,
      apiVersion: '1.0',
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listForCustomer: {
      query: '/order/list-for-customer',
      argFormat: {},
      argMap: {},
      showLoading: false,
      limitProcess: 1,
      serverAddr: Define.constants.serverHeyClean,
      apiVersion: '1.0',
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getForCustomer: {
      query: '/order/get-for-customer',
      argFormat: {},
      argMap: {},
      showLoading: false,
      limitProcess: 1,
      serverAddr: Define.constants.serverHeyClean,
      apiVersion: '1.0',
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getChangedOrdersForCustomer: {
      query: '/order/get-changed-for-customer',
      argFormat: {},
      argMap: {},
      showLoading: false,
      limitProcess: 2,
      serverAddr: Define.constants.serverHeyClean,
      apiVersion: '1.0',
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    reject:{
      query: '/order/reject',
      argFormat: {},
      serverAddr: Define.constants.serverHeyClean,
      argMap: {},
      apiVersion: 1,
      showLoading:true,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    rejectJob:{
      query: '/order/reject-job',
      argFormat: {},
      serverAddr: Define.constants.serverHeyClean,
      argMap: {},
      apiVersion: 1,
      showLoading:true,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    retryJob:{
      query: '/order/retry-job',
      argFormat: {},
      serverAddr: Define.constants.serverHeyClean,
      argMap: {},
      apiVersion: 1,
      showLoading:true,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    retry:{
      query: '/order/retry',
      argFormat: {},
      serverAddr: Define.constants.serverHeyClean,
      argMap: {},
      apiVersion: 1,
      showLoading:true,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    trackCall:{
      query: '/order/track-call-for-customer',
      argFormat: {},
      serverAddr: Define.constants.serverHeyClean,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    trackMessage:{
      query: '/order/track-message-for-customer',
      argFormat: {},
      serverAddr: Define.constants.serverHeyClean,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getNewOrderMessage: {
      query:'/chat/get-new-order-message',
      argFormat:{},
      argMap:{},
      showLoading: false,
      apiVersion: Define.constants.apiVersion,
      //serverAddr:`http://192.168.0.114:8000`,
      limitProcess: 10,
      onArg:(arg,getState)=>{
        return {
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', ''),
          ...arg
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:(dispatch,getState,data)=>{
        return true
      }
    },
    getPromote:{
      query: '/promote-heyclean/get',
      argFormat: {},
      serverAddr: Define.constants.serverPromoteAddr,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listPromote:{
      query: '/promote-heyclean/list',
      argFormat: {},
      serverAddr: Define.constants.serverPromoteAddr,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    countPromote:{
      query: '/promote-heyclean/count',
      argFormat: {},
      serverAddr: Define.constants.serverPromoteAddr,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getPromoteCodeById:{
      query: '/promote-heyclean/get-code-by-id',
      argFormat: {},
      serverAddr: Define.constants.serverPromoteAddr,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listReasonsCancel:{
      query: '/order/list-reasons-cancel',
      argFormat: {},
      serverAddr: Define.constants.serverHeyClean,
      argMap: {},
      apiVersion: 1,
      showLoading:true,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    rating: {
      query:'/clean/member/rating',
      argFormat: {},
      serverAddr: Define.constants.serverRatingHeyCareAddr,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listRatingReason: {
      query:'/member/list-rating-reason',
      argFormat: {},
      serverAddr: Define.constants.serverRatingHeyCareAddr,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getTypicalComments: {
      query:'/clean/member/get-typical-comments',
      argFormat: {},
      serverAddr: Define.constants.serverRatingHeyCareAddr,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
  }
};

module.exports = new CleaningActions_MiddleWare();
