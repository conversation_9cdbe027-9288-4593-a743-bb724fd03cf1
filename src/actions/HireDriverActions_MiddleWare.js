var _ = require('lodash');
var RDActionsTypes = require('./RDActionsTypes');
import RDActions_MiddleWare from './RDActions_MiddleWare';

// LIB

// components
var Define = require('../Define');
var Debug = require('../Util/Debug');
var Util = require('../Util/Util');
var RDActions = require('./RDActions');

// NOTE : stuck when call getState (when dispatch another action in a action)

/*
 * action creators
 */

class HireDriverActions_MiddleWare extends RDActions_MiddleWare {
  constructor() {
    super('HireDriverActions_MiddleWare', true);
    this.init();
  }
  actionsList = {
    trackCall: {
      query: '/hire-driver/track-call-customer',
      argFormat: {},
      serverAddr: Define.constants.serverHireDriverAddr,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    trackMessage: {
      query: '/hire-driver/track-message-customer',
      argFormat: {},
      serverAddr: Define.constants.serverHireDriverAddr,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getCurrentState: {
      query: '/hire-driver/get-current-state',
      argFormat: {},
      argMap: {},
      showLoading: false,
      limitProcess: 1,
      serverAddr: Define.constants.serverHireDriverAddr,
      apiVersion: '1.0',
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    calculateMoney: {
      query: '/hire-driver/calculate-money',
      argFormat: {},
      argMap: {},
      showLoading: false,
      limitProcess: 1,
      serverAddr: Define.constants.serverHireDriverAddr,
      apiVersion: '1.0',
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getChangedOrdersForCustomer: {
      query: '/hire-driver/get-changed-orders-for-customer',
      argFormat: {},
      argMap: {},
      showLoading: false,
      limitProcess: 2,
      serverAddr: Define.constants.serverHireDriverAddr,
      apiVersion: '1.0',
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getOrderType: {
      query: '/order-type/get',
      argFormat: {},
      argMap: {},
      showLoading: false,
      limitProcess: 1,
      serverAddr: Define.constants.serverHireDriverAddr,
      apiVersion: '1.0',
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getDistanceMoney: {
      query: '/hire-driver/get-distance-money',
      argFormat: {},
      argMap: {},
      showLoading: false,
      limitProcess: 1,
      serverAddr: Define.constants.serverHireDriverAddr,
      apiVersion: '1.0',
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    create: {
      query: '/hire-driver/create',
      argFormat: {},
      argMap: {},
      showLoading: false,
      limitProcess: 1,
      serverAddr: Define.constants.serverHireDriverAddr,
      apiVersion: '1.0',
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    modify: {
      query: '/hire-driver/modify',
      argFormat: {},
      serverAddr: Define.constants.serverHireDriverAddr,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getCarBranch: {
      query: '/hire-driver/get-car-branch',
      argFormat: {},
      argMap: {},
      showLoading: false,
      limitProcess: 1,
      serverAddr: Define.constants.serverHireDriverAddr,
      apiVersion: '1.0',
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    addVehicle: {
      query: '/car-info/create',
      argFormat: {},
      argMap: {},
      showLoading: false,
      limitProcess: 1,
      serverAddr: Define.constants.serverHireDriverAddr,
      apiVersion: '1.0',
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    removeVehicle: {
      query: '/car-info/inactive',
      argFormat: {},
      argMap: {},
      showLoading: false,
      limitProcess: 1,
      serverAddr: Define.constants.serverHireDriverAddr,
      apiVersion: '1.0',
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    modifyVehicle: {
      query: '/car-info/update',
      argFormat: {},
      argMap: {},
      showLoading: false,
      limitProcess: 1,
      serverAddr: Define.constants.serverHireDriverAddr,
      apiVersion: '1.0',
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listVehicle: {
      query: '/car-info/list',
      argFormat: {},
      argMap: {},
      showLoading: false,
      limitProcess: 1,
      serverAddr: Define.constants.serverHireDriverAddr,
      apiVersion: '1.0',
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listForCustomer: {
      query: '/hire-driver/list-for-customer',
      argFormat: {},
      serverAddr: Define.constants.serverHireDriverAddr,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getForCustomer: {
      query: '/hire-driver/get-for-customer',
      argFormat: {},
      serverAddr: Define.constants.serverHireDriverAddr,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    reject: {
      query: '/hire-driver/reject',
      argFormat: {},
      serverAddr: Define.constants.serverHireDriverAddr,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      showLoading: true,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    retry: {
      query: '/hire-driver/retry',
      argFormat: {},
      serverAddr: Define.constants.serverHireDriverAddr,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      showLoading: true,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getPromote: {
      query: '/promote-hire-driver/get',
      argFormat: {},
      serverAddr: Define.constants.serverPromoteAddr,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    countPromote: {
      query: '/promote-hire-driver/count',
      argFormat: {},
      serverAddr: Define.constants.serverPromoteAddr,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listPromote: {
      query: '/promote-hire-driver/list',
      argFormat: {},
      serverAddr: Define.constants.serverPromoteAddr,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
  };
}

module.exports = new HireDriverActions_MiddleWare();
