
var _ = require('lodash')
var RDActionsTypes = require('./RDActionsTypes');
import RDActions_MiddleWare from './RDActions_MiddleWare'

// LIB
// components
var Define = require('../Define');
var Debug = require('../Util/Debug');
var Util = require('../Util/Util');
var RDActions = require('./RDActions');

// NOTE : stuck when call getState (when dispatch another action in a action)

/*
 * action creators
 */

class FeedsActions_MiddleWare extends RDActions_MiddleWare {
  constructor(){
    super('FeedsActions_MiddleWare',true);
    this.init();
  }
  actionsList={
    get:{
      query:'/feeds/get',
      argFormat:{},
      argMap:{},
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          ...arg,
          memberToken:_.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getFeedsSaved: {
      query:'/member/feed/all',
      argFormat:{
        memberToken: '',
      },
      argMap:{},
      showLoading: true,
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          memberToken:_.get(getState(), 'User.memberInfo.member.memberToken', ''),
          newTemplate: 1,
          ...arg,
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getHistoryOrder: {
      query:'/feeds/get-history',
      argFormat:{
      },
      argMap:{},
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          ...arg,
          memberToken:_.get(getState(), 'User.memberInfo.member.memberToken', ''),
          newTemplate: 1
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getTotalPostsByTypes: {
      query:'/feeds/get-statistic',
      argFormat:{
      },
      argMap:{},
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          ...arg,
          memberToken:_.get(getState(), 'User.memberInfo.member.memberToken', ''),
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    comment:{
      query:'/comment/post',
      argFormat:{
        message:'',
        feed_id:''
      },
      argMap:{},
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          ...arg,
          memberToken:_.get(getState(), 'User.memberInfo.member.memberToken', ''),
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    notify:{
      query:'/member/notifications',
      argFormat:{},
      argMap:{},
      limitProcess:0,
      onArg:(arg,getState)=>{
        return {
          memberToken:_.get(getState(), 'User.memberInfo.member.memberToken', ''),
          device_id:Define.constants.deviceId,
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    save:{
      query:'/member/feed/save',
      argFormat:{
        message:'',
        permalink_url:'',
        from : {
          name: '',
          id: ''
        },
        created_time:'',
        id:'',
      },
      argMap:{},
      apiVersion:Define.constants.apiVersion,
      limitProcess:0,
      onArg:(arg,getState)=>{
        return {
          message:arg.message,
          permalink_url:arg.permalink_url,
          from:{
            name:arg.from.name,
            id:arg.from.id,
            realId: arg.from.realId
          },
          phone:arg.phone,
          created_time:arg.created_time,
          feed_id:arg.id,
          memberToken:_.get(getState(), 'User.memberInfo.member.memberToken', ''),
          device_id:Define.constants.deviceId,
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    deleteFeedSaved:{
      query:'/member/feed/delete',
      argFormat:{
        feed_id:'',
        memberToken: ''
      },
      argMap:{},
      apiVersion:Define.constants.apiVersion,
      limitProcess:0,
      onArg:(arg,getState)=>{
        return {
          feed_id: arg.feed_id,
          memberToken:_.get(getState(), 'User.memberInfo.member.memberToken', ''),
          device_id:Define.constants.deviceId,
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    deleteAllFeedsSaved:{
      query:'/member/feed/delete-all',
      argFormat:{
        feed_id:'',
        memberToken: ''
      },
      argMap:{},
      apiVersion:Define.constants.apiVersion,
      limitProcess:0,
      onArg:(arg,getState)=>{
        return {
          memberToken:_.get(getState(), 'User.memberInfo.member.memberToken', ''),
          device_id:Define.constants.deviceId,
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getComments:{
      query:'/comment/get-comments',
      argFormat:{
        feed_id:'',
      },
      argMap:{},
      apiVersion:Define.constants.apiVersion,
      limitProcess:0,
      onArg:(arg,getState)=>{
        return {
          memberToken:_.get(getState(), 'User.memberInfo.member.memberToken', ''),
          feed_id: arg.feed_id,
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    createOrder:{
      query:'/feed/create',
      argFormat:{
      },
      argMap:{},
      limitProcess:0,
      onArg:(arg,getState)=>{
        return {
          ...arg,
          memberToken:_.get(getState(), 'User.memberInfo.member.memberToken', ''),
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getFeedSystem:{
      query:'/feeds-system/get',
      argFormat:{
      },
      argMap:{},
      limitProcess:0,
      onArg:(arg,getState)=>{
        return {
          memberToken:_.get(getState(), 'User.memberInfo.member.memberToken', ''),
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    like:{
      query:'/member/like',
      argFormat:{
        facebookId:''
      },
      argMap:{},
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          facebookId:arg.facebookId,
          memberToken:_.get(getState(), 'User.memberInfo.member.memberToken', ''),
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    dislike:{
      query:'/member/dislike',
      argFormat:{
        facebookId:''
      },
      argMap:{},
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          facebookId:arg.facebookId,
          memberToken:_.get(getState(), 'User.memberInfo.member.memberToken', ''),
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getStatisticsByWeek: {
      query:'/newfeeds/getStatisticsByWeek',
      serverAddr: Define.constants.serverStatiticAddr,
      argFormat:{},
      argMap:{},
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      showLoading:true,
      onArg:(arg,getState)=>{
        return {
          ...arg
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getStatisticsByDay: {
      query:'/newfeeds/getStatisticsByDay',
      serverAddr: Define.constants.serverStatiticAddr,
      argFormat:{},
      argMap:{},
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      showLoading:true,
      onArg:(arg,getState)=>{
        return {
          ...arg
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getStatisticsByMonth: {
      query:'/newfeeds/getStatisticsByMonth',
      serverAddr: Define.constants.serverStatiticAddr,
      argFormat:{},
      argMap:{},
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      showLoading:true,
      onArg:(arg,getState)=>{
        return {
          ...arg
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getFinalTime: {
      query:'/newfeeds/getFinalTime',
      serverAddr: Define.constants.serverStatiticAddr,
      argFormat:{},
      argMap:{},
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          ...arg
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getConfigFeedFacebook: {
      query: '/feed/get-config-feed-facebook',
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    }
  }
}


module.exports= new FeedsActions_MiddleWare();
