
var _ = require('lodash')
var RDActionsTypes = require('./RDActionsTypes');
import RDActions_MiddleWare from './RDActions_MiddleWare'

// LIB

// components
var Define = require('../Define');
var Debug = require('../Util/Debug');
var Util = require('../Util/Util');
var RDActions = require('./RDActions');

// NOTE : stuck when call getState (when dispatch another action in a action)

/*
 * action creators
 */

class FeedsSystemActions_MiddleWare extends RDActions_MiddleWare {
  constructor(){
    super('FeedsSystemActions_MiddleWare',true);
    this.init();
  }
  actionsList={
    optimiseRoute: {
      query: '/order/optimise',
      argFormat: {
      },
      argMap: {},
      showLoading: true,
      limitProcess: 1,
      serverAddr: Define.constants.serverOrderAddr,
      apiVersion: '2.1',
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', ''),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getCurrentOrder: {
      query:'/order/get-current-order',
      serverAddr: Define.constants.serverOrderAddr,
      argFormat:{
      },
      argMap:{},
      showLoading: false,
      apiVersion: '4.1',
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    listGuideOrder: {
      query:'/order/list-guide',
      serverAddr: Define.constants.serverOrderAddr,
      argFormat:{
      },
      argMap:{},
      showLoading: true,
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    listQuestionOrder: {
      query:'/order/list-question',
      serverAddr: Define.constants.serverOrderAddr,
      argFormat:{
      },
      argMap:{},
      showLoading: true,
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          includes: arg.includes,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    create: {
      query:'/order/create',
      serverAddr: Define.constants.serverOrderAddr,
      argFormat:{
      },
      argMap:{},
      showLoading: false,
      apiVersion:'4.1',
      // apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          ...arg,
          newApprove:1,
          vForVAT: 1,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getOrderType:{
      query:'/order-type/list',
      serverAddr: Define.constants.serverOrderAddr,
      argFormat:{
      },
      argMap:{},
      timeout: 3000,
      showLoading: false,
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          memberToken:_.get(getState(), 'User.memberInfo.member.memberToken', ''),
          ...arg,
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    listGroupOrderType:{
      query:'/order-type/list-group',
      serverAddr: Define.constants.serverOrderAddr,
      argFormat:{
      },
      argMap:{},
      timeout: 3000,
      showLoading: false,
      apiVersion: '2.0',
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          memberToken:_.get(getState(), 'User.memberInfo.member.memberToken', ''),
          ...arg,
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    modify: {
      query:'/order/modify',
      serverAddr: Define.constants.serverOrderAddr,
      argFormat:{
      },
      argMap:{},
      showLoading: false,
      apiVersion:'4.1',
      // apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          ...arg,
          newApprove:1,
          vForVAT: 1,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getForShipper:{
      query:'/order/get-for-shipper',
      serverAddr: Define.constants.serverOrderAddr,
      argFormat:{},
      argMap:{},
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          ...arg,
          newDepositSalary: 1,
          forVAT: 1,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getForShop:{
      query:'/order/get-for-shop',
      serverAddr: Define.constants.serverOrderAddr,
      argFormat:{},
      argMap:{},
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          ...arg,
          newDepositSalary: 1,
          forVAT: 1,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    get:{
      query:'/order/get',
      serverAddr: Define.constants.serverOrderAddr,
      argFormat:{},
      argMap:{},
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          ...arg,
          newDepositSalary: 1,
          forVAT: 1,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    take: {
      query:'/order/take',
      serverAddr: Define.constants.serverOrderAddr,
      argFormat:{
        id: ''
      },
      argMap:{},
      showLoading: true,
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          id: arg.id,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    list: {
      query:'/order/list-for-shipper',
      serverAddr: Define.constants.serverOrderAddr,
      argFormat:{
        skip: '',
        query: {}
      },
      argMap:{},
      timeout: 3000,
      showLoading: false,
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', ''),
          newApprove: 1,
          newDepositSalary: 1,
          forVAT: 1,
          ...arg
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    listForShop: {
      query:'/order/list-for-shop',
      serverAddr: Define.constants.serverOrderAddr,
      argFormat:{
        skip: '',
        query: {}
      },
      argMap:{},
      timeout: 3000,
      showLoading: false,
      apiVersion:Define.constants.apiVersion,
      limitProcess:2,
      onArg:(arg,getState)=>{
        return {
          newDepositSalary: 1,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', ''),
          forVAT: 1,
          ...arg
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getMarkUniform: {
      query:'/member/get-mark-uniform',
      serverAddr: Define.constants.serverStatiticAddr,
      argFormat:{
        skip: '',
        query: {}
      },
      argMap:{},
      showLoading: true,
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getFeeCOD: {
      query:'/order/get-fee-cod',
      serverAddr: Define.constants.serverOrderAddr,
      argFormat:{
        skip: '',
        query: {}
      },
      argMap:{},
      showLoading: false,
      apiVersion:'2.1',
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    listInstanceOrder: {
      query:'/order/list-instance-order',
      serverAddr: Define.constants.serverOrderAddr,
      argFormat:{},
      argMap:{},
      showLoading: false,
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          newDepositSalary: 1,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', ''),
          ...arg
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    rating:{
      query:'/member/rating',
      argFormat:{},
      argMap:{},
      serverAddr: Define.constants.serverRatingAddr,
      showLoading: false,
      apiVersion: '2.1',
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:(dispatch,getState,data)=>{
        return true
      },
    },
    updateStatus: {
      query:'/order/update-status',
      serverAddr: Define.constants.serverOrderAddr,
      argFormat:{
        id: ''
      },
      argMap:{},
      showLoading: true,
      apiVersion:'3.1',
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    rejectForShop: {
      query:'/order/reject-for-shop',
      serverAddr: Define.constants.serverOrderAddr,
      argFormat:{
      },
      argMap:{},
      showLoading: true,
      apiVersion:'3.1',
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          id: arg.id,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    rejectPushOrder: {
      query:'/order/reject-push-order',
      serverAddr: Define.constants.serverOrderAddr,
      argFormat:{
      },
      argMap:{},
      showLoading: false,
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    rejectForShipper: {
      query:'/order/reject-for-shipper',
      serverAddr: Define.constants.serverOrderAddr,
      argFormat:{
      },
      argMap:{},
      showLoading: true,
      apiVersion:'3.1',
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          id: arg.id,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    retry: {
      query:'/order/retry',
      serverAddr: Define.constants.serverOrderAddr,
      argFormat:{
      },
      argMap:{},
      showLoading: true,
      apiVersion:'4.1',
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          id: arg.id,
          vForVAT: 1,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', ''),
          ...arg
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getDistanceMoney: {
      query:'/order/get-distance-money',
      serverAddr: Define.constants.serverOrderAddr,
      argFormat:{
      },
      argMap:{},
      apiVersion: '2.2',
      limitProcess:1,
      showLoading: false,
      onArg:(arg,getState)=>{
        return {
          orderType: arg.orderType,
          origin: arg.origin,
          destination: arg.destination,
          newApprove: arg.newApprove,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    calculateMoneyFromDistance: {
      query:'/order/calculate-money-from-distance',
      serverAddr: Define.constants.serverOrderAddr,
      argFormat:{
      },
      argMap:{},
      apiVersion: '2.2',
      limitProcess:1,
      showLoading: false,
      onArg:(arg,getState)=>{
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getLocationName: {
      query:'/order/get-location-name',
      serverAddr: Define.constants.serverOrderAddr,
      argFormat:{
      },
      argMap:{},
      showLoading: false,
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', ''),
          newVersion: 1
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getConfigPlaceDetail: {
      query:'/place-detail/get-config',
      serverAddr: Define.constants.serverOrderAddr,
      argFormat:{
      },
      argMap:{},
      showLoading: false,
      apiVersion:'2.1',
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    trackCall: {
      query:'/order/call',
      serverAddr: Define.constants.serverOrderAddr,
      argFormat:{
      },
      argMap:{},
      showLoading: false,
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    trackCallShop: {
      query:'/order/call-for-shop',
      serverAddr: Define.constants.serverOrderAddr,
      argFormat:{
      },
      argMap:{},
      showLoading: false,
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    trackMessage: {
      query:'/order/tracking-message',
      serverAddr: Define.constants.serverOrderAddr,
      argFormat:{
      },
      argMap:{},
      showLoading: false,
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    trackMessageShop: {
      query:'/order/tracking-message-shop',
      serverAddr: Define.constants.serverOrderAddr,
      argFormat:{
      },
      argMap:{},
      showLoading: false,
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    notifyTake: {
      query:'/order/notify-take',
      serverAddr: Define.constants.serverOrderAddr,
      argFormat:{
      },
      argMap:{},
      showLoading: true,
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    notifyDone: {
      query:'/order/notify-done',
      serverAddr: Define.constants.serverOrderAddr,
      argFormat:{
      },
      argMap:{},
      showLoading: true,
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getStatisticsByWeek: {
      query:'/orderSystem/getStatisticsByWeek',
      serverAddr: Define.constants.serverStatiticAddr,
      argFormat:{},
      argMap:{},
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      showLoading:true,
      onArg:(arg,getState)=>{
        return {
          ...arg
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getStatisticsByDay: {
      query:'/orderSystem/getStatisticsByDay',
      serverAddr: Define.constants.serverStatiticAddr,
      argFormat:{},
      argMap:{},
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      showLoading:true,
      onArg:(arg,getState)=>{
        return {
          ...arg
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getStatisticsByMonth: {
      query:'/orderSystem/getStatisticsByMonth',
      serverAddr: Define.constants.serverStatiticAddr,
      argFormat:{},
      argMap:{},
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      showLoading:true,
      onArg:(arg,getState)=>{
        return {
          ...arg
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getFinalTime: {
      query:'/orderSystem/getFinalTime',
      serverAddr: Define.constants.serverStatiticAddr,
      argFormat:{},
      argMap:{},
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          ...arg
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getHistory: {
      query:'/orderSystem/getHistory',
      serverAddr: Define.constants.serverStatiticAddr,
      argFormat:{},
      argMap:{},
      showLoading:true,
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          ...arg,
          newDepositSalary: 1
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getStatisticsOrder: {
      query:'/orderSystem/getStatisticsOrder',
      serverAddr: Define.constants.serverStatiticAddr,
      argFormat:{},
      argMap:{},
      showLoading:true,
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          ...arg
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    listOrderSearching: {
      query:'/order/list-order-by-latlng',
      serverAddr:Define.constants.serverOrderAddr,
      argFormat:{},
      argMap:{},
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    listReasonsCancel: {
      query:'/order/list-reasons-cancel',
      serverAddr:Define.constants.serverOrderAddr,
      argFormat:{},
      argMap:{},
      apiVersion:Define.constants.apiVersion,
      methode:'get',
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          ...arg
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    sendReasonCancel: {
      query:'/order/send-reason-cancel',
      serverAddr:Define.constants.serverOrderAddr,
      argFormat:{},
      argMap:{},
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    changeReceiverInfo: {
      query:'/order/update-receiver-info',
      serverAddr: Define.constants.serverOrderAddr,
      argFormat:{},
      argMap:{},
      showLoading: false,
      apiVersion: '2.1',
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    listRatingReason:{
      query:'/member/listRatingReason',
      argFormat:{},
      argMap:{},
      serverAddr: Define.constants.serverRatingAddr,
      showLoading: false,
      apiVersion: '3.0',
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:(dispatch,getState,data)=>{
        return true
      },
    },
    listRatedReasons: {
      query: '/member/listRatedReasons',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverRatingAddr,
      showLoading: false,
      apiVersion: Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      },
    },
    listRanking: {
      query: '/ranking/get-ranking',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverStatiticAddr,
      showLoading: true,
      apiVersion: Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      },
    },
    getCurrentRank: {
      query: '/ranking/get-current-rank',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverStatiticAddr,
      showLoading: true,
      apiVersion: Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      },
    },
    listAvaiableRanking: {
      query: '/ranking/list-avaiable-ranking',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverStatiticAddr,
      showLoading: true,
      apiVersion: Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      },
    },
    listRestaurant: {
      query: '/restaurant/list',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverRestaurantAddr,
      showLoading: false,
      apiVersion: Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      },
    },
    menuType: {
      query: '/restaurant/menu-type/list',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverRestaurantAddr,
      showLoading: false,
      apiVersion: Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      },
    },
    listMenu: {
      query: '/restaurant/menu/list',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverRestaurantAddr,
      showLoading: true,
      apiVersion: Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      },
    },
    getRestaurant: {
      query: '/restaurant/get',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverRestaurantAddr,
      showLoading: false,
      apiVersion: Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      },
    },
    searchRestaurant: {
      query: '/restaurant/search-by-text',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverRestaurantAddr,
      showLoading: false,
      apiVersion: Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      },
    },
    getSelectedDishes: {
      query: '/restaurant/menu/list',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverRestaurantAddr,
      showLoading: false,
      apiVersion: Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getPromote: {
      query: '/promote/get',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverPromoteAddr,
      showLoading: false,
      apiVersion: '3.0',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          newVersion: 1,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getPromoteCodeById: {
      query: '/promote/get-code-by-id',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverPromoteAddr,
      showLoading: false,
      apiVersion: Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    listServiceAvailable: {
      query: '/order/list-service-available',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    statisticByDay: {
      query: '/income/income-stats-by-day',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverStatiticAddr,
      showLoading: false,
      apiVersion: Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          newDepositSalary: 1,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    statisticByWeek: {
      query: '/income/income-stats-by-week',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverStatiticAddr,
      showLoading: false,
      apiVersion: Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          newDepositSalary: 1,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    statisticByMonth: {
      query: '/income/income-stats-by-month',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverStatiticAddr,
      showLoading: false,
      apiVersion: Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          newDepositSalary: 1,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    listReport: {
      query: '/order/list-report',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    report: {
      query: '/order/send-report',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getUnratedOrder: {
      query: '/order/get-unrated',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: '2.1',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getConfigOrder: {
      query: '/order/get-config',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getConfigForCreatedOrders: {
      query: '/order/get-config-for-created-orders',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: '3.1',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getConfigVat: {
      query: '/order/get-config-vat',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getNewOrderMessage: {
      query:'/chat/get-new-order-message',
      argFormat:{},
      argMap:{},
      showLoading: false,
      apiVersion:Define.constants.apiVersion,
      //serverAddr:`http://*************:8000`,
      limitProcess: 10,
      onArg:(arg,getState)=>{
        return {
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', ''),
          ...arg
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:(dispatch,getState,data)=>{
        return true
      }
    },
    getChangedOrdersForShipper: {
      query:'/order/get-changed-orders-for-shipper',
      argFormat:{},
      argMap:{},
      showLoading: false,
      apiVersion:Define.constants.apiVersion,
      serverAddr:Define.constants.serverOrderAddr,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', ''),
          ...arg
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:(dispatch,getState,data)=>{
        return true
      }
    },
    getChangedOrdersForShop: {
      query:'/order/get-changed-orders-for-shop',
      argFormat:{},
      argMap:{},
      showLoading: false,
      apiVersion:Define.constants.apiVersion,
      serverAddr:Define.constants.serverOrderAddr,
      limitProcess:2,
      onArg:(arg,getState)=>{
        return {
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', ''),
          ...arg
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:(dispatch,getState,data)=>{
        return true
      }
    },
    getLatestLocation: {
      query:'/shipper/get-latest-location',
      argFormat:{},
      argMap:{},
      showLoading: false,
      apiVersion:Define.constants.apiVersion,
      serverAddr: Define.constants.serverLocationAddr,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', ''),
          ...arg
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:(dispatch,getState,data)=>{
        return true
      }
    },
    getCurrentState: {
      query:'/order/get-current-state',
      serverAddr: Define.constants.serverOrderAddr,
      argFormat:{
        skip: '',
        query: {}
      },
      argMap:{},
      showLoading: false,
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', ''),
          ...arg
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getCurrentStateBike: {
      query: '/bike/get-current-state',
      serverAddr: Define.constants.serverBikeAddr,
      argFormat: {
        skip: '',
        query: {}
      },
      argMap: {},
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    calculateFeeVat: {
      query: '/order/calculate-fee-vat',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    uploadImageOrder: {
      query: '/order/shipper-upload-image',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: '3.1',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    uploadBill: {
      query: '/order/shipper-upload-bill',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: '3.2',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    listPromote: {
      query: '/promote/list',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverPromoteAddr,
      showLoading: false,
      apiVersion: '3.0',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    countPromote: {
      query: '/promote/count',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverPromoteAddr,
      showLoading: false,
      apiVersion: Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getConfigImageOrderForShop: {
      query: '/order/get-config-image-order-for-shop',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: '3.1',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          newVersion: 1,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    ratingUniform: {
      query: '/member/rating-uniform',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverRatingAddr,
      showLoading: false,
      apiVersion: Define.constants.apiVersion,
      limitProcess:1,
      onArg: (arg,getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch,getState,data) => {
        return true
      }
    },
    listRatingUniformReason:{
      query: '/member/listRatingUniformReason',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverRatingAddr,
      showLoading: false,
      apiVersion: Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg,getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch,getState,data) => {
        return true
      },
    },
    getConfigCOD: {
      query: '/order/get-config-cod',
      serverAddr: Define.constants.serverOrderAddr,
      argFormat: {
        skip: '',
        query: {}
      },
      argMap: {},
      showLoading: false,
      apiVersion: '3.0',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getQRcode:{
      query: '/vnpay/get-qr',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverVNpayAddr,
      showLoading: true,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      },
    },
    registerMerchant: {
      query: '/vnpay/register-merchant',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverVNpayAddr,
      showLoading: true,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      },
    },
    getMerchant: {
      query: '/vnpay/get-merchant',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverVNpayAddr,
      showLoading: true,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      },
    },
    getOrderTypeErrand: {
      query: '/errand/get-order-type',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    listErrandJob: {
      query: '/errand/list-job',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getOrderTypeFood: {
      query: '/order-type/get',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverStoreAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getConfigWeight: {
      query: '/order/get-config-weight-fee',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    addProcessingOrder: {
      query: '/order/add-processing-order',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: '4.1',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getLimitDeposit: {
      query: '/order/get-limit-deposit',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: '4.1',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    listCategoryStore: {
      query: '/errand/list-category-store',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    listStore: {
      query: '/errand/list-store',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getLinkOrder: {
      query: '/order/get-link-order',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    trackLinkOrder: {
      query: '/order/track-link-order',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    productType: {
      query: '/product-type/list',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverStoreAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      },
    },
    listProduct: {
      query: '/product/list',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverStoreAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      },
    },
    listProductTypeDefault: {
      query: '/product-type/list-default',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverStoreAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      },
    },
    listNewStore: {
      query: '/store/list',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverStoreAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    listStoreWithProduct: {
      query: '/store/list-with-product',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverStoreAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    findPageProduct: {
      query: '/product/find-page-product',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverStoreAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getStore: {
      query: '/store/get',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverStoreAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    updatePath: {
      query: '/order/update-path',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: '4.1',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getConfigErrandFee: {
      query: '/store/get-config-errand-fee',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverStoreAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getBannerStore: {
      query: '/store/get-banner',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverStoreAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },

    listProductViaType: {
      query: '/product/list-product-via-type',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverStoreAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    listReasonsForMerchant: {
      query:'/order/list-reasons',
      serverAddr:Define.constants.serverStoreAddr,
      argFormat:{},
      argMap:{},
      apiVersion: 1,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          ...arg
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getConfigCurrentLocation: {
      query: '/app/get-config-add-current-location',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getConfigChangeLocation: {
      query: '/app/config-change-location',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getConfigSMSReceiver: {
      query: '/order/get-config-sms-receiver',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: 2,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getConfigBlockRider: {
      query: '/order/get-config-block-rider',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: 2,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    blockRider: {
      query: '/order/block-rider',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: 2,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getDirection: {
      query: '/order/direction',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: 2,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getConfigTipRealtime: {
      query: '/order/get-config-tip-realtime',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: 2,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    tipRealtime: {
      query: '/order/tip-realtime',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: 2,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getConfigFastDelivery: {
      query: '/order/config-fast-delivery',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 10,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    configAuthenShop: {
      query: '/shop/config-authen-shop',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverAddr,
      showLoading: false,
      apiVersion: '2.0',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    configRegionMoving: {
      query: '/order/config-region-moving',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getConfigBoundaries: {
      query: '/order/config-boundaries',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getMessageReject: {
      query: '/order/get-message-reject',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getConfigStrategyOrigin: {
      query: '/order/config-strategy-origin',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getConfigFragile: {
      query: '/order/config-fragile',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getConfigHandDelivery: {
      query: '/order/config-hand-delivery',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getConfigTipAfterOrderDone: {
      query: '/order/config-tip-after-order-done',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    tipAfterOrderDone: {
      query: '/order/tip-after-order-done',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverOrderAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getConfigMap: {
      query: '/app/get-config-map',
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: 2,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    }

  }
}


module.exports= new FeedsSystemActions_MiddleWare();
