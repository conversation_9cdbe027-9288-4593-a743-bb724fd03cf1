
var _ = require('lodash')
var RDActionsTypes = require('./RDActionsTypes');
import RDActions_MiddleWare from './RDActions_MiddleWare'

// LIB

// components
var Define = require('../Define');
var Debug = require('../Util/Debug');
var Util = require('../Util/Util');
var RDActions = require('./RDActions');

// NOTE : stuck when call getState (when dispatch another action in a action)

/*
 * action creators
 */

class SBHStoreActions_MiddleWare extends RDActions_MiddleWare {
  constructor() {
    super('SBHStoreActions_MiddleWare', true);
    this.init();
  }
  actionsList = {
    exportStatistics: {
      query: '/user/store/export-excel',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getListBranch: {
      query: '/store/list-branch',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    createBranch: {
      query: '/user/store/create-branch',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },

    listBranch: {
      query: '/user/store/list-branch',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    configBranch: {
      query: '/user/store/config-branch',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    changeBranch: {
      query: '/user/store/choose-branch',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getStoreInfo: {
      query: '/user/store/get',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },

    createStore: {
      query: '/user/store/create',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },

    modifyStore: {
      query: '/user/store/modify',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },

    createCategory: {
      query: '/user/product-type/create',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },

    listCategory: {
      query: '/user/product-type/list',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 2,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },

    modifyCategory: {
      query: '/user/product-type/modify',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },

    createProduct: {
      query: '/user/product/create',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },

    listProduct: {
      query: '/user/product/list',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },

    modifyProduct: {
      query: '/user/product/modify',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },

    listBusinessType: {
      query: '/user/store/list-business-type',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },

    activeStore: {
      query: '/user/store/inactive',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },

    listForCustomer: {
      query: '/order/list-for-customer',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },

    getForCustomer: {
      query: '/order/get-for-customer',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    inActiveProduct: {
      query: '/user/product/inactive',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listForMerchant: {
      query: '/order/list-for-merchant',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getForMerchant: {
      query: '/order/get-for-merchant',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    confirmForMerchant: {
      query: '/order/confirm-order',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: true,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    rejectForMerchant: {
      query: '/order/reject-for-merchant',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: true,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getTimeLine: {
      query: '/order/get-time-line',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    rejectForCustomer: {
      query: '/order/reject-for-customer',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: true,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    createOrder: {
      query: '/order/create',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    statisticByTime: {
      query: '/statistic/income',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getProduct: {
      query: '/user/product/get',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    createByBarCode: {
      query: '/user/product/create-by-bar-code',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    configBarCode: {
      query: '/order/config-bar-code',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listUnit: {
      query: '/user/product/list-unit',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getBanners: {
      query: '/user/store/get-banner',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },

    configShowAddCategory: {
      query: '/user/product-type/config-add-product-type',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },

    configBusinessType: {
      query: '/user/store/config-business-type',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },

    configSubType: {
      query: '/user/store/config-sub-type',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },

    listSubType: {
      query: '/user/store/list-sub-type',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    configShowBarCode: {
      query: '/user/product/config-show-barcode',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    inActiveProductType: {
      query: '/user/product-type/inactive',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    createPromoteByMerchant: {
      query: '/user/promote/create',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listPromote: {
      query: '/user/promote/list',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getPromote: {
      query: '/user/promote/get',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    modifyPromote: {
      query: '/user/promote/modify',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    inActivePromote: {
      query: '/user/promote/inactive',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getPromoteForCustomer: {
      query: '/promote/get',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    countPromoteShop: {
      query: '/promote/count',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listPromoteShop: {
      query: '/promote/list',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    configPromoteNew: {
      query: '/promote/config-show-promote',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    countPromoteAll: {
      query: '/user/promote/count',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    countProductAll: {
      query: '/user/product/count',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    configShowContact: {
      query: '/order/config-show-contact',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listCategoryStrategy: {
      query: '/category-strategy/list',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    historyTransaction: {
      query: '/transaction/history',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getProductInStore: {
      query: '/product/get',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listStoreByCategoryStrategy: {
      query: '/category-strategy/list-store',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 5000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 10,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    configViewImage: {
      query: '/store/config-view-image',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    sendMesageForCustomer: {
      query: '/order/wait-order',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    configWaitTime: {
      query: '/order/config-wait-time',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getListNewsTitle: {
      query: '/user/store/list-news-title',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getListNews: {
      query: '/user/store/list-news',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getConfigShowPaymentTickbox: {
      query: '/app/get-config-show-payment-tickbox',
      serverAddr: Define.constants.serverAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 2,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getConfigMessageWarning: {
      query: '/order/config-message-warning',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listProductPreparing: {
      query: '/user/product/list-product-preparing',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    updateDeliveryType: {
      query: '/order/update-status-merchant',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listOrders: {
      query: '/order/list-by-id',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    reFindShipper: {
      query: '/order/retry-for-merchant',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    countProductPreparing: {
      query: '/user/product/count-product-preparing',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getConfigSelfDelivery: {
      query: '/order/config-self-delivery',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listCoSponsors: {
      query: '/user/promote/list-co-sponsors',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    registerCoSponsors: {
      query: '/user/promote/register-co-sponsors',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    inActiveCoSponsors: {
      query: '/user/promote/inactive-co-sponsors',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getConfigCoSponsors: {
      query: '/user/promote/config-co-sponsors',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    sortProductType: {
      query: '/user/product-type/sort',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    sortProduct: {
      query: '/user/product/sort',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    changeStatusStaff: {
      query: '/user/staff/change-status',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    createStaff: {
      query: '/user/staff/create',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    modifyStaff: {
      query: '/user/staff/modify',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    inactiveStaff: {
      query: '/user/staff/inactive',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listStaff: {
      query: '/user/staff/list',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getStaff: {
      query: '/user/staff/get',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listToppingGr: {
      query: '/user/toppingGroup/list',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getToppingGr: {
      query: '/user/toppingGroup/get',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    createToppingGr: {
      query: '/user/toppingGroup/create',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    modifyToppingGr: {
      query: '/user/toppingGroup/modify',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    inActiveToppingGr: {
      query: '/user/toppingGroup/inactive',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listTopping: {
      query: '/user/topping/list',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    createTopping: {
      query: '/user/topping/create',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    modifyTopping: {
      query: '/user/topping/modify',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getTopping: {
      query: '/user/topping/get',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    inActiveTopping: {
      query: '/user/topping/inactive',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
  }
}


module.exports = new SBHStoreActions_MiddleWare();
