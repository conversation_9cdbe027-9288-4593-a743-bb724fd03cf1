//@flow
/*
 * action types
 */
const RDActionsTypes={

  // Todo:{
  //   test:'TEST',
  // },

  ServerConnection:{
    connect:'SERVER_CONNECT',
    disconnect:'SERVER_DISCONNECT',
    changeNetInfo:'SERVER_CONNECT_NET_INFO_CHANGE',
  },

  Store:{
    set:'STORE_SET',
    get:'STORE_GET',
    remove:'STORE_REMOVE',
  },

  Tips:{

  },

  IHeyU:{
    getPetition: 'IHEYU_GET_PETITION',
    listPetition: 'IHEYU_LIST_PETITION',
    createPetition: 'IHEYU_CREATE_PETITION',
    updatePetition: 'IHEYU_UPDATE_PETITION',
    listCommunityPetition: 'IHEYU_LIST_COMMUNITY_PETITION',
    listCategoryPetition: 'IHEYU_LIST_CATEGORY_PETITION',
    listDistrict: 'IHEYU_LIST_DISTRICT',
    listWard: 'IHEYU_LIST_WARD',
    getLocationDetail: 'IHEYU_GET_LOCATION_DETAIL',
    listNewCategory: 'IHEYU_LIST_NEW_CATEGORY',
    listNews: 'IHEYU_LIST_NEWS',
    listCategory: 'IHEYU_LIST_CATEGORY',
    list: 'IHEYU_LIST_',
    getBanner: 'IHEYU_GET_BANNER',
    getWeather: 'IHEYU_GET_WEATHER',
    listServiceChildren: 'IHEYU_LIST_SERVICE_CHILDREN',
    createConversation: 'IHEYU_CREATE_CONVERSATION',
    listAskChatBot: 'IHEYU_LIST_ASK_CHAT_BOT',
    listConversation: 'IHEYU_LIST_CONVERSATION',
    listModel: 'IHEYU_LIST_MODEL',
    listChatSocioEconomic: 'IHEYU_LIST_CHAT_SOCIO_ECONOMIC',
    askSocioEconomic: 'IHEYU_ASK_SOCIO_ECONOMIC',
    createConversationSocioEconomic: 'IHEYU_CREATE_CONVERSATION_SOCIO_ECONOMIC',
    listChat: 'IHEYU_LIST_CHAT',
    ask: 'IHEYU_CHATBOT_ASK',
    listSchool: 'IHEYU_LIST_SCHOOL',
    getSchool: 'IHEYU_GET_SCHOOL',
    listTeachingCenter: 'IHEYU_LIST_TEACHING_CENTER',
    getTeachingCenter: 'IHEYU_GET_TEACHING_CENTER',
    listLawEnforcementAgency: 'IHEYU_LIST_LAW_ENFORCEMENT_ANGENCY',
    getLawEnforcementAgency: 'IHEYU_GET_LAW_ENFORCEMENT_ANGENCY',
    listLegislature: 'IHEYU_LIST_LEGISLATURE',
    getLegislature: 'IHEYU_GET_LEGISLATURE',
    listBilliards: 'IHEYU_LIST_BILLIARDS',
    getBilliards: 'IHEYU_GET_BILLIARDS',
    listKaraoke: 'IHEYU_LIST_KARAOKE',
    getKaraoke: 'IHEYU_GET_KARAOKE',
    listSportsField: 'IHEYU_LIST_SPORT_FIELD',
    getSportsField: 'IHEYU_GET_SPORT_FIELD',
    listMassage: 'IHEYU_LIST_MASSAGE',
    getMassage: 'IHEYU_GET_MASSAGE',
    listHospital: 'IHEYU_LIST_HOSPITAL',
    getHospital: 'IHEYU_GET_HOSPITAL',
    listMaternityClini: 'IHEYU_LIST_MATERNITY_CLINI',
    getMaternityClini: 'IHEYU_GET_MATERNITY_CLINI',
    listPharmacy:'IHEYU_LIST_PHARMACY',
    getPharmacy: 'IHEYU_GET_PHARMACY',
    listTypeSportsField: 'IHEYU_LIST_TYPE_SPORT_FIELD',
    listTouristAttraction: 'IHEYU_LIST_TOURIST_ATTRACTION',
    getTouristAttraction: 'IHEYU_GET_TOURIST_ATTRACTION',
    listHotel: 'IHEYU_LIST_HOTEL',
    getHotel: 'IHEYU_GET_HOTEL',
    listRestaurant: 'IHEYU_LIST_RESTAURANT',
    getRestaurant: 'IHEYU_GET_RESTAURANT',
    listFestival: 'IHEYU_LIST_FESTIVAL',
    getFestival: 'IHEYU_GET_FESTIVAL',
    listTourList: 'IHEYU_LIST_TOUR_LIST',
    getTourList: 'IHEYU_GET_TOUR_LIST',
    speechToText: 'IHEYU_SPEECH_TO_TEXT',
    deleteConversation: 'IHEYU_DELETE_CONVERSATION',
    listIntrucstion: 'IHEYU_LIST_INTRUCSTION',
    getIntrucstion: 'IHEYU_GET_INTRUCSTION',
    getConfigMaintain: 'IHEYU_GET_CONFIG_MAINTAIN',
    petitionRate: 'IHEYU_PETTION_RATE',
    checkMember: 'IHEYU_CHECK_MEMBER',
    saveConversationId: 'IHEYU_SAVE_CONVERSATION_ID',
    deleteConversationId: 'IHEYU_DELETE_CONVERSATION_ID',
    setJustLogin: 'IHEYU_SET_JUST_LOGIN',
    stopStream: 'IHEYU_STOP_STREAM',
    listOCOP: 'IHEYU_LIST_OCOP',
    getOCOP: 'IHEYU_GET_OCOP',
    listCooperative: 'IHEYU_LIST_COOPERATIVE',
    getCooperative: 'IHEYU_GET_COOPERATIVE',
    listTraditionalVillage: 'IHEYU_LIST_TRADITIONAL_VILLAGE',
    getTraditionalVillage: 'IHEYU_GET_TRADITIONAL_VILLAGE',
    listHistoricalSite: 'IHEYU_LIST_ HISTORICAL_SITE',
    getHistoricalSite: 'IHEYU_GET_HISTORICAL_SITE',
    listCulturalHeritage: 'IHEYU_LIST_CULTURA_HERITAGE',
    getCulturalHeritage: 'IHEYU_GET_CULTURA_HERITAGE',
    listCompanyTravel:'IHEYU_LIST_COMPANY_TRAVEL',
    getCompanyTravel:'IHEYU_GET_COMPANY_TRAVEL',
    levelEducation: 'IHEYU_LEVEL_EDUCATION',
    listEducation: 'IHEYU_LIST_EDUCATION',
    getEducation: 'IHEYU_GET_EDUCATION',
    listCenterLanguage: 'IHEYU_LIST_CENTER_LANGUAGE',
    getCenterLanguage: 'IHEYU_GET_CENTER_LANGUAGE',
    listCenterJob: 'IHEYU_LIST_CENTER_JOB',
    getCenterJob: 'IHEYU_GET_CENTER_JOB',
    listVaccinationFacility: 'IHEYU_LIST_VACCINATION_FACILITY',
    getVaccinationFacility: 'IHEYU_GET_VACCINATION_FACILITY',
    listNewPharmacy: 'IHEYU_LIST_NEW_PHARMACY',
    getNewPharmacy: 'IHEYU_GET_NEW_PHARMACY',
    listPressAgency: 'IHEYU_LIST_PASS_AGENCY',
    getPressAgency: 'IHEYU_GET_PASS_AGENCY',
    listTelecomService: 'IHEYU_LIST_TELECOM_SERVICE',
    getTelecomService: 'IHEYU_GET_TELECOM_SERVICE',
    listLowOffce: 'IHEYU_LIST_LOW_OFFICE',
    getLowOffce: 'IHEYU_GET_LOW_OFFICE',
    listNotaryOffice: 'IHEYU_LIST_NOTARY_OFFICE',
    getNotaryOffice: 'IHEYU_GET_NOTARY_OFFICE',
    listPublicService: 'IHEYU_LIST_PUBLIC_SERVICE',
    listProcedurePublicService: 'IHEY_LIST_PROCEDURE_PUBLIC_SERVICE',
    listServiceChatbot: 'IHEY_LIST_SERVICE_CHAT_BOT',
    listCategoryNotify: 'IHEY_LIST_CATEGORY_NOTIFY',
    listLevelAdministrativeAgency: 'IHEY_LIST_LEVEL_ADMINISTRATIVE_AGENCY',
    listStructureAdministrativeAgency: 'IHEY_LIST_STRUCTURE_ADMINISTRATIVE_AGENCY',
    listBusStation: 'IHEY_LIST_BUS_STATION',
    getBusStation: 'IHEY_GET_BUS_STATION',
    listRegistrationCenter: 'IHEY_LIST_REGISTRATION_CENTER',
    getRegistrationCenter: 'IHEY_GET_REGISTRATION_CENTER',
    listSocioEconomicCategory: 'IHEY_LIST_SOCIO_ECONOMIC_CATEGORY',
    listSocioEconomicTitle: 'IHEY_LIST_SOCIO_ECONOMIC_TITLE',
    listGovernment:'IHEYU_LIST_GOVERNMENT',
    getGovernment:'IHEYU_GET_GOVERNMENT',
    getPublicService: 'IHEYU_GET_PUBLIC_SERVICE',
    getListEvent: 'IHEYU_GET_LIST_EVENT_PUBLIC_SERVICE',
    ratingChatBot:'IHEYU_RATING_CHAT_BOT',
    getPetitionStatistic: 'IHEYU_GET_PETITION_STATISTIC',
    getAnswer: 'IHEYU_GET_ANSWER',
    listMonthSocio: 'IHEYU_LIST_MONTH_SOCIO',
    listYearSocio: 'IHEYU_LIST_YEAR_SOCIO',
    createConversationReport: 'IHEYU_CREATE_CONVERSATION_REPORT',
    getConversationReport: 'IHEYU_GET_CONVERSATION_REPORT',
    listConversationReport: 'IHEYU_LIST_CONVERSATION_REPORT',
    listChatWriteReport: 'IHEYU_LIST_CHAT_WRITE_REPORT',
    getSuggestPromptWriteReport: 'IHEYU_GET_SUGGEST_PROMPT_WRITE_REPORT',
    writeReport: 'IHEYU_WRITE_REPORT',
    attachDocument: 'IHEYU_ATTACH_DOCUMENT',
    uploadFile: 'IHEYU_UPLOAD_FILE',
    getDocument: 'IHEYU_GET_DOCUMENT',
    addDocument: 'IHEYU_ADD_DOCUMENT',
    clearDocument: 'IHEYU_CLEAR_DOCUMENT',
    listDocument: 'IHEYU_LIST_DOCUMENT',
    createConversationPlanning: 'IHEYU_CREATE_CONVERSATION_PLANNING',
    listConversationPlanning: 'IHEYU_LIST_CONVERSATION_PLANNING',
    workPlanning: 'IHEYU_WORK_PLANNING',
    getConversationPlanning: 'IHEYU_GET_CONVERSATION_PLANNING',
    listChatPlanning: 'IHEYU_LIST_CHAT_PLANNING',
    getSuggestPromptPlanning: 'IHEYU_GET_SUGGEST_PROMPT_PLANNING',
    createConversationSpeech: 'IHEYU_CREATE_CONVERSATION_SPEECH',
    listConversationSpeech: 'IHEYU_LIST_CONVERSATION_SPEECH',
    createSpeech: 'IHEYU_CREATE_SPEECH',
    getConversationSpeech: 'IHEYU_GET_CONVERSATION_SPEECH',
    listChatSpeech: 'IHEYU_LIST_CHAT_SPEECH',
    createConversationDocumentSummary: 'IHEYU_CREATE_CONVERSATION_DOCUMENT_SUMMARY',
    listConversationDocumentSummary: 'IHEYU_LIST_CONVERSATION_DOCUMENT_SUMMARY',
    createSummary: 'IHEYU_CREATE_SUMMARY',
    getConversationDocumentSummary: 'IHEYU_GET_CONVERSATION_DOCUMENT_SUMMARY',
    listChatDocumentSummary: 'IHEYU_LIST_CHAT_DOCUMENT_SUMMARY',
    updateDocument: 'IHEYU_UPDATE_DOCUMENT',
    deleteDocument: 'IHEYU_DELETE_DOCUMENT',
    downloadFile: 'IHEYU_DOWNLOAD_FILE',
    clearListDocument: 'IHEYU_CLEAR_LIST_DOCUMENT',
    clearListSharedDocuments: 'IHEYU_CLEAR_LIST_SHARED_DOCUMENTS',
    clearConversationReport: 'IHEYU_CLEAR_CONVERSATION_REPORT',
    clearConversationPlanning: 'IHEYU_CLEAR_CONVERSATION_PLANNING',
    clearConversationSpeech: 'IHEYU_CLEAR_CONVERSATION_SPEECH',
    clearConversationDocumentSummary: 'IHEYU_CLEAR_CONVERSATION_DOCUMENT_SUMMARY',
    clearDataReport: 'IHEYU_CLEAR_DATA_REPORT',
    clearDataPlanning: 'IHEYU_CLEAR_DATA_PLANNING',
    clearDataSpeech: 'IHEYU_CLEAR_DATA_SPEECH',
    clearDataDocumentSummary: 'IHEYU_CLEAR_DATA_DOCUMENT_SUMMARY',
    clearDataMakeDecision: 'IHEYU_CLEAR_DATA_MAKE_DECISION',
    clearDataSocioEconomicReport: 'IHEYU_CLEAR_DATA_SOCIO_ECONOMIC_REPORT',
    clearDataImageToText: 'IHEYU_CLEAR_DATA_IMAGE_TO_TEXT',
    clearDataVideoExcerpt: 'IHEYU_CLEAR_DATA_VIDEO_EXCERPT',
    clearConversationMakeDecision: 'IHEYU_CLEAR_CONVERSATION_MAKE_DECISION',
    clearConversationSocioEconomicReport: 'IHEYU_CLEAR_CONVERSATION_SOCIO_ECONOMIC_REPORT',
    clearConversationImageToText: 'IHEYU_CLEAR_CONVERSATION_IMAGE_TO_TEXT',
    clearConversationChatWithDoc: 'IHEYU_CLEAR_CONVERSATION_CHAT_WITH_DOC',
    clearConversationVideoExcerpt: 'IHEYU_CLEAR_CONVERSATION_VIDEO_EXCERPT',
    createConversationMakeDecision: 'IHEYU_CREATE_CONVERSATION_MAKE_DECISION',
    listConversationMakeDecision: 'IHEYU_LIST_CONVERSATION_MAKE_DECISION',
    makeDecision: 'IHEYU_MAKE_DECISION',
    getConversationMakeDecision: 'IHEYU_GET_CONVERSATION_MAKE_DECISION',
    listChatMakeDecision: 'IHEYU_LIST_CHAT_MAKE_DECISION',
    getSuggestPromptMakeDecision: 'IHEYU_GET_SUGGEST_PROMPT_MAKE_DECISION',
    createConversationSocioEconomicReport: 'IHEYU_CREATE_CONVERSATION_SOCIO_ECONOMIC_REPORT',
    listConversationSocioEconomicReport: 'IHEYU_LIST_CONVERSATION_SOCIO_ECONOMIC_REPORT',
    socioEconomicReport: 'IHEYU_SOCIO_ECONOMIC_REPORT',
    getConversationSocioEconomicReport: 'IHEYU_GET_CONVERSATION_SOCIO_ECONOMIC_REPORT',
    listChatSocioEconomicReport: 'IHEYU_LIST_CHAT_SOCIO_ECONOMIC_REPORT',
    searchUnit: 'IHEYU_SEARCH_UNIT',
    listSharedDocuments: 'IHEYU_LIST_SHARED_DOCUMENTS',
    listUnitFilter: 'IHEYU_LIST_UNIT_FILTER',
    createConversationImageToText: 'IHEYU_CREATE_CONVERSATION_IMAGE_TO_TEXT',
    listConversationImageToText: 'IHEYU_LIST_CONVERSATION_IMAGE_TO_TEXT',
    imageToText: 'IHEYU_IMAGE_TO_TEXT',
    getConversationImageToText: 'IHEYU_GET_CONVERSATION_IMAGE_TO_TEXT',
    listChatImageToText: 'IHEYU_LIST_CHAT_IMAGE_TO_TEXT',
    createConversationChatWithDoc: 'IHEYU_CREATE_CONVERSATION_CHAT_WITH_DOC',
    listConversationChatWithDoc: 'IHEYU_LIST_CONVERSATION_CHAT_WITH_DOC',
    askChatWithDoc: 'IHEYU_ASK_CHAT_WITH_DOC',
    getConversationChatWithDoc: 'IHEYU_GET_CONVERSATION_CHAT_WITH_DOC',
    listChatWithDoc: 'IHEYU_LIST_CHAT_WITH_DOC',
    getStartQuestionChatWithDoc: 'IHEYU_GET_START_QUESTION_CHAT_WITH_DOC',
    getSuggestQuestionChatWithDoc: 'IHEYU_GET_SUGGEST_QUESTION_CHAT_WITH_DOC',
    createConversationVideoExcerpt: 'IHEYU_CREATE_CONVERSATION_VIDEO_EXCERPT',
    listConversationVideoExcerpt: 'IHEYU_LIST_CONVERSATION_VIDEO_EXCERPT',
    videoExcerpt: 'IHEYU_VIDEO_EXCERPT',
    getConversationVideoExcerpt: 'IHEYU_GET_CONVERSATION_VIDEO_EXCERPT',
    listChatVideoExcerpt: 'IHEYU_LIST_CHAT_VIDEO_EXCERPT',
    saveVideoExcerpt: 'IHEYU_SAVE_VIDEO_EXCERPT',
  },

  IOCHB:{
    reportTemplates: 'IOCHB_REPORT_TEMPLATES',
    reportCreate: 'IOCHB_REPORT_CREATE',
    reportList: 'IOCHB_REPORT_LIST',
    attendanceCheckin: 'IOCHB_ATTENDANCE_CHECKIN',
    attendanceHistory: 'IOCHB_ATTENDANCE_HISTORY',
    attendanceStatus: 'IOCHB_ATTENDANCE_STATUS',
    attendanceStatistic: 'IOCHB_ATTENDANCE_STATISTIC',
    getWorkSchedule: 'IOCHB_GET_WORK_SCHEDULE',
    leaveRequestList: 'IOCHB_LEAVE_REQUEST_LIST',
    leaveRequestCreate: 'IOCHB_LEAVE_REQUEST_CREATE',
    leaveRequestDetail: 'IOCHB_LEAVE_REQUEST_DETAIL',
    listStatisticOnDutyOfficer: 'IOCHB_LIST_STATISTIC_ON_DUTY_OFFICER',
    listStatisticTotalOfficer: 'IOCHB_LIST_STATISTIC_TOTAL_OFFICER',
    listStatisticAttendance: 'IOCHB_LIST_STATISTIC_ATTENDANCE',
    listAttendanceStatistic: 'IOCHB_LIST_ATTENDANCE_STATISTIC',
    listStatisticDocumentSummary: 'IOCHB_LIST_STATISTIC_DOCUMENT_SUMMARY',
    listStatisticReportSummary: 'IOCHB_LIST_STATISTIC_REPORT_SUMMARY',
    listStatisticReportByArea: 'IOCHB_LIST_STATISTIC_REPORT_BY_AREA',
    listGroupedSchedule: 'IOCHB_LIST_GROUPED_SCHEDULE',
    listArea: 'IOCHB_LIST_AREA',
    listReportIncidentHighlight: 'IOCHB_LIST_REPORT_INCIDENT_HIGHLIGHT',
    detailReportDocumentSummary: 'IOCHB_DETAIL_REPORT_DOCUMENT_SUMMARY',
    detailReportIdentitySummary: 'IOCHB_DETAIL_REPORT_IDENTITY_SUMMARY',
    detailReportProtectionSummary: 'IOCHB_DETAIL_REPORT_PROTECTION_SUMMARY',
    detailReportLicensePlateSummary: 'IOCHB_DETAIL_REPORT_LICENSE_PLATE_SUMMARY',
    reportStatistics: 'IOCHB_REPORT_STATISTICS',
    statusOverView: 'IOCHB_STATUS_OVER_VIEW',
    checkinSuddenAttendance: 'IOCHB_CHECKIN_SUDDEN_ATTENDANCE',
    listCategoryReportByArea: 'IOCHB_LIST_CATEGORY_REPORT_BY_AREA',
    listReportByCategory: 'IOCHB_LIST_REPORT_BY_CATEGORY',
    listCategoryStatistic: 'IOCHB_LIST_CATEGORY_STATISTIC',
    clearListReportByCategory: 'IOCHB_CLEAR_LIST_REPORT_BY_CATEGORY',
    clearListCategoryReportByArea: 'IOCHB_CLEAR_LIST_CATEGORY_REPORT_BY_AREA',
    listLatestIncident: 'IOCHB_LIST_LATEST_INCIDENT',
    listReportOtherIncident: 'IOCHB_LIST_REPORT_OTHER_INCIDENT',
    getReport: 'IOCHB_GET_REPORT',
    reportUpdate: 'IOCHB_REPORT_UPDATE',
    handbookCategory: 'IOCHB_HAND_BOOK_CATEGORY',
    listHandbook: 'IOCHB_LIST_HAND_BOOK',
    getHandbook: 'IOCHB_GET_HAND_BOOK',
    listMission: 'IOCHB_LIST_MISSION',
    getMission: 'IOCHB_GET_MISSION',
    acceptMission: 'IOCHB_ACCEPT_MISSION',
    rejectMission: 'IOCHB_REJECT_MISSION',
    startMission: 'IOCHB_START_MISSION',
    listUnit: 'IOCHB_LIST_UNIT',
    createMission: 'IOCHB_CREATE_MISSION',
    updateMission: 'IOCHB_UPDATE_MISSION',
    listPersonnel: 'IOCHB_LIST_PERSONNEL',
    clearListPersonnel: 'IOCHB_CLEAR_LIST_PERSONNEL',
    assignOfficer: 'IOCHB_ASSIGN_OFFICER',
    notifyMission: 'IOCHB_NOTIFY_MISSION',
    cancelMission: 'IOCHB_CANCEL_MISSION',
    getMissionLog: 'IOCHB_GET_MISSION_LOG',
    clearListMission: 'IOCHB_CLEAR_LIST_MISSION',
    completeMission: 'IOCHB_COMPLETE_MISSION',
    updateNumberOfUser: 'IOCHB_UPDATE_NUMBER_OF_USER',
  },

  AppState:{
    getConfigAnalytics: 'APP_GET_CONFIG_ANALYTICS',
    sendCrashLog: 'APP_SEND_CRASH_LOG',
    getConfigReview: "APP_GET_CONFIG_REVIEW",
    getConfigForUpdateLocation: 'APP_GET_CONFIG_FOR_UPDATE_LOCATION',
    getConfigForBackgroundLocation: 'APP_GET_CONFIG_FOR_BACKGROUND_LOCATION',
    getApiKeyGoogle: 'APP_GET_API_KEY_GOOGLE',
    getContact: 'APP_GET_CONTACT',
    showLoading: 'APP_STATE_SHOW_LOADING',
    listRegion: 'APP_STATE_LIST_REGION',
    getConfig: 'APP_STATE_GET_CONFIG',
    reportLocation: 'APP_STATE_REPORT_LOCATION',
    set: 'APP_STATE_SET',
    setDirect : 'APP_STATE_DIRECT_SET',
    constants:{
      APP_STATE_LIST:{
        LOADING:'LOADING',
        RUNNING:'RUNNING',
      },
      APP_STATE_DIRECT_LIST:{
        PORTRAIT:'PORTRAIT',
        LANDSCAPE:'LANDSCAPE',
        UNKNOWN: 'UNKNOWN'
      },
    },
    setInstanceMode: 'APP_STATE_SET_INSTANCE_MODE',
    getConfigPhoneAuthen: 'APP_GET_CONFIG_PHONE_AUTHEN',
    getMessageWarningAuthen: 'APP_GET_MESSSAGE_WARNING_AUTHEN',
    listServiceAvailable: 'APP_LIST_SERVICE_AVAILABLE',
    getPromotionCategory: 'APP_GET_PROMOTION_CATEGORY',
    getPromotionHeyU: 'APP_GET_PROMOTION_HEYU',
    getNewsCategory: 'APP_GET_NEWS_CATEGORY',
    getNews: 'APP_GET_NEWS',
    getDetailNews: 'APP_GET_DETAIL_NEWS',
    getBanner: 'APP_GET_BANNER',
    getConfigForMount: 'APP_GET_CONFIG_MOUNT',
    getConfigHistoryFacebook: 'APP_GET_CONFIG_HISTORY_FACEBOOK',
    getListTopUpAddress: 'APP_LIST_TOP_UP_ADDRESS',
    getConfigForTag: 'APP_GET_CONFIG_FOR_TAG',
    getConfigLogin: 'APP_GET_CONFIG_LOGIN',
    getHotlineBookCar: 'APP_GET_HOTLINE_BOOK_CAR',
    getStateMachineRider: 'APP_GET_STATE_MACHINE_RIDER',
    getConfigMaxServiceCharge: 'APP_GET_CONFIG_MAX_SERVICE_CHARGE',
    getConfigChangePhone: 'APP_GET_CONFIG_CHANGE_PHONE',
    getConfigReferralCode: 'APP_GET_CONFIG_REFERRAL_CODE',
    getConfigShowPaymentInapp: 'APP_GET_CONFIG_SHOW_PAYMENT_INAPP',
    getConfigAuthenShipperOnline: 'APP_GET_CONFIG_AUTHEN_SHIPPER_ONLINE',
    getConfigShowCovidPassport: 'APP_GET_CONFIG_SHOW_COVID_PASSPORT',
    getConfigShowNews: 'APP_GET_CONFIG_SHOW_NEWS',
    getConfigShortcutService: 'APP_GET_CONFIG_SHORTCUT_SERVICE',
    configShowStore: 'APP_GET_CONFIG_SHOW_STORE',
    configRoutingShop: 'APP_GET_CONFIG_ROUTING_SHOP',
    configFrameBanner: 'APP_GET_CONFIG_FRAME_BANNER',
    getConfigGuide: 'APP_GET_CONFIG_GUIDE',
    getConfigRegion: 'APP_GET_CONFIG_REGION',
    getRegionByLatLng: 'APP_GET_REGION',
    setStatusBarColor: 'APP_SET_STATUS_BAR_COLOR',
    setFooterColor: 'APP_SET_FOOTER_COLOR',
    autoComplete:'APP_AUTO_COMPLETE',
    placeDetail:'APP_PLACE_DETAIL',
  },

  AppSetting:{
    setOpenToolTipFirstTime: 'APP_SET_OPEN_TOOLTIP_FIRST_TIME',
    getRegionByLatLng: 'APP_GET_REGION_BY_LATLNG',
    setTipFeedsSave:'APP_SET_TIP_FEEDS_SAVED',
    increaseHintPickLocation: 'APP_INCREATE_HINT_PICK_LOCATION',
    increaseHintOrder: 'APP_INCREASE_HINT_ORDER',
    increaseTipsCash:'APP_INCREASE_TIPS_CASH',
    increaseTipCart:'APP_INCREASE_TIP_CART',
    saveSetting:'APP_SETTING_SAVE',
    setQuickReceiverMess:'APP_SETTING_QUICK_RECEIVER_MESS',
    setNumberFeedsCache:'APP_SETTING_NUMBER_FEEDS_CACHE',
    setNumberFeedsDisplay:'APP_SETTING_NUMBER_FEEDS_DISPLAY',
    setSearchOptions:'APP_SETTING_SEARCH_OPTIONS',
    setShowAllLineFeed:'APP_SETTING_SHOW_ALL_LINE_FEED',
    setKeepScreenOn:'APP_SETTING_KEEP_SCREEN_ON',
    setNewFlags:'APP_SETTING_NEW_FLAGS',
    setMode: 'APP_SET_MODE',
    setRegion: 'APP_SET_REGION',
    setModeOpenFb: 'APP_SET_MODE_OPEN_FB',
    addDefaultOrigin: 'APP_ADD_DEFAULT_ORIGIN',
    deleteDefaultOrigin: 'APP_DELETE_DEFAULT_ORIGIN',
    modifyDefaultOrigin: 'APP_MODIFY_DEFAULT_ORIGIN',
    setActiveDefaultOrigin: 'APP_SET_ACTIVE_DEFAULT_LOCATION',
    setDoneSyncDefaultLocation: 'APP_SET_DONE_SYNC_DEFAULT_LOCATION',
    listDefaultLocation: 'APP_LIST_DEFAULT_LOCATION',
    modifyListDefaultLocation: 'APP_MODIFY_LIST_DEFAULT_LOCATION',
    unshiftDefaultLocation: 'APP_UNSHIFT_DEFAULT_LOCATION',
    showOnlyFacebookOrder: 'APP_SHOW_ONLY_FACEBOOK_ORDER',
    showTotalPosts: 'APP_SHOW_TOTAL_POSTS',
    setActionOrderOnpress:'APP_SET_ACTION_ORDER_ONPRESS',
    switchSoundOS: 'APP_SET_SWITCH_SOUND_OS',
    constants:{
      APP_REGION_LIST:{
        'HÀ NỘI':'hn',
        // 'HỒ CHÍ MINH':'hcm'
      }
    },
    increaseTipsImageOrder: 'APP_INCREASE_TIPS_IMAGE_ORDER',
    addRecentlyLocationPick: 'APP_RECENTLY_LOCATIONS',
    addRecentlyLocationBikePick: 'APP_RECENTLY_LOCATIONS_BIKE',
    addRecentlyLocationHeyCarePick: 'APP_RECENTLY_LOCATIONS_HEY_CARE',
    addRecentlyOriginPick: 'APP_RECENTLY_ORIGIN',
    removeRecentlyOriginPick: 'APP_REMOVE_RECENTLY_ORIGIN',
    removeRecentlyLocationPick: 'APP_REMOVE_RECENTLY_LOCATIONS',
    removeRecentlyLocationBikePick: 'APP_REMOVE_RECENTLY_LOCATIONS_BIKE',
    removeRecentlyLocationHeyCarePick: 'APP_REMOVE_RECENTLY_LOCATIONS_HEY_CARE',
    changeAuthorizationStatus: 'APP_CHANGE_AUTHORIZATION_STATUS',
    chooseActiveService: 'APP_CHOOSE_ACTIVE_SERVICE',
    chooseActiveServiceForShip: 'APP_CHOOSE_ACTIVE_SERVICE_SHIPPER',
    checkPopupPermissionNotify: 'APP_CHECK_SHOW_PERMISSION',
    checkPopupNativeNotify: 'APP_CHECK_SHOW_PERMISSION_NOTI',
    recentSearchProduct: 'APP_GET_RECENT_SEARCH_PRODUCT',
    addCurrentLocation: 'APP_ADD_CURRENT_LOCATION',
    changeTagStore: 'APP_CHANGE_TAG_STORE',
    settingShowStore: 'APP_SETTING_SHOW_STORE',
    addListOrderWait: 'APP_ADD_LIST_ORDER_WAIT',
    removeListOrderWait: 'APP_REMOVE_LIST_ORDER_WAIT',
    addSenderInf: 'APP_ADD_SENDER_INF',
    changeProductDisplay: 'APP_CHANGE_PRODUCT_DISPLAY',
    addListGoods: 'APP_ADD_LIST_GOODS',
    addListErrandGoods: 'APP_ADD_LIST_ERRAND_GOODS',
    addNewGoods: 'APP_ADD_NEW_GOODS',
    addNewErrandGoods: 'APP_ADD_NEW_ERRAND_GOODS',
    changeUpdatedLocation: 'APP_CHANGE_UPDATED_LOCATION',
    messageChatBot: 'APP_MESSAGE_CHATBOT_IHEYU',
    recentStuff: 'RECENT_STUFF_IHEYU',
  },
  Package: {
    list: 'PACKAGE_LIST'
  },
  User: {
    get: 'USER_GET',
    requireLogin: 'USER_REQUIRE_LOGIN',
    login: 'USER_LOGIN',
    logout: 'USER_LOGOUT',
    register: 'USER_REGISTER',
    changePassword: 'USER_CHANGE_PASSWORD',
    resetPassword: 'USER_RESET_PASSWORD',
    checkOTP: 'USER_CHECK_OTP',
    sendOTP: 'USER_SEND_OTP',
    updateProfile: 'USER_UPDATE_PROFILE',
    saveDraftPetition: 'USER_SAVE_DRAFT_PETITION',
    updateDraftPetition: 'USER_UPDATE_DRAFT_PETITION',
    removeDraftPetition: 'USER_REMOVE_DRAFT_PETITION',
    sentNotifyToken:'USER_SENT_NOTIFY_TOKEN',
    getConfigRemoveAccount: 'USER_GET_CONFIG_REMOVE_ACCOUNT',
    sendFeedback: 'USER_SEND_FEEDBACK',
    trackingAction: 'USER_TRACKING_ACTION',
    setToolTipCount: 'USER_SET_TOOLTIP_COUNT',
  },

  Chat: {
    listHistory: 'CHAT_LIST_HISTORY',
    getConversationInf: 'CHAT_GET_CONVERSATION_INF',
    getHistory: 'CHAT_GET_HISTORY',
    newMessage: 'CHAT_NEW_MESSAGE',
    typingMessage: 'CHAT_TYPING_MESSAGE',
    getHistoryByConversation: 'CHAT_GET_MESSAGE_BY_CONVERSATION',
    getHistoryByUser: 'CHAT_GET_MESSAGE_BY_USER',
    sendMessage: 'CHAT_SEND_MESSAGE',
    reSendMessage: 'CHAT_RESEND_MESSAGE',
    seenMessage: 'CHAT_SEEN_MESSAGE',
    clear: 'CHAT_CLEAR',
    clearMessages: 'CHAT_CLEAR_MESSAGES'
  },

  Feeds:{
    get:'FEEDS_GET',
    getViaEvent: 'FEED_GET_VIA_EVENT',
    getFeedsSaved: 'FEEDS_GET_SAVED',
    save:'FEEDS_SAVE',
    deleteFeedSaved: 'FEEDS_DELETE_SAVED',
    deleteAllFeedsSaved: 'FEEDS_DELETE_ALL',
    notify:'FEEDS_NOTIFY',
    comment:'FEEDS_COMMENT',
    getComments: 'FEEDS_GET_COMMENT',
    createOrder: 'FEED_CREATE_NEW',
    like:'FEED_LIKE_USER',
    dislike:'FEED_DISLIKE_USER',
    getHistoryOrder: 'FEED_GET_HISTORY',
    getTotalPostsByTypes: 'FEED_GET_TOTAL_POST_BY_TYPES',
    clear: 'FEEDS_CLEAR',
    getStatisticsByWeek:'FEEDS_GET_STATISTICS_BY_WEEK',
    getStatisticsByDay:'FEEDS_GET_STATISTICS_BY_DAY',
    getStatisticsByMonth:'FEEDS_GET_STATISTICS_BY_MONTH',
    getFinalTime:'FEES_GET_FINAL_TIME',
    clearNewFeed: 'FEEDS_CLEAR_NEW_FEED',
    getConfigFeedFacebook: 'FEEDS_GET_CONFIG_FEED_FACEBOOK'
  },

  FeedsSystem: {
    listGroupOrderType: 'FEED_SYSTEM_LIST_GROUP_ORDER_TYPE',
    optimiseRoute: 'FEED_SYSTEM_OPTIMISE_ROUTE',
    getCurrentOrder: 'FEED_SYSTEM_GET_CURRENT_ORDER',
    create: 'FEED_SYSTEM_CREATE',
    getOrderType: 'FEED_SYSTEM_GET_ORDER_TYPE',
    get: 'FEED_SYSTEM_GET',
    getForShipper: 'FEED_SYSTEM_GET_FOR_SHIPPER',
    getForShop: 'FEED_SYSTEM_GET_FOR_SHOP',
    take: 'FEED_SYSTEM_TAKE',
    updateStatus: 'FEED_SYSTEM_UPDATE',
    list: 'FEED_SYSTEM_LIST',
    listForShop: 'FEED_SYSTEM_LIST_FOR_SHOP',
    getDistanceMoney: 'FEED_SYSTEM_DISTANCE_MONEY',
    retry: 'FEED_SYSTEM_RETRY',
    rejectForShop: 'FEED_SYSTEM_REJECT_SHOP',
    rejectForShipper: 'FEED_SYSTEM_REJECT_SHIPPER',
    rejectPushOrder: 'FEED_SYSTEM_REJECT_PUSH_ORDER',
    modify: 'FEED_SYSTEM_MODIFY',
    getLocationName: 'FEED_SYSTEM_GET_LOCATION_NAME',
    getConfigPlaceDetail: 'FEED_SYSTEM_GET_CONFIG_PLACE_DETAIL',
    trackCall: 'FEED_SYSTEM_TRACK_CALL',
    trackCallShop: 'FEED_SYSTEM_TRACK_CALL_SHOP',
    trackMessage: 'FEED_SYSTEM_TRACK_MESSAGE',
    trackMessageShop: 'FEED_SYSTEM_TRACK_MESSAGE_SHOP',
    notifyDone: 'FEED_SYSTEM_NOTIFY_DONE',
    notifyTake: 'FEED_SYSTEM_NOTIFY_TAKE',
    currentShipperReceivingPush: 'FEED_SYSTEM_CURRENT_SHIPPER',
    listGuideOrder: 'FEED_SYSTEM_LIST_GUIDE',
    listQuestionOrder: 'FEED_SYSTEM_LIST_QUESTION',
    clear: 'FEED_SYSTEM_CLEAR',
    clearOrderSeaching: 'FEED_SYSTEM_CLEAR_ORDER_SEARCHING',
    getStatisticsByWeek:'FEED_SYSTEM_GET_STATISTICS_BY_WEEK',
    getStatisticsByDay:'FEED_SYSTEM_GET_STATISTICS_BY_DAY',
    getStatisticsByMonth:'FEED_SYSTEM_GET_STATISTICS_BY_MONTH',
    getFinalTime:'FEED_SYSTEM_GET_FINAL_TIME',
    getHistory:'FEED_SYSTEM_GET_HISTORY',
    getStatisticsOrder:'FEED_SYSTEM_GET_STATISTICS_ORDER',
    listOrderSearching:'FEED_SYSTEM_LIST_ORDER_SEARCHING',
    doneOrder:'FEED_SYSTEM_DONE_ORDER',
    newOrder:'FEED_SYSTEM_NEW_ORDER',
    listReasonsCancel:'FEED_SYSTEM_LIST_REASONS_CANCEL',
    sendReasonCancel:'FEED_SYSTEM_SEND_REASON_CANCEL',
    changeReceiverInfo: 'FEED_SYSTEM_CHANGE_RECEIVER_INFO',
    ratingStar: 'FEED_SYSTEM_RATING_STAR',
    rating: 'FEED_SYSTEM_RATING',
    listRatingReason: 'FEED_SYSTEM_LISTRATINGREASON',
    listRatedReasons: 'FEED_SYSTEM_LISTRATEDREASONS',
    listRanking: 'FEED_SYSTEM_LISTRANKING',
    getCurrentRank: 'FEED_SYSTEM_GETCURRENTRANK',
    listRestaurant: 'FEED_SYSTEM_LISTRESTAURANT',
    menuType: 'FEED_SYSTEM_MENUTYPE',
    listMenu: 'FEED_SYSTEM_LISTMENU',
    searchRestaurant: 'FEED_SYSTEM_SEARCHRESTAURANT',
    getRestaurant: 'FEED_SYSTEM_GET_RESTAURANT',
    getSelectedDishes: 'FEED_SYSTEM_GET_SELLECTED_DISHES',
    listInstanceOrder: 'FEED_SYSTEM_LIST_INSTANCE_ORDER',
    getPromote: 'FEED_SYSTEM_GET_PROMOTE',
    getPromoteCodeById: 'FEED_PROMOTE_CODE_BY_ID',
    listServiceAvailable: 'FEED_SYSTEM_LIST_SERVICE_AVAILABLE',
    listAvaiableRanking: 'FEED_SYSTEM_LIST_AVAIABLE_RANKING',
    statisticByDay: 'FEED_SYSTEM_STATISTIC_BY_DAY',
    statisticByWeek: 'FEED_SYSTEM_STATISTIC_BY_WEEK',
    statisticByMonth: 'FEED_SYSTEM_STATISTIC_BY_MONTH',
    listReport: 'FEED_SYSTEM_LIST_REPORT',
    report: 'FEED_SYSTEM_REPORT',
    getUnratedOrder: 'FEED_SYSTEM_GET_UNRATED_ORDER',
    getConfigVat: 'FEED_SYSTEM_GET_CONFIG_VAT',
    getConfigOrder: 'FEED_SYSTEM_GET_CONFIG_ORDER',
    getConfigForCreatedOrders: 'FEED_SYSTEM_GET_CONFIG_FOR_CREATED_ORDERS',
    getNewOrderMessage: 'FEED_SYSTEM_GET_NEW_ORDER_MESSAGE',
    checkNewOrderMessage: 'FEED_SYSTEM_CHECK_NEW_ORDER_MESSAGE',
    seenNewMessageOrder: 'FEED_SYSTEM_SEEN_NEW_ORDER_MESSAGE',
    getMarkUniform: 'GET_MARK_UNIFORM',
    getFeeCOD: 'FEED_SYSTEM_GET_FEE_COD',
    getChangedOrdersForShop: 'FEED_SYSTEM_GET_CHANGED_ORDERS_FOR_SHOP',
    getChangedOrdersForShipper: 'FEED_SYSTEM_GET_CHANGED_ORDERS_FOR_SHIPPER',
    getLatestLocation: 'FEED_SYSTEM_GET_LATEST_LOCATION',
    getCurrentState: 'FEED_SYSTEM_GET_CURRENT_STATE',
    calculateFeeVat: 'FEED_SYSTEM_CALCULATE_FEE_VAT',
    calculateMoneyFromDistance: 'FEED_SYSTEM_CALCULATE_MONEY_FROM_DISTANCE',
    uploadImageOrder: 'FEED_SYSTEM_UPLOAD_IMAGE_ORDER',
    uploadBill: 'FEED_SYSTEM_UPLOAD_BILL',
    addImageOrder: 'FEED_SYSTEM_ADD_IMAGE_ORDER',
    listPromote: 'FEED_SYSTEM_LIST_PROMOTE',
    countPromote: 'FEED_SYSTEM_COUNT_PROMOTE',
    getConfigImageOrderForShop: 'FEED_SYSTEM_GET_CONFIG_IMAGE_ORDER_FOR_SHOP',
    ratingUniform: 'FEED_SYSTEM_RATING_UNIFORM',
    listRatingUniformReason: 'FEED_SYSTEM_LISTRATINGUNIFORMREASON',
    getConfigCOD: 'FEED_SYSTEM_GET_CONFIG_COD',
    getQRcode: 'FEED_SYSTEM_GET_QR_CODE',
    registerMerchant: 'REGISTER_MERCHANT',
    getMerchant: 'GET_MERCHANT',
    getOrderTypeErrand: 'FEED_SYSTEM_GET_ORDER_TYPE_ERRAND',
    listErrandJob: 'FEED_SYSTEM_LIST_ERRAND',
    getOrderTypeFood: 'FEED_SYSTEM_GET_ORDER_TYPE_FOOD',
    getConfigWeight: 'FEED_SYSTEM_GET_CONFIG_WEIGHT',
    addProcessingOrder: 'FEED_SYSTEM_ADD_PROCESSING_ORDER',
    getCurrentStateBike: 'BIKE_GET_CURRENT_STATE',
    getLimitDeposit: 'FEED_SYSTEM_GET_LIMIT_DEPOSIT',
    listCategoryStore: 'FEED_SYSTEM_LIST_CATEGORY_STORE',
    listStore: 'FEED_SYSTEM_LIST_STORE',
    getLinkOrder: 'FEED_SYSTEM_GET_LINK_ORDER',
    trackLinkOrder: 'FEED_SYSTEM_TRACK_LINK_ORDER',
    productType: 'FEED_SYSTEM_PRODUCT_TYPE',
    listProduct: 'FEED_SYSTEM_LIST_PRODUCT',
    listProductTypeDefault: 'FEED_SYSTEM_LIST_PRODUCT_TYPE_DEFAULT',
    listNewStore: 'FEED_SYSTEM_LIST_NEW_STORE',
    listStoreWithProduct: 'FEED_SYSTEM_LIST_STORE_WITH_PRODUCT',
    findPageProduct: 'FEED_SYSTEM_FIND_PAGE_PRODUCT',
    getStore: 'FEED_SYSTEM_GET_STORE',
    updatePath: 'FEED_SYSTEM_UPDATE_PATH',
    getConfigErrandFee: 'FEED_SYSTEM_GET_CONFIG_ERRAND_FEE',
    getBannerStore: 'FEED_SYSTEM_GET_BANNERS_STORE',
    listProductViaType: 'FEED_SYSTEM_LIST_PRODUCT_VIA_TYPE',
    listReasonsForMerchant: 'FEED_SYSTEM_LIST_REASONS_FOR_MERCHANT',
    getConfigCurrentLocation: 'FEED_SYSTEM_GET_CONFIG_CURRENT_LOCATION',
    getConfigChangeLocation: 'FEED_SYSTEM_GET_CONFIG_CHANGE_LOCATION',
    getConfigSMSReceiver: 'FEED_SYSTEM_GET_CONFIG_SMS_RECEIVER',
    getConfigBlockRider: 'FEED_SYSTEM_GET_CONFIG_BLOCK_RIDER',
    blockRider: 'FEED_SYSTEM_BLOCK_RIDER',
    getDirection: 'FEED_SYSTEM_GET_DIRECTION',
    getConfigTipRealtime: 'FEED_SYSTEM_GET_CONFIG_TIP_REALTIME',
    tipRealtime: 'FEED_SYSTEM_TIP_REALTIME',
    getConfigFastDelivery: 'FEED_SYSTEM_GET_CONFIG_DELIVERY',
    configAuthenShop: 'FEED_SYSTEM_GET_CONFIG_AUTHEN_SHOP',
    setAuthenticateLater: 'FEED_SYSTEM_SET_AUTHENTICATE_LATER',
    configRegionMoving: 'FEED_SYSTEM_GET_CONFIG_REGION_MOVING',
    getConfigBoundaries: 'FEED_SYSTEM_GET_BOUNDARIES',
    getMessageReject: 'FEED_SYSTEM_GET_MESSAGE_REJECT',
    getConfigStrategyOrigin: 'FEED_SYSTEM_GET_CONFIG_STRATEGY_ORIGIN',
    getConfigFragile: 'FEED_SYSTEM_GET_CONFIG_FRAGILE',
    getConfigHandDelivery: 'FEED_SYSTEM_GET_CONFIG_HAND_DELIVERY',
    getConfigTipAfterOrderDone: 'FEED_SYSTEM_GET_CONFIG_TIP_AFTER_ORDER_DONE',
    tipAfterOrderDone: 'FEED_SYSTEM_TIP_AFTER_ORDER_DONE',
    getConfigMap:'FEED_SYSTEM_GET_CONFIG_MAP'
  },

  Notify: {
    add: 'NOTIFY_ADD',
    remove: 'NOTIFY_REMOVE',
    removeAllNotify: 'REMOVE_ALL'
  },

  Notifications: {
    add: 'NOTIFICATION_ADD',
    removeAllNotify: 'NOTIFICATION_REMOVE_ALL',
    notify: 'NOTIFICATION_NOTIFY',
    updateSeen: 'NOTIFICATION_UPDATE_SEEN',
    countNotify: 'NOTIFICATION_COUNT',
    updateCount: 'NOTIFICATION_UPDATE_COUNT',
    seenNotify: 'NOTIFICATION_SEEN',
    seenAllNotify: 'NOTIFICATION_SEEN_ALL',
    getDetailNotification: 'NOTIFICATION_GET_DETAIL',
    clear: 'NOTIFICATION_CLEAR',
    refreshNotify: 'NOTIFICATION_REFRESH_NOTIFY',
    getHotNew: 'NOTIFICATION_GET_HOT_NEW',
  },

  Shipper: {
    getNearest: 'SHIPPER_GET_NEAREST',
    clear: 'CLEAR_SHIPPER_NEAREST'
  },

  //
  constants:{
    REQUEST_SUBTYPE:{
      REQUEST:'REQUEST',
      ERROR:'ERROR',
      SUCCESS:'SUCCESS',
    },
  }

}

module.exports= RDActionsTypes