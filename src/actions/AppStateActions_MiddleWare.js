var _ = require('lodash')
import {
  Platform
} from 'react-native';
var DeviceInfo = require('react-native-device-info');

var _ = require('lodash')
var RDActionsTypes = require('./RDActionsTypes');
import RDActions_MiddleWare from './RDActions_MiddleWare'
import axios from 'axios';
import async from 'async';

// LIB

// components
var Define = require('../Define');
var Debug = require('../Util/Debug');
var Util = require('../Util/Util');
var RDActions = require('./RDActions');
var {globalVariableManager}= require('../components/modules/GlobalVariableManager');

// NOTE : stuck when call getState (when dispatch another action in a action)

/*
 * action creators
 */

class AppStateActions_MiddleWare extends RDActions_MiddleWare {
  constructor(){
    super('AppStateActions_MiddleWare',true);
    this.init();
  }
  actionsList={
    getConfigAnalytics: {
      query:'/app/get-config-analytics',
      argFormat:{
      },
      argMap:{},
      limitProcess:1,
      apiVersion:Define.constants.apiVersion,
      onArg:(arg,getState)=>{
        return arg;
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    sendCrashLog: {
      query:'/app/send-crash-log',
      serverAddr: Define.constants.serverIHeyU,
      argFormat:{
      },
      argMap:{},
      limitProcess:1,
      apiVersion: 1,
      onArg:(arg,getState)=>{
        return {
          device: {
            platform: Platform.OS,
            uniqueId: Define.constants.deviceId,
            deviceManufacturer: Define.constants.deviceManufacturer,
            deviceBrand: DeviceInfo.getBrand(),
            deviceModel: DeviceInfo.getModel(),
            deviceId: DeviceInfo.getDeviceId(),
            systemName: DeviceInfo.getSystemName(),
            systemVersion: DeviceInfo.getSystemVersion(),
            bundleId: DeviceInfo.getBundleId(),
            appBuild: DeviceInfo.getBuildNumber(),
            appVersion: DeviceInfo.getVersion(),
            readableVersion: DeviceInfo.getReadableVersion(),
            deviceName: Define.constants.deviceName,
            userAgent: Define.constants.userAgent,
            versionCodePush: Define.constants.versionCodePush,
          },
          ...arg
        }
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getConfigReview: {
      query:'/app/get-config-payment',
      argFormat:{
      },
      argMap:{},
      limitProcess:2,
      apiVersion:Define.constants.apiVersion,
      onArg:(arg,getState)=>{
        return arg;
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getConfigForUpdateLocation: {
      query:'/app/get-config-for-update-location',
      argFormat:{
      },
      argMap:{},
      limitProcess:1,
      apiVersion:Define.constants.apiVersion,
      onArg:(arg,getState)=>{
        return {
          ...arg
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getConfigForBackgroundLocation: {
      query:'/app/get-config-for-background-location',
      argFormat:{
      },
      argMap:{},
      limitProcess:1,
      apiVersion:'2.1',
      onArg:(arg,getState)=>{
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getApiKeyGoogle: {
      query:'/app/get-api-key-google-map',
      argFormat:{
      },
      argMap:{},
      limitProcess:1,
      apiVersion:Define.constants.apiVersion,
      onArg:(arg,getState)=>{
        return {
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getContact: {
      query:'/app/get-contact',
      argFormat:{
      },
      argMap:{},
      limitProcess:1,
      apiVersion:'2.1',
      onArg:(arg,getState)=>{
        return {
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    reportLocation: {
      query:'/app/report-location',
      argFormat:{
        platform:''
      },
      argMap:{},
      limitProcess:1,
      apiVersion:Define.constants.apiVersion,
      onArg:(arg,getState)=>{
        return {
          message: arg.message,
          feed_id: arg.id,
          nameLocation: arg.nameLocation
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    listRegion: {
      query:'/app/region/list',
      argFormat:{
        memberToken:''
      },
      argMap:{},
      limitProcess:1,
      showLoading: true,
      apiVersion:Define.constants.apiVersion,
      onArg:(arg,getState)=>{
        return {
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getConfigPhoneAuthen: {
      query:'/app/get-config-for-phone-athentication',
      argMap:{},
      limitProcess:1,
      showLoading: true,
      apiVersion:Define.constants.apiVersion,
      onArg:(arg,getState)=>{
        return {
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    },
    getMessageWarningAuthen: {
      query: '/app/get-message-warning-authentication',
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: 2,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    listServiceAvailable: {
      query: '/app/list-service-available',
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: '2.2',
      limitProcess: 3,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', ''),
          newDelivery: 1
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getPromotionCategory: {
      query: '/app/get-promotion-category',
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: '2.1',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getPromotionHeyU: {
      query: '/app/get-promotion-heyu',
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: '2.1',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getNewsCategory: {
      query: '/app/get-news-category',
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: '2.1',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getNews: {
      query: '/app/get-news',
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: '2.1',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getDetailNews: {
      query: '/app/get-detail-news',
      argFormat: {},
      argMap: {},
      showLoading: true,
      apiVersion: Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getBanner: {
      query: '/app/get-banner',
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: '2.1',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getConfigForMount: {
      query: '/app/get-config-for-mount',
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: '2.1',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getConfigShowNews: {
      query: '/app/get-config-show-news',
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: '2.1',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getConfigHistoryFacebook: {
      query: '/app/get-config-history-facebook',
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: 2,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getListTopUpAddress: {
      query: '/app/list-topup-address',
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: 2,
      limitProcess: 1,
      showLoading: true,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getConfigForTag: {
      query: '/app/get-config-tag',
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: Define.constants.apiVersion,
      limitProcess: 2,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getConfigLogin: {
      query: '/app/get-config-login',
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getHotlineBookCar: {
      query: '/app/get-config-hotline',
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: '1.0',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getStateMachineRider: {
      query: '/app/get-state-machine-rider',
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: '2.0',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getConfigMaxServiceCharge: {
      query: '/app/get-config-show-max-service-charge',
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getConfigChangePhone: {
      query: '/app/get-config-change-phone',
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getConfigReferralCode: {
      query: '/app/get-config-referral-code',
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getConfigShowPaymentInapp: {
      query: '/app/get-config-show-payment-inapp',
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getConfigAuthenShipperOnline: {
      query: '/app/get-config-shipper-authen-online',
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getConfigShowCovidPassport: {
      query: '/app/get-config-covid_passport',
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: 2,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getConfigShortcutService: {
      query: '/app/get-config-shortcut-service',
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    configShowStore: {
      query: '/order/config-show-store',
      serverAddr: Define.constants.serverStoreAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    configRoutingShop: {
      query: '/app/config-routing-shop',
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    configFrameBanner: {
      query: '/app/config-image-frame-banner',
      serverAddr: Define.constants.serverAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getConfigGuide: {
      query: '/app/config-guide',
      serverAddr: Define.constants.serverAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getConfigRegion: {
      query: '/app/get-config-region',
      argFormat: {
      },
      argMap: {},
      showLoading: false,
      limitProcess: 1,
      serverAddr: Define.constants.serverAddr,
      apiVersion: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          level: 2,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', ''),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getRegionByLatLng: {
      query: '/app/get-region',
      argFormat: {
      },
      argMap: {},
      showLoading: false,
      limitProcess: 1,
      serverAddr: Define.constants.serverAddr,
      apiVersion: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          level: 2,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', ''),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    autoComplete: {
      query: '/google/place-auto-complete',
      argFormat: {},
      argMap: {},
      apiVersion: 1,
      serverAddr: Define.constants.serverIHeyU,
      onArg: (arg, getState) => {
        return {
          ...arg,

        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    placeDetail: {
      query: '/google/place-detail',
      argFormat: {},
      argMap: {},
      apiVersion: 1,
      serverAddr: Define.constants.serverIHeyU,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
  }

  setShowLoading(show) {
    const actionName = 'showLoading';

    return (dispatch) => {
      return new Promise((resolve,reject)=>{
          dispatch(RDActions[this.sortName][actionName+'OnRequest']({show}))
      })
    }
  }

  setStatusBarColor(color) {
    const actionName = 'setStatusBarColor';

    return (dispatch) => {
      return new Promise((resolve, reject) => {
        dispatch(RDActions[this.sortName][actionName + 'OnRequest']({ color }))
      })
    }
  }

  setFooterColor(color) {
    const actionName = 'setFooterColor';

    return (dispatch) => {
      return new Promise((resolve, reject) => {
        dispatch(RDActions[this.sortName][actionName + 'OnRequest']({ color }))
      })
    }
  }

  callApi(uri,params) {
    return new Promise((resolve, reject) => {
      let source = axios.CancelToken.source();
      if(Platform.OS === 'android') {
        setTimeout(() => {
          source.cancel('API Request timeout');
        }, 3000);
      }
      axios
        .get(uri,{
          headers: {
            'x-access-token' : Define.constants.serverApiToken,
            goal: Util.getGoal('/config',JSON.stringify(params))
          },
          timeout: 3000,
          params: params,
          cancelToken: source.token
        })
        .then((res) => {
          const listServer = res?.data?.data?.servers;
          if (listServer && Object.keys(listServer).length) {
            Object.keys(listServer).forEach((key) => {
              if (listServer[key]) {
                Define.servers[Define.constants[key]] = listServer[key];
              } else {
                Define.servers[Define.constants[key]] = '';
              }
            });
          } else {
            Define.servers = {};
          }

          resolve(res);
        })
        .catch((err) => {
          reject(err);
        })
    })
  }

  getConfig(arg={},setState = true){
    // return;
    var self = this;
    const urls = [
      `${Define.constants.proxyAddr}/api/v1.0/proxy/config`,
      `${Define.constants.proxyAddr1}/api/v1.0/proxy/config`,
      `${Define.constants.proxyAddr2}/api/v1.0/proxy/config`
    ];

    var actionName = 'getConfig';
    var argFormat={
      platform: Platform.OS,
      appName: Define.constants.appName,
      nativeVersion: DeviceInfo.getBuildNumber(),
      versionCodePush: Define.constants.versionCodePush,
      os_version: DeviceInfo.getSystemVersion(),
      tShoot: Date.now()
    }

    var argTemp = Util.dataProtectAndMap(arg, argFormat);

    var preTextLog = self.name+':'+actionName+':';

    return (dispatch) => {
      return new Promise((resolve,reject)=>{
        dispatch(RDActions[this.sortName][actionName+'OnRequest'](argTemp))
        let index = 0;
        let counter = 0;

        async.until((next) => {
          let uri = urls[index];

          this.callApi(uri, argTemp)
            .then((res) => {
              let data = {
                systemError: true
              };

              if (res.status === 200 && res.data.code === 200) {
                data = res.data.data;
              }

              dispatch(RDActions[this.sortName][actionName + 'OnResult'](RDActionsTypes.constants.REQUEST_SUBTYPE.SUCCESS, data));

              next(res);
            })
            .catch((err) => {
              if (counter < 10) {
                return next();
              }

              let data = {
                // networkError: err.message === 'Network Error' ? true : false,
                // systemError: true
              }

              dispatch(RDActions[this.sortName][actionName + 'OnResult'](RDActionsTypes.constants.REQUEST_SUBTYPE.SUCCESS, data));

              next(err);
            });
        }, (next) => {
          counter++;
          if (index >= urls.length || index < 0) {
            index = 0;
          } else {
            index++;
          }

          next();
        }, (data) => {
          if (_.isError(data)) {
            reject(data);
          } else {
            resolve(data);
          }
        })
      })
    }
  }
}


module.exports= new AppStateActions_MiddleWare();
