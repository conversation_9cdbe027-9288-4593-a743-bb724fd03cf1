var _ = require('lodash');
var RDActionsTypes = require('./RDActionsTypes');
import RDActions_MiddleWare from './RDActions_MiddleWare';
import {Actions} from 'react-native-router-flux';

import {Platform} from 'react-native';
// LIB
var DeviceInfo = require('react-native-device-info');
// components
var Define = require('../Define');
var Debug = require('../Util/Debug');
var Util = require('../Util/Util');
var RDActions = require('./RDActions');
var {
  globalVariableManager,
} = require('../components/modules/GlobalVariableManager');

// NOTE : stuck when call getState (when dispatch another action in a action)

var FeedsSystemActions_MiddleWare = require('./FeedsSystemActions_MiddleWare');

/*
 * action creators
 */

class UserActions_MiddleWare extends RDActions_MiddleWare {
  constructor() {
    super('UserActions_MiddleWare', true);
    this.init();
  }
  actionsList = {
    get: {
      query: '/user/get',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone:(dispatch,getState,data)=>{
        // if(Date.now() - getState().User.lastTracking >= 3 * 60 * 60 * 1000) {
        //   setTimeout(() => {
        //     dispatch(this.trackingAction({type: 2}));
        //   }, 1000)
        // }
        return true
      },
    },
    sendFeedback: {
      query: '/member/send-feedback',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getConfigRemoveAccount: {
      query: '/user/get-config-remove-account',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    sentNotifyToken: {
      query: '/notify/add-token',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    logout: {
      query: '/user/logout',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      showLoading: true,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    login: {
      query: '/user/login',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      showLoading: true,
      timeout: 3000,
      limitProcess: 1,
      showLoading: true,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: function (dispatch,getState,data) {
        dispatch(this.sentNotifyToken({
          memberToken: data?.res?.data?.token,
          notify_token: Define.config.token
        }));
        // setTimeout(() => {
        //   dispatch(this.trackingAction({type: 0}));
        // }, 1000)
        return true
      }
    },
    resetPassword: {
      query: '/user/change-password-otp',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      showLoading: true,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: function (dispatch,getState,data) {
        dispatch(this.sentNotifyToken({
          memberToken: data?.res?.data?.memberToken,
          notify_token: Define.config.token
        }));
        // setTimeout(() => {
        //   dispatch(this.trackingAction({type: 0}));
        // }, 1000)
        return true
      }
    },
    changePassword: {
      query: '/user/change-password',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      showLoading: true,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    sendOTP: {
      query: '/user/send-otp',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      showLoading: true,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    updateProfile: {
      query: '/user/update-profile',
      argFormat: {},
      serverAddr: Define.constants.serverIOC,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
  };
}

module.exports = new UserActions_MiddleWare();
