
var _ = require('lodash')
var RDActionsTypes = require('./RDActionsTypes');
import RDActions_MiddleWare from './RDActions_MiddleWare'

// LIB

// components
var Define = require('../Define');
var Debug = require('../Util/Debug');
var Util = require('../Util/Util');
var RDActions = require('./RDActions');

// NOTE : stuck when call getState (when dispatch another action in a action)

/*
 * action creators
 */

class HubSystemActions_MiddleWare extends RDActions_MiddleWare {
  constructor() {
    super('HubSystemActions_MiddleWare', true);
    this.init();
  }
  actionsList = {
    getOrderTypeHub: {
      query: '/order-type/get',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverHubAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined,
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    createHub: {
      query: '/hub/create',
      serverAddr: Define.constants.serverHubAddr,
      argFormat: {},
      argMap: {},
      showLoading: true,
      apiVersion: 1,
      // apiVersion:Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listHubForShop: {
      query: '/hub/list-for-shop',
      serverAddr: Define.constants.serverHubAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      // apiVersion:Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getDetailHub: {
      query: '/hub/get-for-shop',
      serverAddr: Define.constants.serverHubAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      // apiVersion:Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    stopFindHub: {
      query: '/hub/stop',
      serverAddr: Define.constants.serverHubAddr,
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: 1,
      // apiVersion:Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    continueFindHub: {
      query: '/hub/continue',
      serverAddr: Define.constants.serverHubAddr,
      argFormat: {},
      argMap: {},
      showLoading: true,
      apiVersion: 1,
      // apiVersion:Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getChangedHub: {
      query: '/hub/get-changed-for-shop',
      serverAddr: Define.constants.serverHubAddr,
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: 1,
      // apiVersion:Define.constants.apiVersion,
      limitProcess: 2,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    rejectHubOrder: {
      query: '/hub/reject-for-shop',
      serverAddr: Define.constants.serverHubAddr,
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: 1,
      // apiVersion:Define.constants.apiVersion,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listHubSearching: {
      query: '/hub/list-hub-searching',
      serverAddr: Define.constants.serverHubAddr,
      argFormat: {},
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    takeHub: {
      query: '/hub/take',
      serverAddr: Define.constants.serverHubAddr,
      argFormat: {},
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listHubForShipper: {
      query: '/hub/list-for-shipper',
      serverAddr: Define.constants.serverHubAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getHubForShipper: {
      query: '/hub/get-for-shipper',
      serverAddr: Define.constants.serverHubAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getDistanceMoney: {
      query: '/hub/get-distance-money',
      serverAddr: Define.constants.serverHubAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    calculateMoneyFromDistance: {
      query: '/hub/calculate-money-from-distance',
      serverAddr: Define.constants.serverHubAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getChangedForShipper: {
      query: '/hub/get-changed-for-shipper',
      serverAddr: Define.constants.serverHubAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    calculateServiceCharge: {
      query: '/hub/calculate-service-charge',
      serverAddr: Define.constants.serverHubAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getConfigShowHub: {
      query: '/hub/get-config-show-hub',
      serverAddr: Define.constants.serverHubAddr,
      argFormat: {},
      argMap: {},
      showLoading: false,
      apiVersion: 1,
      limitProcess: 2,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    createOrder: {
      query: '/hub/create-for-shipper',
      serverAddr: Define.constants.serverHubAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: true,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getConfigTake: {
      query: '/hub/get-config-take',
      serverAddr: Define.constants.serverHubAddr,
      argFormat: {},
      argMap: {},
      timeout: 3000,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', '')
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    }
  }
}


module.exports = new HubSystemActions_MiddleWare();
