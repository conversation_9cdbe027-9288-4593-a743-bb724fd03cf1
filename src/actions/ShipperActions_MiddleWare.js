
var _ = require('lodash')
var RDActionsTypes = require('./RDActionsTypes');
import RDActions_MiddleWare from './RDActions_MiddleWare'

// LIB

// components
var Define = require('../Define');
var Debug = require('../Util/Debug');
var Util = require('../Util/Util');
var RDActions = require('./RDActions');

// NOTE : stuck when call getState (when dispatch another action in a action)

/*
 * action creators
 */

class ShipperActions_MiddleWare extends RDActions_MiddleWare {
  constructor(){
    super('ShipperActions_MiddleWare',true);
    this.init();
  }
  actionsList={
    getNearest:{
      query:'/shipper/get-nearest',
      argFormat:{},
      argMap:{},
      limitProcess:1,
      showLoading: false,
      onArg:(arg,getState)=>{
        return {
          memberToken:_.get(getState(), 'User.memberInfo.member.memberToken', ''),
          ...arg,
          distance: arg.distance || 1000
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:undefined, // (dispatch,getState,data)=>{return true},
    }
  }
}


module.exports= new ShipperActions_MiddleWare();
