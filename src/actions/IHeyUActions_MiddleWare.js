var _ = require('lodash');
var RDActionsTypes = require('./RDActionsTypes');
import RDActions_MiddleWare from './RDActions_MiddleWare';
const DeviceInfo = require('react-native-device-info');

// LIB

// components
var Define = require('../Define');
var Debug = require('../Util/Debug');
var Util = require('../Util/Util');
var RDActions = require('./RDActions');

// NOTE : stuck when call getState (when dispatch another action in a action)

/*
 * action creators
 */

class IHeyUActions_MiddleWare extends RDActions_MiddleWare {
  constructor() {
    super('IHeyUActions_MiddleWare', true);
    this.init();
  }
  actionsList = {
    listDocument: {
      query: '/admin/chatbot/chat-with-doc/list-document',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    searchUnit: {
      query: '/admin/chatbot/chat-with-doc/search-unit',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listSharedDocuments: {
      query: '/admin/chatbot/chat-with-doc/list-shared-documents',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listUnitFilter: {
      query: '/admin/chatbot/chat-with-doc/list-unit-filter',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getDocument: {
      query: '/admin/chatbot/chat-with-doc/get-document',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    attachDocument: {
      query: '/admin/chatbot/chat-with-doc/attach-document',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    uploadFile: {
      query: '/admin/chatbot/chat-with-doc/upload',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    downloadFile: {
      query: '/util/download',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    createConversationImageToText: {
      query: '/admin/chatbot/create-conversation-itt',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    imageToText: {
      query: '/admin/chatbot/image-to-text',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listConversationImageToText: {
      query: '/admin/chatbot/list-conversation-itt',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listChatImageToText: {
      query: '/admin/chatbot/list-chat-itt',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getConversationImageToText: {
      query: '/admin/chatbot/image-to-text/get-conversation',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    createConversationChatWithDoc: {
      query: '/admin/chatbot/chat-with-doc/create-conversation',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    askChatWithDoc: {
      query: '/admin/chatbot/chat-with-doc/ask',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listConversationChatWithDoc: {
      query: '/admin/chatbot/chat-with-doc/list-conversation',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listChatWithDoc: {
      query: '/admin/chatbot/chat-with-doc/list-chat',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getStartQuestionChatWithDoc: {
      query: '/admin/chatbot/chat-with-doc/get-start-question',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getSuggestQuestionChatWithDoc: {
      query: '/admin/chatbot/chat-with-doc/get-suggest-question',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    createConversationVideoExcerpt: {
      query: '/admin/chatbot/create-conversation-video-excerpt',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    videoExcerpt: {
      query: '/admin/chatbot/video-excerpt',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listConversationVideoExcerpt: {
      query: '/admin/chatbot/list-conversation-video-excerpt',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listChatVideoExcerpt: {
      query: '/admin/chatbot/list-chat-video-excerpt',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getConversationVideoExcerpt: {
      query: '/admin/chatbot/get-conversation-video-excerpt',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    saveVideoExcerpt: {
      query: '/admin/chatbot/save-video-excerpt',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    createConversationMakeDecision: {
      query: '/admin/chatbot/create-conversation-make-decision',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    makeDecision: {
      query: '/admin/chatbot/make-decision',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listConversationMakeDecision: {
      query: '/admin/chatbot/list-conversation-make-decision',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listChatMakeDecision: {
      query: '/admin/chatbot/list-chat-make-decision',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getConversationMakeDecision: {
      query: '/admin/chatbot/make-decision/get-conversation',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getSuggestPromptMakeDecision: {
      query: '/admin/chatbot/make-decision/get-suggest-prompt',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listConversationReport: {
      query: '/admin/chatbot/list-conversation-write-report',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listChatWriteReport: {
      query: '/admin/chatbot/list-chat-write-report',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getConversationReport: {
      query: '/admin/chatbot/write-report/get-conversation',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    createConversationReport: {
      query: '/admin/chatbot/create-conversation-write-report',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getSuggestPromptWriteReport: {
      query: '/admin/chatbot/write-report/get-suggest-prompt',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    writeReport: {
      query: '/admin/chatbot/write-report',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: '1.1',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listChatPlanning: {
      query: '/admin/chatbot/list-chat-planning',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getConversationPlanning: {
      query: '/admin/chatbot/planning/get-conversation',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listConversationPlanning: {
      query: '/admin/chatbot/list-conversation-planning',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    createConversationPlanning: {
      query: '/admin/chatbot/create-conversation-planning',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    workPlanning: {
      query: '/admin/chatbot/planning',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getSuggestPromptPlanning: {
      query: '/admin/chatbot/planning/get-suggest-prompt',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    createConversationSocioEconomicReport: {
      query: '/admin/chatbot/create-conversation-report',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    socioEconomicReport: {
      query: '/admin/chatbot/report',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listConversationSocioEconomicReport: {
      query: '/admin/chatbot/list-conversation-report',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listChatSocioEconomicReport: {
      query: '/admin/chatbot/list-chat-report',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getConversationSocioEconomicReport: {
      query: '/admin/chatbot/report/get-conversation',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listChatSpeech: {
      query: '/admin/chatbot/list-chat-speech',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getConversationSpeech: {
      query: '/admin/chatbot/speech/get-conversation',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listConversationSpeech: {
      query: '/admin/chatbot/list-conversation-speech',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    createConversationSpeech: {
      query: '/admin/chatbot/create-conversation-speech',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    createSpeech: {
      query: '/admin/chatbot/speech',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listChatDocumentSummary: {
      query: '/admin/chatbot/list-chat-document-summary',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getConversationDocumentSummary: {
      query: '/admin/chatbot/document-summary/get-conversation',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listConversationDocumentSummary: {
      query: '/admin/chatbot/list-conversation-document-summary',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    createConversationDocumentSummary: {
      query: '/admin/chatbot/create-conversation-document-summary',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    createSummary: {
      query: '/admin/chatbot/document-summary',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    updateDocument: {
      query: '/admin/chatbot/chat-with-doc/update',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    deleteDocument: {
      query: '/admin/chatbot/chat-with-doc/delete',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listDistrict: {
      query: '/member/list-district',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listWard: {
      query: '/member/list-ward',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getLocationDetail: {
      query: '/member/get-location-detail',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    createPetition: {
      query: '/petition/create',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      showLoading: true,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    updatePetition: {
      query: '/petition/update',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      showLoading: true,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listPetition: {
      query: '/petition/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listCommunityPetition: {
      query: '/petition/list-community',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listCategoryPetition: {
      query: '/petition/category/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getPetition: {
      query: '/petition/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listServiceChildren: {
      query: '/services/list-service-children',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listNewCategory: {
      query: '/services/list-news-category',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listNews: {
      query: '/services/list-news',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listCategory: {
      query: '/services/list-category',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    list: {
      query: '/services/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getBanner: {
      query: '/services/get-banner',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getWeather: {
      query: '/services/get-weather',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    askSocioEconomic: {
      query: '/chatbot/ask-socio-economic',
      argFormat: {},
      serverAddr: Define.constants.serverHeyGPT,
      argMap: {},
      apiVersion: '1.1',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          deviceId: Define.constants.deviceId,
          memberToken: _.get(getState(), 'User.memberInfo.memberToken', ''),
        };
      },
    },
    listPublicService: {
      query: '/dich-vu-cong/list',
      argFormat: {},
      serverAddr: Define.constants.serverHeyGPT,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          deviceId: Define.constants.deviceId,
          memberToken: _.get(getState(), 'User.memberInfo.memberToken', ''),
        };
      },
    },
    listProcedurePublicService: {
      query: '/dich-vu-cong/list-procedure',
      argFormat: {},
      serverAddr: Define.constants.serverHeyGPT,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          deviceId: Define.constants.deviceId,
          memberToken: _.get(getState(), 'User.memberInfo.memberToken', ''),
        };
      },
    },
    listChatSocioEconomic: {
      query: '/chatbot/list-chat-socio-economic',
      argFormat: {},
      serverAddr: Define.constants.serverHeyGPT,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          deviceId: Define.constants.deviceId,
          memberToken: _.get(getState(), 'User.memberInfo.memberToken', ''),
        };
      },
    },
    createConversationSocioEconomic: {
      query: '/chatbot/create-conversation-socio-economic',
      argFormat: {},
      serverAddr: Define.constants.serverHeyGPT,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          deviceId: Define.constants.deviceId,
          memberToken: _.get(getState(), 'User.memberInfo.memberToken', ''),
        };
      },
    },
    createConversation: {
      query: '/chatbot-json/create-conversation',
      argFormat: {},
      serverAddr: Define.constants.serverHeyGPT,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          deviceId: Define.constants.deviceId,
          id: _.get(getState(), 'User.memberInfo._id', ''),
          memberToken: _.get(getState(), 'User.memberInfo.memberToken', ''),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listChat: {
      query: '/chatbot-json/list-chat',
      argFormat: {},
      serverAddr: Define.constants.serverHeyGPT,
      argMap: {},
      apiVersion: '1.1',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          deviceId: Define.constants.deviceId,
          memberToken: _.get(getState(), 'User.memberInfo.memberToken', ''),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    ask: {
      query: '/chatbot-json/ask-stream',
      argFormat: {},
      serverAddr: Define.constants.serverHeyGPT,
      argMap: {},
      apiVersion: '1.2',
      limitProcess: 1,
      timeout: 30000,
      onArg: (arg, getState) => {
        return {
          ...arg,
          deviceId: Define.constants.deviceId,
          memberToken: _.get(getState(), 'User.memberInfo.memberToken', ''),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getAnswer: {
      query: '/chatbot-json/get-answer',
      argFormat: {},
      serverAddr: Define.constants.serverHeyGPT,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      timeout: 30000,
      onArg: (arg, getState) => {
        return {
          ...arg,
          deviceId: Define.constants.deviceId,
          memberToken: _.get(getState(), 'User.memberInfo.memberToken', ''),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    stopStream: {
      query: '/chatbot-json/stop-stream',
      argFormat: {},
      serverAddr: Define.constants.serverHeyGPT,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      timeout: 30000,
      onArg: (arg, getState) => {
        return {
          ...arg,
          deviceId: Define.constants.deviceId,
          memberToken: _.get(getState(), 'User.memberInfo.memberToken', ''),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listAskChatBot: {
      query: '/chatbot-json/list-ask-default',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      timeout: 30000,
      onArg: (arg, getState) => {
        return {
          ...arg,
          deviceId: Define.constants.deviceId,
          memberToken: _.get(getState(), 'User.memberInfo.memberToken', ''),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listConversation: {
      query: '/chatbot-json/list-conversation',
      argFormat: {},
      serverAddr: Define.constants.serverHeyGPT,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      timeout: 30000,
      onArg: (arg, getState) => {
        return {
          ...arg,
          deviceId: Define.constants.deviceId,
          memberToken: _.get(getState(), 'User.memberInfo.memberToken', ''),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listModel: {
      query: '/model/list',
      argFormat: {},
      serverAddr: Define.constants.serverHeyGPT,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      timeout: 30000,
      onArg: (arg, getState) => {
        return {
          ...arg,
          deviceId: Define.constants.deviceId,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listSchool: {
      query: '/education/school/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listTeachingCenter: {
      query: '/education/teaching-center/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listLawEnforcementAgency: {
      query: '/administrative-agency/law-enforcement-agency/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listLegislature: {
      query: '/administrative-agency/legislature/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listBilliards: {
      query: '/entertainment/billiards/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listKaraoke: {
      query: '/entertainment/karaoke/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listMassage: {
      query: '/entertainment/massage/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listSportsField: {
      query: '/entertainment/sports-field/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listTypeSportsField: {
      query: '/entertainment/sports-field/list-type',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listHospital: {
      query: '/medical/hospital/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listMaternityClini: {
      query: '/medical/maternity-clinic/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listPharmacy: {
      query: '/medical/pharmacy/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listTouristAttraction: {
      query: '/travel/tourist-attraction/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
          deviceId: Define.constants.deviceId,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getSchool: {
      query: '/education/school/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getTeachingCenter: {
      query: '/education/teaching-center/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getHospital: {
      query: '/medical/hospital/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getMaternityClini: {
      query: '/medical/maternity-clinic/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getPharmacy: {
      query: '/medical/pharmacy/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getTouristAttraction: {
      query: '/travel/tourist-attraction/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getLawEnforcementAgency: {
      query: '/administrative-agency/law-enforcement-agency/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getLegislature: {
      query: '/administrative-agency/legislature/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getBilliards: {
      query: '/entertainment/billiards/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getKaraoke: {
      query: '/entertainment/karaoke/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getSportsField: {
      query: '/entertainment/sports-field/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getMassage: {
      query: '/entertainment/massage/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listHotel: {
      query: '/travel/hotel/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getHotel: {
      query: '/travel/hotel/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listRestaurant: {
      query: '/travel/restaurant/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getRestaurant: {
      query: '/travel/restaurant/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listFestival: {
      query: '/travel/festival/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getFestival: {
      query: '/travel/festival/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listTourList: {
      query: '/travel/tour-list/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getTourList: {
      query: '/travel/tour-list/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    speechToText: {
      query: '/chatbot-json/speech-to-text',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    deleteConversation: {
      query: '/chatbot-json/delete-conversation',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          deviceId: Define.constants.deviceId,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listIntrucstion: {
      query: '/instruction/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          deviceId: Define.constants.deviceId,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getIntrucstion: {
      query: '/instruction/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          deviceId: Define.constants.deviceId,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getConfigMaintain: {
      query: '/petition/get-config-maintain',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          deviceId: Define.constants.deviceId,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    petitionRate: {
      query: '/petition/rate',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          deviceId: Define.constants.deviceId,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    checkMember: {
      query: '/member/check-member-exists',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          deviceId: Define.constants.deviceId,
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listOCOP: {
      query: '/agriculture/ocop/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getOCOP: {
      query: '/agriculture/ocop/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listCooperative: {
      query: '/agriculture/cooperative/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getCooperative: {
      query: '/agriculture/cooperative/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listTraditionalVillage: {
      query: '/agriculture/traditional-village/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getTraditionalVillage: {
      query: '/agriculture/traditional-village/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listHistoricalSite: {
      query: '/travel/historical-site/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getHistoricalSite: {
      query: '/travel/historical-site/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listCulturalHeritage: {
      query: '/travel/cultural-heritage/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getCulturalHeritage: {
      query: '/travel/cultural-heritage/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listCompanyTravel: {
      query: '/travel/tourist-company/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getCompanyTravel: {
      query: '/travel/tourist-company/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    levelEducation: {
      query: '/edu/education/list-level',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listEducation: {
      query: '/edu/education/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getEducation: {
      query: '/edu/education/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listCenterLanguage: {
      query: '/edu/education-center/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getCenterLanguage: {
      query: '/edu/education-center/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listCenterJob: {
      query: '/edu/education-job/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getCenterJob: {
      query: '/edu/education-job/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listVaccinationFacility: {
      query: '/medical/vaccine-center/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getVaccinationFacility: {
      query: '/medical/vaccine-center/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listNewPharmacy: {
      query: '/medical/drug-store/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getNewPharmacy: {
      query: '/medical/drug-store/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listPressAgency: {
      query: '/press/press-agency/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getPressAgency: {
      query: '/press/press-agency/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listTelecomService: {
      query: '/press/tele-service/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getTelecomService: {
      query: '/press/tele-service/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listLowOffce: {
      query: '/law/lawyer-organization/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getLowOffce: {
      query: '/law/lawyer-organization/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listNotaryOffice: {
      query: '/law/notary-office/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getNotaryOffice: {
      query: '/law/notary-office/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listServiceChatbot: {
      query: '/services/list-chatbot',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listCategoryNotify: {
      query: '/notification/category/list',
      argFormat: {},
      serverAddr: Define.constants.serverNotifyAddr,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listLevelAdministrativeAgency: {
      query: '/administrative-agency/law-enforcement-agency/list-level',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listStructureAdministrativeAgency: {
      query: '/administrative-agency/law-enforcement-agency/list-structure',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listBusStation: {
      query: '/traffic/bus-station/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getBusStation: {
      query: '/traffic/bus-station/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listRegistrationCenter: {
      query: '/traffic/registration-center/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getRegistrationCenter: {
      query: '/traffic/registration-center/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listSocioEconomicCategory: {
      query: '/socio-economic/list-category',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listSocioEconomicTitle: {
      query: '/socio-economic/list-title',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listGovernment: {
      query: '/government/haiphong/list',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getListEvent: {
      query: '/dich-vu-cong/list-event',
      argFormat: {},
      serverAddr: Define.constants.serverHeyGPT,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getGovernment: {
      query: '/government/haiphong/get',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getPublicService: {
      query: '/dich-vu-cong/get-procedure',
      argFormat: {},
      serverAddr: Define.constants.serverHeyGPT,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listMonthSocio: {
      query: '/socio-economic/list-month',
      argFormat: {},
      serverAddr: Define.constants.serverHeyGPT,
      argMap: {},
      apiVersion: '1.1',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    listYearSocio: {
      query: '/socio-economic/list-year',
      argFormat: {},
      serverAddr: Define.constants.serverHeyGPT,
      argMap: {},
      apiVersion: '1.1',
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    ratingChatBot: {
      query: '/chatbot-json/rating',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
    getPetitionStatistic: {
      query: '/petition/statistic',
      argFormat: {},
      serverAddr: Define.constants.serverIHeyU,
      argMap: {},
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
          memberToken: _.get(
            getState(),
            'User.memberInfo.member.memberToken',
            '',
          ),
        };
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: undefined, // (dispatch,getState,data)=>{return true},
    },
  };
}

module.exports = new IHeyUActions_MiddleWare();
