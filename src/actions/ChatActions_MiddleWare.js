
var _ = require('lodash')
var RDActionsTypes = require('./RDActionsTypes');
import RDActions_MiddleWare from './RDActions_MiddleWare'

// LIB

// components
var Define = require('../Define');
var Debug = require('../Util/Debug');
var Util = require('../Util/Util');
var RDActions = require('./RDActions');

// NOTE : stuck when call getState (when dispatch another action in a action)

/*
 * action creators
 */

class ChatActions_MiddleWare extends RDActions_MiddleWare {
  constructor(){
    super('ChatActions_MiddleWare',true);
    this.init();
  }
  actionsList={
    listHistory:{
      query:'/chat/list-history',
      argFormat:{},
      argMap:{},
      showLoading: false,
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', ''),
          ...arg
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:(dispatch,getState,data)=>{
        return true
      },
    },
    getConversationInf: {
      query:'/chat/get-conversation-inf',
      argFormat:{},
      argMap:{},
      showLoading: false,
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', ''),
          ...arg
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:(dispatch,getState,data)=>{
        return true
      },
    },
    getHistoryByConversation: {
      query:'/chat/get-history-by-conversation',
      argFormat:{},
      argMap:{},
      showLoading: false,
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', ''),
          conversation: arg.conversation,
          limit: 15,
          from: arg.from
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:(dispatch,getState,data)=>{
        return true
      },
    },
    getHistoryByUser: {
      query:'/chat/get-history',
      argFormat:{},
      argMap:{},
      showLoading: false,
      apiVersion:Define.constants.apiVersion,
      limitProcess:1,
      onArg:(arg,getState)=>{
        return {
          memberToken: _.get(getState(), 'User.memberInfo.member.memberToken', ''),
          listUserInGroup: arg.listUserInGroup,
          limit: 15
        };
      },
      onError:undefined, // (dispatch,getState,data)=>{return true},
      onDone:(dispatch,getState,data)=>{
        return true
      },
    }
  }
}


module.exports= new ChatActions_MiddleWare();
