
var _ = require('lodash')
var RDActionsTypes = require('./RDActionsTypes');
import RDActions_MiddleWare from './RDActions_MiddleWare'

// LIB

// components
var Define = require('../Define');
var Debug = require('../Util/Debug');
var Util = require('../Util/Util');
var RDActions = require('./RDActions');

// NOTE : stuck when call getState (when dispatch another action in a action)

/*
 * action creators
 */

class NotificationsActions_MiddleWare extends RDActions_MiddleWare {
  constructor(){
    super('NotificationsActions_MiddleWare',true);
    this.init();
  }
  actionsList={
    notify: {
      query: '/notify/list',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverIOC,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    refreshNotify: {
      query: '/notification/list',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverNotifyAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    seenAllNotify: {
      query: '/notification/seen-all',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverNotifyAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    countNotify: {
      query: '/notify/count-unread',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverIOC,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getDetailNotification: {
      query: '/notify/get',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverIOC,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    seenNotify: {
      query: '/notify/seen',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverIOC,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    },
    getHotNew: {
      query: '/hotnew/get',
      argFormat: {},
      argMap: {},
      serverAddr: Define.constants.serverNotifyAddr,
      showLoading: false,
      apiVersion: 1,
      limitProcess: 1,
      onArg: (arg, getState) => {
        return {
          ...arg,
        }
      },
      onError: undefined, // (dispatch,getState,data)=>{return true},
      onDone: (dispatch, getState, data) => {
        return true
      }
    }
  }
}


module.exports = new NotificationsActions_MiddleWare();
