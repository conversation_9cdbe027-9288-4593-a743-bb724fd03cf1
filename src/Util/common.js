import {
  Platform,
  Linking,
  NativeModules,
  PermissionsAndroid
} from 'react-native'
const Debug = require('./Debug')
import {popupActions} from '../components/popups/PopupManager'
import DefaultPopup from '../components/popups/DefaultPopup';
var RNIntent = NativeModules.RNIntent;
module.exports = {
  callNormal: (phone) => {
    var url = `tel:${phone}`;
    if(Platform.OS === 'android') {
      checkAndRequestPermission(phone)
    } else {
      url = `telprompt:${phone}`;
      Linking
        .canOpenURL(url)
        .then((supported) => {
          if(supported) {
            return Linking.openURL(url);
          }
          return Promise.reject();
        })
        .catch((err) => {
          Debug.log(err,Debug.level.ERROR)
        })
    }
  },
  calculateMoneyWait: (time, moneyStrategy, priceIncrease) => {
    if (!time || !moneyStrategy) {
      return 0;
    }

    const configMoney = moneyStrategy.step || [];
    const min = moneyStrategy.minMoney;
    priceIncrease = priceIncrease || 1;
    let money = 0;
    for(let i=0; i<configMoney.length; i++) {
      if (configMoney[i].time === 0) {
        money += configMoney[i].money*time;
        break;
      } else if (time >= configMoney[i].time) {
        time -= configMoney[i].time
        money += configMoney[i].money*configMoney[i].time
      } else {
        money += configMoney[i].money*time;
        break;
      }
    }
    if(money < min) {
      money = min;
    }
    money = money*priceIncrease;
    money = Math.round(money);
    return money;
  },
  calculateMoney: (distance, moneyStrategy, priceIncrease, numDes) => {
    if (!moneyStrategy) {
      return 0
    }

    const configMoney = moneyStrategy.step || [];
    const min = moneyStrategy.minMoney;
    const moreDesPrice = moneyStrategy.moreDesPrice || 0;
    priceIncrease = priceIncrease || 1;
    numDes = numDes || 1;
    let money = 0;
    for(let i=0; i<configMoney.length; i++) {
      if (configMoney[i].distance === 0) {
        money += configMoney[i].money*distance;
        break;
      } else if (distance >= configMoney[i].distance) {
        distance -= configMoney[i].distance
        money += configMoney[i].money*configMoney[i].distance
      } else {
        money += configMoney[i].money*distance;
        break;
      }
    }
    if(money < min) {
      money = min;
    }
    if(numDes > 1) {
      money += moreDesPrice*(numDes - 1);
    }
    money = money*priceIncrease;
    money = Math.round(money/1000)*1000;
    return money;
  }
}
const checkAndRequestPermission = async (phone) => {
  try {
    const granted = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.CALL_PHONE,
    );
    if (granted === PermissionsAndroid.RESULTS.GRANTED) {
      RNIntent.makeCall(`${phone}`, true)
    } else {
      popupActions.setRenderContentAndShow(DefaultPopup,{
        title:'Thông báo',
        description:'Bạn hãy cho phép HeyU quyền gọi điện trong (Cài đặt -> Quyền -> Điện thoại -> Cho phép) để có thể tạo cuộc gọi nhanh',
        buttonTitle2:'Bỏ qua',
        buttonTitle:'Đến cài đặt',
        onPress2:() => {popupActions.popPopup()},
        onPress:() => {
          Linking.openSettings()
          popupActions.popPopup()
        }
      })
    }
  } catch (err) {
    console.warn(err);
  }
};