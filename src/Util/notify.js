const Define = require('../Define')
const Debug = require('./Debug')
const Util = require('./Util')
import RDActions from '../actions/RDActions'
import { Platform } from 'react-native';
import { Notifier, NotifierComponents } from 'react-native-notifier';

const { globalVariableManager } = require('../components/modules/GlobalVariableManager')

module.exports = {
  pushNotify(notify) {
   Notifier.showNotification({
      title: notify.title,
      description: notify.description,
      translucentStatusBar: 1,
      duration: 5000,
      showAnimationDuration: 800,
      Component: NotifierComponents.Notification,
      componentProps: {
        imageSource: { uri: notify.icon || 'https://media.heyu.asia/uploads/notify/2020-11-02-heyu.jpeg' },
        containerStyle: { flex: 1, flexDirection: 'row', backgroundColor: '#f9f7f7', borderRadius: 10, width: Define.constants.widthScreen - 10, elevation: Define.constants.elevation, borderColor: '#000', paddingVertical: 10, alignSelf: 'center', shadowColor: '#000', shadowOpacity: 0.5, shadowOffset: { height: 0, width: 0 }, shadowRadius: 0.5 },
        imageStyle: { width: 40, height: 40, borderRadius: 5, marginLeft: 8, marginTop: 6 },
        viewContentStyle: { flex: 1, paddingHorizontal: 8 },
        titleStyle: { left: 0, right: 0, fontSize: 17, color: '#000', marginVertical: 0, fontWeight: 'bold' }
      },

      onStartHiding: () => { },
      onHidden: () => { },
      onPress: () => {
        globalVariableManager.reduxManager.dispatch(RDActions['Notify']['removeOnRequest']({ id: notify._id }));
        globalVariableManager.navigatorManager.handleNavigator(notify.link, notify.extras, notify.fromSocket);
      },
      hideOnPress: true,

      swipePixelsToClose: 10,
    })
  },
  pushAlertTopNotify(notify) {
    let colorBackground = '#2ecc71'
    if (notify.type === 'warning') {
      colorBackground = '#fcca17'
    } else if (notify.type === 'error') {
      colorBackground = '#D44333'
    }

    Notifier.showNotification({
      title: notify.title || '',
      description: notify.content || '',
      duration: notify.timeClose || 3000 ,
      showAnimationDuration: 400,
      Component: NotifierComponents.Alert,
      translucentStatusBar: Platform.OS == 'ios' ? 0 : 1,
      componentProps: {
        backgroundColor: colorBackground,
        titleStyle: { fontSize: 16, color: '#fff'},
        descriptionStyle: { fontSize: 14, color: '#fff', textAlign: 'left'}
      },
       onStartHiding: () => { },
       onHidden: () => { },
       onPress: () => {},
       hideOnPress: true,

       swipePixelsToClose: 10,
     })
   }
}
