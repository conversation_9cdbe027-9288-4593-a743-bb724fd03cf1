import polyline from '@mapbox/polyline';

var gis = {
  /**
  * All coordinates expected EPSG:4326
  * @param {Array} start Expected [lon, lat]
  * @param {Array} end Expected [lon, lat]
  * @return {number} Distance - meter.
  */
  calculateDistance: function(start, end) {
    var lat1 = parseFloat(start[1]),
        lon1 = parseFloat(start[0]),
        lat2 = parseFloat(end[1]),
        lon2 = parseFloat(end[0]);

    return gis.sphericalCosinus(lat1, lon1, lat2, lon2);
  },

  /**
  * All coordinates expected EPSG:4326
  * @param {number} lat1 Start Latitude
  * @param {number} lon1 Start Longitude
  * @param {number} lat2 End Latitude
  * @param {number} lon2 End Longitude
  * @return {number} Distance - meters.
  */
  sphericalCosinus: function(lat1, lon1, lat2, lon2) {
    var radius = 6371e3; // meters
    var dLon = gis.toRad(lon2 - lon1),
        lat1 = gis.toRad(lat1),
        lat2 = gis.toRad(lat2),
        distance = Math.acos(Math.sin(lat1) * Math.sin(lat2) +
            Math.cos(lat1) * Math.cos(lat2) * Math.cos(dLon)) * radius;

    return distance;
  },

  /**
  * @param {Array} coord Expected [lon, lat] EPSG:4326
  * @param {number} bearing Bearing in degrees
  * @param {number} distance Distance in meters
  * @return {Array} Lon-lat coordinate.
  */
  createCoord: function(coord, bearing, distance) {
    /** http://www.movable-type.co.uk/scripts/latlong.html
    * φ is latitude, λ is longitude,
    * θ is the bearing (clockwise from north),
    * δ is the angular distance d/R;
    * d being the distance travelled, R the earth’s radius*
    **/
    var
      radius = 6371e3, // meters
      δ = Number(distance) / radius, // angular distance in radians
      θ = gis.toRad(Number(bearing)),
      φ1 = gis.toRad(coord[1]),
      λ1 = gis.toRad(coord[0]);

    var φ2 = Math.asin(Math.sin(φ1)*Math.cos(δ) +
      Math.cos(φ1)*Math.sin(δ)*Math.cos(θ));

    var λ2 = λ1 + Math.atan2(Math.sin(θ) * Math.sin(δ)*Math.cos(φ1),
      Math.cos(δ)-Math.sin(φ1)*Math.sin(φ2));
    // normalise to -180..+180°
    λ2 = (λ2 + 3 * Math.PI) % (2 * Math.PI) - Math.PI;

    return [gis.toDeg(λ2).toFixed(6), gis.toDeg(φ2).toFixed(6)];
  },
  intermediatePoint: function (start, end, fraction) {
    const φ1 = gis.toRad(start[0]), λ1 = gis.toRad(start[1]);
    const φ2 = gis.toRad(end[0]), λ2 = gis.toRad(end[1]);

    // distance between points
    const Δφ = φ2 - φ1;
    const Δλ = λ2 - λ1;
    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2)
      + Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const δ = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    const A = Math.sin((1 - fraction) * δ) / Math.sin(δ);
    const B = Math.sin(fraction * δ) / Math.sin(δ);

    const x = A * Math.cos(φ1) * Math.cos(λ1) + B * Math.cos(φ2) * Math.cos(λ2);
    const y = A * Math.cos(φ1) * Math.sin(λ1) + B * Math.cos(φ2) * Math.sin(λ2);
    const z = A * Math.sin(φ1) + B * Math.sin(φ2);

    const φ3 = Math.atan2(z, Math.sqrt(x * x + y * y));
    const λ3 = Math.atan2(y, x);

    const lat = gis.toDeg(φ3);
    const lon = gis.toDeg(λ3);
    // return { longitude: +lon, latitude: +lat }
    return [ +lat, +lon ]
  },
  /**
   * All coordinates expected EPSG:4326
   * @param {Array} start Expected [lon, lat]
   * @param {Array} end Expected [lon, lat]
   * @return {number} Bearing in degrees.
   */
  getBearing: function(start, end){
    var
      startLat = gis.toRad(start[1]),
      startLong = gis.toRad(start[0]),
      endLat = gis.toRad(end[1]),
      endLong = gis.toRad(end[0]),
      dLong = endLong - startLong;

    let y = Math.sin(dLong) * Math.cos(endLat);
    let x = Math.cos(startLat) * Math.sin(endLat) - Math.sin(startLat) * Math.cos(endLat) * Math.cos(dLong);
    let bearing = (gis.toDeg(Math.atan2(y, x)) + 360) % 360;

    return parseFloat(bearing.toFixed(2));
  },
  toDeg: function(n) { return n * 180 / Math.PI; },
  toRad: function(n) { return n * Math.PI / 180; }
};

const self = {
  generateNewPolyline: (currentPolyline, distance = 2) => {
    const DISTANCE_NEW_COOR = distance * (distance / 10 + 5) < 1 ? 1 : distance * (distance / 10 + 5);
    const locations = polyline.decode(currentPolyline);

    // lưu danh sách toạ độ sau khi biến đổi
    const newLocations = [];
    // ban đầu ta có toạ độ đầu tiên của danh sách toạ độ mới
    newLocations.push(locations[0]);

    // lặp qua tất cả các toạ độ
    for (let i = 1; i < locations.length; i++) {
      const lastIndex = newLocations.length - 1;
      const lastCoord = newLocations[lastIndex];
      const targetCoord = locations[i];

      // tính khoảng cách giữa toạ độ cuối cùng của danh sách toạ độ mới và toạ độ ban đầu đang xét
      // công thức tính có thể tìm ở đây: https://www.movable-type.co.uk/scripts/latlong.html
      const distance = gis.calculateDistance(lastCoord, targetCoord);

      // nếu khoảng cách đó lớn hơn giá trị tối đa thì ta cần phải biến đổi
      if (distance > DISTANCE_NEW_COOR) {
        // chia đoạn thẳng thành các đoạn nhỏ hơn, với số phần bằng tổng khoảng cách chia cho giá trị tối đa của một đoạn
        // ví dụ ta có khoảng cách = 500 và giá trị tối đa = 100 thì ta sẽ chia thành 5 phần nhỏ hơn
        const numberOfSegments = Math.ceil(distance / DISTANCE_NEW_COOR);

        // lần lượt tính toạ độ của các đoạn thẳng nhỏ hơn
        for (let j = 1; j < numberOfSegments; j++) {
          // tính phần trăm của đoạn nhỏ thứ j
          // ví dụ ta đang tính đoạn thứ 2, thì đoạn thứ 2 sẽ có chiều dài 200 và bằng 40% tổng khoảng cách
          const fraction = (DISTANCE_NEW_COOR * j) / distance;

          // tính toạ độ dựa trên điểm đầu, điểm cuối và phần trăm vừa tính
          // công thức tính có thể tìm ở đây: https://www.movable-type.co.uk/scripts/latlong.html
          const intermediatePoint = gis.intermediatePoint(lastCoord, targetCoord, fraction);

          newLocations.push(intermediatePoint);
        }
      }

      newLocations.push(targetCoord);
    }

    return polyline.encode(newLocations);
  },
  generateNewLocation: (start, end, minDistance) => {
    const newLocations = [];
    const distance = gis.calculateDistance(start, end); // meters
    // newLocations.push(start);
    if (distance >= minDistance) {
      const numNewLocation = Math.floor(distance / minDistance);
      const bearing = gis.getBearing(start, end);

      for (let j = 0; j < numNewLocation; j++) {
        let coord = gis.createCoord(start, bearing, minDistance * (j + 1));

        newLocations.push(coord);
      }
    }

    newLocations.push(end);

    return newLocations;
  }
}

module.exports = self;
