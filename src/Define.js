
var DeviceInfo = require('react-native-device-info');

import {
  Dimensions, Platform,PixelRatio, StatusBar
} from 'react-native';

var PlatformConfig = require('./PlatformConfig');
var mapStyle = require('./Themes/MapStyle');
var RNFS = require('react-native-fs');
//variable
var widthScreen = Dimensions.get('window').width;
var heightScreen = Dimensions.get('window').height;
const screenHeight = Dimensions.get('screen').height;

var screenSizeByInch = Math.sqrt(Math.pow(widthScreen,2) + Math.pow(heightScreen,2))  / (PixelRatio.get()*160) * 2;

var assets={
  Images:{
    home: require('../assets/Images/home.png'),
    bell: require('../assets/Images/bell.png'),
    chatBubble: require('../assets/Images/chatBubble.png'),
    maps: require('../assets/Images/maps.png'),
    search: require('../assets/Images/search.png'),
    community: require('../assets/Images/community.png'),
    bubbleAdd: require('../assets/Images/bubbleAdd.png'),
    user: require('../assets/Images/user.png'),
    speakerGrey: require('../assets/Images/speakerGrey.png'),
    noPetition: require('../assets/Images/noPetition.png'),
    cloudUpload: require('../assets/Images/cloudUpload.png'),
    splashscreen: require('../assets/Images/splashscreen.png'),
    star: require('../assets/Images/star.png'),
    chatbot: require('../assets/Images/chatbot.png'),
    chatbotEffect: require('../assets/Images/chatbotEffect.png'),
    school: require('../assets/Images/school.png'),
    tour: require('../assets/Images/tour.png'),
    hospital: require('../assets/Images/hospital.png'),
    restaurant: require('../assets/Images/restaurant.png'),
    history: require('../assets/Images/history.png'),
    creature: require('../assets/Images/creature.png'),
    geography: require('../assets/Images/geography.png'),
    travel: require('../assets/Images/travel.png'),
    flag: require('../assets/Images/flag.png'),
    vneid: require('../assets/Images/vneid.jpg'),
    paperMessage:require('../assets/Images/paperMessage.png'),
    instruct: require('../assets/Images/instruct.png'),
    headPhone: require('../assets/Images/headPhone.png'),
    changePass: require('../assets/Images/changePass.png'),
    shield: require('../assets/Images/shield.png'),
    term: require('../assets/Images/term.png'),
    bgAI: require('../assets/Images/bgAI.png'),
    bgrmain: require('../assets/Images/bgrmain.png'),
    bgrlogin: require('../assets/Images/bgrlogin.png'),
    thongtinungdung: require('../assets/Images/thongtinungdung.png'),
    logocongan: '',
    bgrProfile: require('../assets/Images/bgrProfile.png'),
    bookInfo: require('../assets/Images/bookInfo.png'),
    trongdongdongson: require('../assets/Images/trongdongdongson.png'),
    spotlight: require('../assets/Images/spotlight.png'),
    upload: require('../assets/Images/upload.png'),
    pin: require('../assets/Images/pin.png'),
  },
  Animation:{
    animationLoading: require('../assets/Animation/animationLoading.json'),
  }
}

var mapAssets={
}

var configDev = true // must be false when release

var serverInf = {
  serverAddr : configDev ? 'http://************:8000' : 'https://api.heyu.asia',
  serverOrderAddr: configDev ? 'http://************:9001' : 'https://orders.heyu.asia',
  serverBikeAddr: configDev ? 'http://************:2001' : 'https://bike.heyu.asia',
  serverGatewayAddr: configDev ? 'http://************:1970' : 'https://gateway.heyu.asia',
  serverRatingAddr: configDev ? 'http://************:1996' : 'https://rating.heyu.asia',
  serverStatiticAddr: configDev ? 'http://************:3001' : 'https://statistic.heyu.asia',
  serverRestaurantAddr: configDev ? 'http://************:2000' : 'https://restaurant.heyu.asia',
  serverPromoteAddr: configDev ? 'http://************:1975' : 'https://promote.heyu.asia',
  serverSocketChatAddr: configDev ? 'http://************:2025' : 'https://chat.heyu.asia',
  serverSocketShipperAddr: 'https://shipper-socket.heyu.asia',
  serverSocketAddr: configDev ? 'http://************:9218' : 'https://socket-hb.canbo.ai',
  serverSocketLocationAddr: 'https://realtime-location.heyu.asia',
  serverLocationHeyCareAddr: configDev ? 'http://************:2090' : 'https://location-heycare.heyu.asia',
  serverMediaAddr:  configDev ? 'http://************:2024' : 'https://media-hb.canbo.ai',
  serverLocationAddr: configDev ? 'http://************:2009' : 'https://location.heyu.asia',
  serverHubAddr: configDev ? 'http://************:2030' : 'https://hub.heyu.asia',
  proxyAddr: configDev ? 'http://************:3007' :'https://api-hb.canbo.ai',
  proxyAddr1: configDev ? 'http://************:3007' :'https://proxy-hb.canbo.ai',
  proxyAddr2: configDev ? 'http://************:3007' :'https://api-hb.canbo.ai',
  serverNotifyAddr: configDev ? 'http://************:1991' : 'https://notify-hb.canbo.ai',
  serverVNpayAddr: 'https://vnpay.heyu.asia',
  serverSignalWebRTC: configDev ? 'http://************:4443': 'https://signal.heyu.asia',
  serverLoyaltyAddr: configDev ? 'http://************:5005' : 'https://loyalty.heyu.asia',
  serverCMSService: configDev ? 'http://************:1977' : 'https://cms-service.heyu.asia',
  serverHireDriverAddr: configDev ? 'http://************:7237' : "https://hire-driver.heyu.asia",
  urlTerms: 'https://icongdanso.vn/terms',
  urlTermHeyCare: 'https://heycare.vn/terms-app/',
  urlTermHeyClean: 'https://heyu.vn/terms-app-heyclean/',
  tutorial: 'https://heyu.vn/how-to-use-for-app',
  tutorialForShop: 'https://heyu.vn/how-to-use-for-shop',
  support: 'https://heyu.vn/support',
  intro: 'https://heyu.vn/intro-app',
  blogForShipper: 'https://heyu.vn/blogs/shipper',
  blogForShop: 'https://heyu.vn/blogs/shop',
  serverStoreAddr: configDev ? 'http://************:2026' : 'https://stores.heyu.asia',
  serverHeyCare: configDev ? 'http://************:2224' : 'https://care.heyu.asia',
  serverRatingHeyCareAddr: configDev ? 'http://************:1969' : 'https://rating-heycare.heyu.asia',
  serverHeyClean: configDev ? 'http://************:2234' : 'https://clean.heyu.asia',
  serverIHeyU: configDev ? 'http://************:3007' : 'https://api-hb.canbo.ai',
  serverHeyGPT: configDev ? 'http://************:3007' : 'https://api-hb.canbo.ai',
  serverIOC: configDev ? 'http://************:9654' : 'https://api-hb.canbo.ai',
  debug: configDev ? true : false,
  logLevel: configDev ? 0 : 10
}
var Define = {

  assets: (__DEV__ || Platform.OS === 'ios')? assets:PlatformConfig.default.processAsset(assets,mapAssets),
  constants:{
    linkConfigMap:'https://maps.heyu.asia/styles/Bright/{z}/{x}/{y}.png',
    hybridVersion: PlatformConfig.default.hybridVersion,
    heightOfStatusBarAndroid : 0,
    heightOfSoftMenuBarAndroid: 0,
    availableHeightScreen: heightScreen,
    widthScreen:widthScreen,
    heightScreen:heightScreen,
    screenSizeByInch:screenSizeByInch,
    appName: 'wowworker',
    displayName: 'WowWorker',
    deviceId:'',
    userAgent: '',
    deviceName: '',
    deviceManufacturer: '',
    nativeVersion:DeviceInfo.getBuildNumber(),
    currentHybridVersion:0,

    imageThumbRate:(20/9),
    smallImageThumbRate:(9/6),
    videoHeight:widthScreen<heightScreen?widthScreen:heightScreen,
    videoWidth:widthScreen<heightScreen? heightScreen:widthScreen,

    fontScale : Math.floor(4),

    navBarHeight: Platform.OS === 'android' ? PlatformConfig.default.navBarHeight+StatusBar.currentHeight : PlatformConfig.default.navBarHeight,
    X : (widthScreen<heightScreen? widthScreen : heightScreen)/ ((screenSizeByInch<7)?9.25:12) ,
    serviceOrderSystem: ['5d4cea1268731c9493253a91', '5df993f87709dc2a56912b09'],
    serviceBike: ['5d7717128b162864317d4980', '5db9c9a934033834fda81e9d'],
    serviceFood: ['5d4cea5468731c9493253bb9', '5ecb8ef5e5cc236f417bae25'],
    serviceErrand: ['5e8d575ded99aa22acde13e2', '5ecb90e2e5cc236f417bae2a', '60b9ffa2ceeaad1c947386a8'],
    serviceCar: ['5d7717128b162864317d4981'],
    pendingPaymentMethods: ['momo','zalo', 'mb', 'shopee', 'tokenization'],
    serviceHubOrder: ['6096369fa9b6fb64b4b7fddd'],
    serviceMarket: ['613ef6b37b4adc4587d630e5', '615c43506461254304f9712e', '615ee6d19b619727c7c67100', '615ee64d9b619727c7c670ff', '615dd0859b619727c7c67072', '609f759d0f5f34653f164ba5'],
    serviceHireDriver: ['6603cfa4786b6014fdb90f63'],
    serviceHeyCare: ['663499f98a901e3737c2f22c'],
    serviceCleaning: ['66a0771147950f7c9da8f1be'],
    ...serverInf,

    apiVersion:2,

    serverApiToken:'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************.38hjO99PEhzk1IT8l16zbKemikhPHHAqZzsSw8lmWtE',
    apiKeyGoogleMap: 'AIzaSyBIidO3qxiwdjdX_GT1fLRvfGM8E8D4WIc',
    googleIDTrack: '***********-1',
    font:PlatformConfig.default.font,
    fontBold:PlatformConfig.default.fontBold,
    fontBold400:PlatformConfig.default.fontBold400,
    fontBold500:PlatformConfig.default.fontBold500,
    fontBold600:PlatformConfig.default.fontBold600,
    fontItalic:PlatformConfig.default.fontItalic,
    dataBase:'database.db',
    // crashLog: RNFS.DocumentDirectoryPath + '/CrashLog.txt',
    // trackingLog: RNFS.DocumentDirectoryPath + '/TrackingLog.txt',
    alarmListTable:'AlarmList',
    footballTeamsTable:'FootballTeams',
    signoutBeforeDisconnect:true,
    accountTest:{
      user:'',   // TODO : must = '' when release
      pass:'',
    },
    getMoreHeight:100,
    getMoreHeightMin:1,
    timeoutToHideContent:5000,
    timeoutToHideContent2:10000,
    elevation:3,
    periodOfAccelerometer:1000,
    requestTimeout:26000,
    debugStyle:false,
    review:false,
    debugTrackerLogLength:166,
    funnyMode:false,
    timeIntervalOrder: 1000,
    timeIntervalComment: 3000,
    defaultLocation: {
      latitude: 21.**************,
      longitude: 105.*************
    },
    defaultLocationDelta: {
      latitudeDelta: 0.0072,
      longitudeDelta: 0.0081,
    },
    defaultLocationDeltaShowShipper: {
      latitudeDelta: 0.01,
      longitudeDelta: 0.005,
    },
    defaultAvatar: "https://media.heyu.asia/uploads/notify/2019-11-09-avt-default.png",
    logoVNPAY: "https://media.heyu.asia/uploads/notify/2019-12-28-vnpay.png",
    logoRecharge: "https://media.heyu.asia/uploads/valentine/2020-03-18-recharge3.png",
    logoCharge: "https://media.heyu.asia/uploads/valentine/2020-03-18-charge2.png",
    mapStyle: mapStyle.default,
    versionCodePush: '',
    workhard: '0OASx05M0dEppbcd'
  },
  servers: {},
  config:{
    properties:{
      dtid: "0",
      spid: "0",
    },
    currentHybridVersion:0,
    waitToken: true,
    token: '',
    voipToken: ''
  },
  init:function(cb=()=>{}){
    var self = this;

    if (Platform.OS === 'android') {
        self.constants.heightOfStatusBarAndroid = StatusBar.currentHeight;
        self.constants.heightOfSoftMenuBarAndroid = screenHeight-heightScreen;

        self.constants.availableHeightScreen= heightScreen-StatusBar.currentHeight;
    }

    if (self.constants.debug) {
      self.assets = assets;
    }

    var assetsContent={};
    if (Platform.OS === 'android') {
      // get a list of files and directories in the main bundle
      RNFS.readDir(RNFS.DocumentDirectoryPath+'/ASSETS')
        .then((result) => {
          result.forEach((current)=>{
            try{
              var fileNameArray = current.name.split('.');
              assetsContent[fileNameArray[0]] = current;
            }
            catch(ex){}
          })
          self.assets = PlatformConfig.default.processAsset(assets,mapAssets,assetsContent);
          return Promise.reject();
        })
      .catch(()=>{
        if (cb && typeof cb === 'function') {
          cb();
        }
      })
    } else if(Platform.OS === 'ios') {
      if (cb && typeof cb === 'function') {
        return cb();
      }

      const path = 'file://'+RNFS.DocumentDirectoryPath+'/assets';
      RNFS.exists(path)
        .then((isExist) => {
          if(isExist) {
            self.assets = PlatformConfig.default.processAsset(assets,mapAssets, 'assets', true);
          }
          return Promise.reject();
        })
        .catch(()=>{
          if (cb && typeof cb === 'function') {
            cb();
          }
        })
    }
    return self;
  },
};

module.exports = Define;
