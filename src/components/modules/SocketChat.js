import _ from 'lodash';
import socketIOClient from 'socket.io-client';
import {globalVariableManager} from './GlobalVariableManager';
import RDActions from '../../actions/RDActions';
import ChatActions_MiddleWare from '../../actions/ChatActions_MiddleWare';
import TrackPlayer from 'react-native-track-player'; // Use TrackPlayer instead
const Define = require('../../Define');
var NotifyUtil = require('../../Util/notify');
import EventEmitter from "react-native/Libraries/vendor/emitter/EventEmitter";
const ms = require('ms');

// Remove Sound.setCategory('Playback', true);

import {
  Platform,
  AppState,
  Vibration
} from 'react-native';
import NetInfo from "@react-native-community/netinfo";

import {popupActions} from '../popups/PopupManager';
import DefaultPopup from '../popups/DefaultPopup'

const SOCKET_STATUS = {
  CONNECTING: 1,
  CONNECTED: 2,
  ERROR: 3
}

const MAX_TIME_PICKING_IMG = ms('5m');

class SocketChatManager extends EventEmitter {
  constructor() {
    super();

    this.socketURL = Define.servers[Define.constants.serverSocketChatAddr] || Define.constants.serverSocketChatAddr;
    this.socket = null;
    this.timeoutTyping = {};
    this.statusSocket = SOCKET_STATUS.CONNECTING;
    this.currentNetInfoType = '';
    this.handleAppStateChange = this.handleAppStateChange.bind(this);
    this.handleFirstConnectivityChange = this.handleFirstConnectivityChange.bind(this);
    this.isPickingPhoto = false;
  }

  init() {
    this.emit('connecting');
    this.connectSocket();
    this.initTrackPlayer();
    this.appStateSubscription = AppState.addEventListener('change', this.handleAppStateChange);
    this.unsubscribeNetInfo = NetInfo.addEventListener(this.handleFirstConnectivityChange);
    NetInfo.fetch().then((connectionInfo) => {
      this.currentNetInfoType = connectionInfo.type;
    });
  }
  async initTrackPlayer() {
    await TrackPlayer.setupPlayer();
  }
  async playMessageSound() {
    try {
      await TrackPlayer.reset();
      await TrackPlayer.add({
        id: 'message',
        url: require('../../../assets/message.mp3'), // adjust path as needed
        title: 'Message',
        artist: 'App',
      });
      await TrackPlayer.play();
    } catch (e) {
      // handle error if needed
    }
  }

  handleAppStateChange(currentAppState) {
    switch (currentAppState) {
      case 'active': {
        if(!this.isPickingPhoto) {
          setTimeout(() => {
            this.socket.open();
          }, 1000);
        }
        break;
      }

      case 'background': {
        if(!this.isPickingPhoto) {
          this.socket.disconnect();
        }
        break;
      }

      default:
    }
  }

  handleFirstConnectivityChange(connectionInfo) {
    if(connectionInfo.type === 'none') {
      this.socket.disconnect();
    } else {
      if(this.currentNetInfoType !== 'none') {
        this.socket.disconnect();
      }

      this.socket.open();
    }

    this.currentNetInfoType = connectionInfo.type;
  }

  connectSocket() {
    this.socket = socketIOClient(this.socketURL, {
      transports: ['websocket'],
      jsonp: false,
      reconnection: true,
      query: {
        region: globalVariableManager.reduxManager.state.AppSetting.regionNew,
        mode: globalVariableManager.reduxManager.state.AppSetting.mode,
        memberToken: _.get(globalVariableManager, 'reduxManager.state.User.memberInfo.member.memberToken', ''),
        nativeVersion: Define.constants.nativeVersion,
        versionCodePush: Define.constants.versionCodePush,
        appName: Define.constants.appName
      }
    });

    this.socket.on('reconnect_attempt', () => {
      this.socket.io.opts.query = {
        region: globalVariableManager.reduxManager.state.AppSetting.regionNew,
        mode: globalVariableManager.reduxManager.state.AppSetting.mode,
        memberToken: _.get(globalVariableManager, 'reduxManager.state.User.memberInfo.member.memberToken', ''),
        nativeVersion: Define.constants.nativeVersion,
        versionCodePush: Define.constants.versionCodePush,
        appName: Define.constants.appName
      }
    })

    this.socket.on('connect', (data) => {
      // console.log('socket:connect', data);
      this.login();
    });

    this.socket.on('connect_error', (err) => {
      // console.log('socket:connect_error', err);
      this.emit('error', err);
      this.statusSocket = SOCKET_STATUS.ERROR;
    });

    this.socket.on('error', (err) => {
      // console.log('socket:error', err);
      this.emit('error', err);
      this.statusSocket = SOCKET_STATUS.ERROR;
    });

    this.socket.on('disconnect', (reason) => {
      // console.log('socket:disconnect', reason);
      this.emit('error');
      this.statusSocket = SOCKET_STATUS.ERROR;
    });

    this.socket.on('reconnecting', (data) => {
      // console.log('socket:reconnecting', data);
      this.emit('connecting');
      this.statusSocket = SOCKET_STATUS.CONNECTING;
    });

    this.socket.on('newMessage', (data) => {
      this.playMessageSound(); // Play sound using TrackPlayer
      Vibration.vibrate();

      if(globalVariableManager.reduxManager.state.Chat.conversations[data.conversation]) {
        const currentConversation = globalVariableManager.reduxManager.state.Chat.conversations[data.conversation];
        currentConversation.latestMessage = {
          _id: data.id,
          message: data.message,
          imageUrl: data.imageUrl || '',
          senderId: data.senderId,
          seen: 0
        };
        if(data.location){
          currentConversation.latestMessage.location = data.location
        }
        currentConversation.updatedAt = data.time || Date.now();
        globalVariableManager.reduxManager.dispatch(RDActions.Chat.newMessage(currentConversation));
      } else {
        const currentScreenName = globalVariableManager.reduxManager.state.Navigator.currentScreen.name;
        const stack = globalVariableManager.reduxManager.state.Navigator.stack;
        if(currentScreenName === 'ChatScreen' && stack[stack.length - 2] !== 'ChatListScreen') {
          if (data.conversation) {
            globalVariableManager.reduxManager.dispatch(ChatActions_MiddleWare.getConversationInf({ id: data.conversation }))
              .then((result) => {
                globalVariableManager.reduxManager.dispatch(RDActions.Chat.newMessage(result.res.data));
              })
              .catch((err) => {
                globalVariableManager.reduxManager.dispatch(RDActions.Chat.newMessage({
                  _id: data.conversation,
                  latestMessage: {
                    message: data.message,
                    imageUrl: data.imageUrl || '',
                    senderId: data.senderId,
                    seen: 0
                  },
                  users: [{
                    _id: data.senderId,
                    facebook: {
                      name: 'UNKNOWN SENDER',
                      picture: 'http://is3.mzstatic.com/image/thumb/Purple128/v4/76/d3/d4/76d3d401-1617-f110-1d40-c7a2ef673f36/source/175x175bb.jpg'
                    }
                  }],
                  updatedAt: data.time || Date.now()
                }));
              });
          } else {
            // Show notify
            const notiObj = {
              _id: Date.now(),
              notifiedAt: Date.now(),
              title: 'HeyU',
              description: `Bạn có tin nhắn mới: ${data.message.substring(0, 20)}`,
              icon: 'http://www.iconarchive.com/download/i76811/wineass/ios7-redesign/Messages.ico',
              link: 'ChatListScreen',
              extras: {
                conversation: data.conversation
              }
            }
            NotifyUtil.pushNotify(notiObj)
          }
          return;
        }

        globalVariableManager.reduxManager.dispatch(ChatActions_MiddleWare.getConversationInf({id: data.conversation}))
          .then((result) => {
            globalVariableManager.reduxManager.dispatch(RDActions.Chat.newMessage(result.res.data));
          })
          .catch((err) => {
            globalVariableManager.reduxManager.dispatch(RDActions.Chat.newMessage({
              _id: data.conversation,
              latestMessage: {
                message: data.message,
                imageUrl: data.imageUrl || '',
                senderId: data.senderId,
                seen: 0
              },
              users: [{
                _id: data.senderId,
                facebook: {
                  name: 'UNKNOWN SENDER',
                  picture: 'http://is3.mzstatic.com/image/thumb/Purple128/v4/76/d3/d4/76d3d401-1617-f110-1d40-c7a2ef673f36/source/175x175bb.jpg'
                }
              }],
              updatedAt: data.time || Date.now()
            }));
          });
      }
    });

    this.socket.on('typingMessage', (data) => {
      if(this.timeoutTyping[data.conversation]) {
        clearTimeout(this.timeoutTyping[data.conversation]);
      }

      globalVariableManager.reduxManager.dispatch(RDActions.Chat.typingMessage({conversation: data.conversation, value: 1}));
      this.timeoutTyping[data.conversation] = setTimeout(() => {
        delete this.timeoutTyping[data.conversation];
        globalVariableManager.reduxManager.dispatch(RDActions.Chat.typingMessage({conversation: data.conversation, value: 0}));
      }, 5000);
    });

    this.socket.on('seenMessage', (data) => {
      globalVariableManager.reduxManager.dispatch(RDActions.Chat.seenMessage({
        id: data.messageId,
        conversation: data.conversation
      }));
    });
  }

  destroy() {
    if(this.socket) {
      this.socket.disconnect();
      this.appStateSubscription.remove();
      this.unsubscribeNetInfo();
    }
    // No need to release TrackPlayer
  }

  setPickingPhoto(data) {
    if(Platform.OS === 'ios') {
      return;
    }
    if(!data) {
      this.isPickingPhoto = false;
      if((Date.now() - this.timeStartPicking) >= MAX_TIME_PICKING_IMG) {
        this.socket.disconnect();
      }
    } else {
      this.isPickingPhoto = true
      this.timeStartPicking = Date.now();
    }
  }


  sendMessage(arg) {
    return new Promise((resolve, reject) => {
      if(!arg._id) {
        const fakeId = Date.now() + '';
        arg._id = fakeId;
        globalVariableManager.reduxManager.dispatch(RDActions.Chat.sendMessage(arg));
      } else {
        globalVariableManager.reduxManager.dispatch(RDActions.Chat.reSendMessage(arg));
      }

      if(!arg.conversation) {
        globalVariableManager.reduxManager.dispatch(RDActions['AppState']['showLoadingOnRequest']({show: true}))
      }
      let appInfo ={
        platform: Platform.OS,
        version: Define.constants.nativeVersion,
        versionCodePush: Define.constants.versionCodePush,
        regionName: globalVariableManager.reduxManager.state.AppSetting.regionNew,
        callToApp: arg.callToApp,
      }
      this.socket.emit('sendMessage', {message: arg.newMessage, conversation: arg.conversation, receiverId: arg.receiverId, file: arg.file, idOrder: arg.idOrder, location: arg.location, appInfo: appInfo, callToApp: arg.callToApp}, (dataBack) => {
        if(!arg.conversation) {
          globalVariableManager.reduxManager.dispatch(RDActions['AppState']['showLoadingOnRequest']({show: false}))
        }

        if(dataBack && dataBack.code === 200) {
          globalVariableManager.reduxManager.dispatch(RDActions.Chat['sendMessage' + 'OnResult']('SUCCESS', {
            arg: arg,
            data: dataBack
          }))

          return resolve(dataBack);
        } else {
          globalVariableManager.reduxManager.dispatch(RDActions.Chat['sendMessage' + 'OnResult']('ERROR', {
            arg: arg,
            data: dataBack
          }))

          return reject();
        }
      });
    });
  }

  typingMessage(conversation) {
    this.socket.emit('typingMessage',{conversation});
  }

  seenMessage(messageInfo) {
    this.socket.emit('seenMessage', {id: messageInfo.id}, (dataBack) => {
      if(dataBack && dataBack.code === 200) {
        globalVariableManager.reduxManager.dispatch(RDActions.Chat.seenMessage(messageInfo));
        if(messageInfo && messageInfo.senderId) {
          globalVariableManager.reduxManager.dispatch(RDActions.FeedsSystem.seenNewMessageOrder({
            senderId:messageInfo.senderId,
            mode: _.get(globalVariableManager, 'reduxManager.state.AppSetting.mode', '')
          }))
          globalVariableManager.reduxManager.dispatch(RDActions.HeyCare.seenNewMessageOrder({
            senderId: messageInfo.senderId,
            mode: _.get(globalVariableManager, 'reduxManager.state.AppSetting.mode', '')
          }))
          globalVariableManager.reduxManager.dispatch(RDActions.Cleaning.seenNewMessageOrder({
            senderId: messageInfo.senderId,
            mode: _.get(globalVariableManager, 'reduxManager.state.AppSetting.mode', '')
          }))
        }
      }
    });
  }

  login() {
    const memberToken = _.get(globalVariableManager, 'reduxManager.state.User.memberInfo.member.memberToken', '');
    if(memberToken) {
      this.socket.emit('login', {memberToken}, (data) => {
        if(data && data.code === 200) {
          this.statusSocket = SOCKET_STATUS.CONNECTED;
          return this.emit('connected');
        }

        this.handleError();
      })
    } else {
      this.handleError();
    }
  }

  handleError(err) {
    setTimeout(() => {
      this.destroy();
      this.init();
    }, 2000);

    this.emit('error');
    this.statusSocket = SOCKET_STATUS.ERROR;
  }
}

module.exports = SocketChatManager;
