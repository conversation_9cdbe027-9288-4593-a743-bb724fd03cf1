
var _ = require('lodash')
//LIB
import React  from 'react';
import {
  View,
  Image,
  Text,
  ActivityIndicator,
  TouchableOpacity,
  Platform
} from 'react-native';

// import { connect } from 'react-redux';

//action

//components
var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Util = require('../../Util/Util');
// var Themes = require('../../Themes');
// var Include = require('../../Include');
var ButtonWrap= require('../elements/ButtonWrap');
import {Ionicons as Icon} from '@react-native-vector-icons/ionicons';

var {popupActions} = require('../popups/PopupManager');
var {globalVariableManager}= require('../modules/GlobalVariableManager');

//Actions
// var Actions_MiddleWare = require('../../actions/Actions_MiddleWare');
// var RDActions = require('../../actions/RDActions');
// import UploadFilePopup from '../popups/UploadFilePopup';

class UploadFileManager{
  // static defaultProps = {}
  // static propTypes = {}
  constructor(){
    this.fileStorage = {}
    this.holdRef = {}
    this.handleDisplayComponent = this.handleDisplayComponent.bind(this);
  }

  declineFile(id) {
    const memberToken = _.get(globalVariableManager, 'reduxManager.state.User.memberInfo.memberToken', '');
    const nativeVersion =  Define.constants.nativeVersion;
    const modeApp = _.get(globalVariableManager, 'reduxManager.state.AppSetting.mode', '');
    const regionName = _.get(globalVariableManager,'reduxManager.state.AppSetting.regionNew','');
    const versionCodePush = Define.constants.versionCodePush;
    const tShoot = Date.now();
    const appName =  Define.constants.appName;
    const payload = {
      url: this.fileStorage[id].url,
      memberToken,
      nativeVersion,
      modeApp,
      regionName,
      versionCodePush,
      tShoot,
      appName
    }

    fetch(`${Define.constants.serverMediaAddr}/api/v1.0/decline-file`,{
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
        goal: Util.getGoal('/api/v1.0/decline-file',JSON.stringify(payload)),
        token: memberToken
      },
      body: JSON.stringify(payload)
    })
    .then((result) => {
      delete this.fileStorage[id]
    })
    .catch((err) => {});
  }

  addFile(id, type, file) {
    let objType = {};
    objType[type] = file;
    this.fileStorage[id] = objType
  }

  getComponent(id, closeObj, sendObj ) {

    return(
      <View ref={ref => this.holdRef[id] = ref} style={{flex:1, opacity:0.5}}>
        <Image
          style={{flex: 1,
          width: null,
          height: null,
          resizeMode: 'contain'}}
          source={{uri:this.getFile(id)['local']}}
        />
        {closeObj ?
          <View
            style={{display:'flex',alignItems:'center', justifyContent:'center', width:30, height:30, borderRadius:15, backgroundColor:'#0984e3', top: 0, right: 0, position: 'absolute', ...closeObj.buttonStyle}}
          >
            <TouchableOpacity
              onPress={() => {
                closeObj.onPressClose();
              }}
            >
              <View
                style={{flex:1, justifyContent:'center', alignItem:'center'}}>
                <Icon name='close' style={{fontSize:20, color:'#fff', ...closeObj.iconStyle}} />
              </View>
            </TouchableOpacity>
          </View>
        :null}
        {sendObj?
          <View
            style={{display:'flex', height:70, width:150, flexDirection:'row', backgroundColor:'#fff', borderRadius:35, alignItems: 'center', alignSelf:'center', justifyContent:'center', position: 'absolute', top:Define.constants.heightScreen/2-50}}>
            <TouchableOpacity
              onPress={() => {
                sendObj.onPressSend();
              }}
            >
              <View
                style={{height:70, width:150, flexDirection:'row', backgroundColor:'#fff', borderRadius:35, alignItems: 'center', alignSelf:'center', justifyContent:'center'}}
              >
                <Text allowFontScaling={false} style={{color:'#0984e3', fontSize:25}}>GỬI ẢNH</Text>
              </View>
            </TouchableOpacity>
          </View>
        :null}
      </View>
    )
  }

  getFile(id) {
    return this.fileStorage[id];
  }

  handleDisplayComponent(id) {
    if(this.holdRef[id]) {
      this.holdRef[id].setNativeProps({
        style: {
          opacity: 1
        }
      })
    }
  }

  upload(arg){

    var argFormat={
      fileUpload: '',
    }
    var argTemp = Util.dataProtectAndMap(arg, argFormat);

    var req ;
    // req config
    var temp = new FormData();
    const memberToken = _.get(globalVariableManager, 'reduxManager.state.User.memberInfo.memberToken', '');
    const nativeVersion =  Define.constants.nativeVersion;
    const modeApp = _.get(globalVariableManager, 'reduxManager.state.AppSetting.mode', '');
    const regionName = _.get(globalVariableManager,'reduxManager.state.AppSetting.regionNew','');
    const versionCodePush = Define.constants.versionCodePush;
    const tShoot = Date.now();
    const appName =  Define.constants.appName;

    if(arg.fileUpload) {
      temp.append('fileUpload', {
        uri: arg.fileUpload,
        name: arg.fileName ? arg.fileName : "image.jpg",
        type: 'multipart/form-data'
      })
      temp.append('folder', arg.folder);
      temp.append('memberToken',memberToken);
      temp.append('nativeVersion',nativeVersion);
      temp.append('modeApp',modeApp);
      temp.append('regionName',regionName);
      temp.append('versionCodePush',versionCodePush);
      temp.append('appName',appName);
      temp.append('tShoot',tShoot);
    }
    req = temp;
    //
    const payload = {
      folder: arg.folder,
      memberToken,
      nativeVersion,
      modeApp,
      regionName,
      versionCodePush,
      tShoot,
      appName
    }

    var data = {};
    var promise = new Promise((resolve,reject)=>{

      const serverAddr = Define.constants.serverMediaAddr
      const preLinkApi = '/api/v1.0'
      const query = '/upload-single'
      fetch(`${serverAddr}${preLinkApi}${query}`,{
        method: 'post',
        headers: {
          'Content-Type': 'multipart/form-data',
          goal: Util.getGoal('/api/v1.0/upload-single',JSON.stringify(payload)),
          token: memberToken
        },
        body: req
      })
      .then((response) => {
        return response.json()
      })
      .then((response)=>{
        if(response.code === 200) {
          var res = response;
          data={
            arg:argTemp,
            res:res,
          }
          this.addFile(arg.id, 'url', res.filename ? res.filename : '')
          this.handleDisplayComponent(arg.id);
          resolve(data);
        }else{
          if(argTemp.fileUpload) {
            globalVariableManager.rootView.showToast('Gửi ảnh thất bại');
          }
          return Promise.reject(response)
        }
      })
      .catch((err)=>{
        if(argTemp.fileUpload) {
          globalVariableManager.rootView.showToast('Gửi ảnh thất bại');
        }
        data={
          arg:argTemp,
          err:JSON.stringify(err),
          res:err,
          errObj:err,
        }
        reject(data);
      })
    })
    return promise;
  }

}

const uploadFileManager = new UploadFileManager()
module.exports = uploadFileManager;
