var _ = require('lodash');
import { Platform, PermissionsAndroid, AppState, Linking } from 'react-native';

const Debug = require('../../Util/Debug');
const Util = require('../../Util/Util');
import { globalVariableManager } from './GlobalVariableManager';
import { connect } from 'react-redux';
var { Actions } = require('react-native-router-flux');
import RDActions from '../../actions/RDActions';
const Define = require('../../Define');
import { navigationRef } from '../../../RootNavigation';
import { TabActions } from '@react-navigation/native';
var {
  PopupManager,
  popupActions,
  popupConst
} = require('../popups/PopupManager');
const NotificationsActions_MiddleWare = require('../../actions/NotificationsActions_MiddleWare');
const UserActions_MiddleWare = require('../../actions/UserActions_MiddleWare');
import DefaultPopup from '../popups/DefaultPopup';
import Popup from '../popups/Popup';
// import WebRTC from '../popups/WebRTC';
import WebviewCommentPopup from '../popups/WebviewCommentPopup';
import EventEmitter from "react-native/Libraries/vendor/emitter/EventEmitter";

class NavigatorManager extends EventEmitter {
  constructor() {
    super();

    this.config();
    this.DefaultPopup = DefaultPopup;
    this.Popup = Popup;
    // this.WebRTCPopup = WebRTC;
    this.WebviewCommentPopup = WebviewCommentPopup;
  }

  config() {
    // this.forcePopup = ['WebRTC', 'WebRTCPopup'];

    this.forceScreen = [];
  }

  handleNavigator(link, extras = {}, fromSocket) {
    const {AppSetting, Navigator} = globalVariableManager.reduxManager.state;
    if (!link) {
      return;
    }

    if(link === 'ChatListScreen') {
      link = 'ChatScreen';
    }

    if(link === 'Linking'){
      this.handleOpenBrowser(extras)
    }

    const isScreen = this.checkIsScreen(link);

    if (isScreen) {
      this.handleOpenScreen(link, extras);
    } else {
      this.handleOpenPopup(link, extras);
    }
  }

  handleOpenBrowser(extras){
    if(extras && extras.link){
      Linking.openURL(extras.link)
    }
  }

  handleOpenScreen(link, extras) {
    const {AppSetting, Navigator} = globalVariableManager.reduxManager.state;
    let extrasSend = {
      ...extras,
      time: Date.now()
    }

    if (link === 'MainContainer' && extras.tabIndex) {
      const tabNames = ['Trang chủ', 'Nhiệm vụ', 'Điểm danh', 'Báo cáo', 'Cá nhân'];
      const tabName = tabNames[extras.tabIndex];
      navigationRef.goBack();
      navigationRef.dispatch(TabActions.jumpTo(tabName));
    }
    else {
      navigationRef.navigate(link, extrasSend);
    }
    this.handleAddDefaultOriginPlaceFromNotify(link, extrasSend)
  }
  handleAddDefaultOriginPlaceFromNotify(link, extras) {
    if(link === "DefaultLocationForShopScreen") {
      if(extras && extras.lat && extras.lng && extras.locationName
        && typeof extras.lat === 'number'
        && typeof extras.lng === 'number'
      ) {
        const defaultOriginPlace = {
          location: {
            lat: extras.lat,
            lng: extras.lng,
          },
          name: extras.locationName
        }
        globalVariableManager.reduxManager.dispatch(UserActions_MiddleWare.addDefaultOrigin(defaultOriginPlace));
      }
    }
  }

  handleOpenPopup(link, extras) {
    const appSetting = globalVariableManager.reduxManager.state.AppSetting;
    if(this[link]) {
      popupActions.setRenderContentAndShow(this[link], extras);
    }
  }

  checkIsScreen(link) {
    let isScreen = false;
    if (link.includes('Screen') || link.includes('Container')) {
      isScreen = true;
    }

    return isScreen;
  }
}

const navigatorManager = new NavigatorManager();

module.exports = { navigatorManager };
