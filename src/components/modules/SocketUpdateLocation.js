import _ from 'lodash';
import socketIOClient from 'socket.io-client';
import {globalVariableManager} from './GlobalVariableManager';
import RDActions from '../../actions/RDActions';
const Define = require('../../Define');
import EventEmitter from "react-native/Libraries/vendor/emitter/EventEmitter";

import { AppState } from 'react-native';
import NetInfo from "@react-native-community/netinfo";

import {popupActions} from '../popups/PopupManager';
import DefaultPopup from '../popups/DefaultPopup'

const SOCKET_STATUS = {
  CONNECTING: 1,
  CONNECTED: 2,
  ERROR: 3
}

class SocketUpdateLocationManager extends EventEmitter {
  constructor() {
    super();

    this.socketURL = Define.servers[Define.constants.serverSocketLocationAddr] || Define.constants.serverSocketLocationAddr;
    this.socket = null;
    this.timeoutTyping = {};
    this.statusSocket = SOCKET_STATUS.CONNECTING;
    this.currentNetInfoType = '';
    this.handleAppStateChange = this.handleAppStateChange.bind(this);
    this.handleFirstConnectivityChange = this.handleFirstConnectivityChange.bind(this);

    this.listFnc = [];
  }

  init() {
    this.connectSocket();
    this.appStateSubscription = AppState.addEventListener('change', this.handleAppStateChange);
    this.unsubscribeNetInfo = NetInfo.addEventListener(this.handleFirstConnectivityChange);
    NetInfo.fetch().then((connectionInfo) => {
      this.currentNetInfoType = connectionInfo.type;
    });
  }

  handleAppStateChange(currentAppState) {
    switch (currentAppState) {
      case 'active': {
        setTimeout(() => {
          this.socket.open();
        }, 1000);
        break;
      }

      case 'background': {
        this.socket.disconnect();
        break;
      }

      default:
    }
  }

  handleFirstConnectivityChange(connectionInfo) {
    if(connectionInfo.type === 'none') {
      this.socket.disconnect();
    } else {
      if(this.currentNetInfoType !== 'none') {
        this.socket.disconnect();
      }

      this.socket.open();
    }

    this.currentNetInfoType = connectionInfo.type;
  }

  listenForUpdateLocation(fn) {
    this.listFnc.push(fn);
  }

  connectSocket() {
    this.socket = socketIOClient(this.socketURL, {
      transports: ['websocket'],
      reconnection: true,
      query: {
        region: globalVariableManager.reduxManager.state.AppSetting.regionNew,
        mode: globalVariableManager.reduxManager.state.AppSetting.mode,
        memberToken: _.get(globalVariableManager, 'reduxManager.state.User.memberInfo.member.memberToken', ''),
        nativeVersion: Define.constants.nativeVersion,
        versionCodePush: Define.constants.versionCodePush,
        appName: Define.constants.appName
      }
    });

    this.socket.on('reconnect_attempt', () => {
      this.socket.io.opts.query = {
        region: globalVariableManager.reduxManager.state.AppSetting.regionNew,
        mode: globalVariableManager.reduxManager.state.AppSetting.mode,
        memberToken: _.get(globalVariableManager, 'reduxManager.state.User.memberInfo.member.memberToken', ''),
        nativeVersion: Define.constants.nativeVersion,
        versionCodePush: Define.constants.versionCodePush,
        appName: Define.constants.appName
      }
    })

    this.socket.on('connect', (data) => {
      this.statusSocket = SOCKET_STATUS.CONNECTED;
      this.emit('connected');
    });

    this.socket.on('location_update', (location) => {
      this.listFnc.map(fn => {
        fn({...location, latitude: location.lat, longitude: location.lng});
      });
    });
  }

  join(roomId) {
    if(this.socket) {
      this.socket.emit('join', roomId);
    }
  }

  leave(roomId) {
    if(this.socket) {
      this.socket.emit('leave', roomId);
    }
  }

  destroy() {
    if(this.socket) {
      this.socket.disconnect();
      this.appStateSubscription.remove();
      this.unsubscribeNetInfo();
    }
  }
}

module.exports = SocketUpdateLocationManager;
