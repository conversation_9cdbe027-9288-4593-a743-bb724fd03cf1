
var _ = require('lodash')
//LIB

// import {
//   View,
//   InteractionManager
// } from 'react-native';

// import { connect } from 'react-redux';

//action

//components
//var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Util = require('../../Util/Util');
// var Themes = require('../../Themes');
// var Include = require('../../Include');

var {popupActions} = require('../popups/PopupManager');
var {globalVariableManager}= require('../modules/GlobalVariableManager');

//Actions
// var Actions_MiddleWare = require('../../actions/Actions_MiddleWare');
// var RDActions = require('../../actions/RDActions');

export default class TemplateModule {
  // static defaultProps = {}
  // static propTypes = {}
  constructor(){    
  }

}
