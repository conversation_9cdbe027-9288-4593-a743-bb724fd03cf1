import * as _ from 'lodash'
import {
  Alert,
  AppState,
  Platform,
  Linking
 } from 'react-native';
const {globalVariableManager} = require('../modules/GlobalVariableManager');
const AppStateActions_MiddleWare = require('../../actions/AppStateActions_MiddleWare');
const DeviceInfo = require('react-native-device-info');
import NetInfo from "@react-native-community/netinfo";

class BackgroundLocationManager {
  constructor() {
    this.currentLocation = null
    this.init();

    this.stateRunning = false;
    this.config = null;
  }

  init() {
    // Handle appState
    this.appStateSubscription = AppState.addEventListener('change', this.handleAppStateChange.bind(this));
  }

  handleAppStateChange(newState) {
    switch (newState) {
      case 'active':
        setTimeout(() => {
        }, 500);

        break;
      case 'inactive':
        break;
      case 'background':
        break;
      default:
        break;
    }
  }

  showAppSettings() {
    Linking.openSettings();
  }
}


const locationManager = new BackgroundLocationManager()

module.exports= locationManager;
