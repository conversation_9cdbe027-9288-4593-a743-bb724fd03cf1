var _ = require('lodash')
import {Platform, NativeModules, PermissionsAndroid, Linking} from 'react-native';
import PushNotificationIOS from "@react-native-community/push-notification-ios";
// import VoipPushNotification from 'react-native-voip-push-notification';
var RNIntent = NativeModules.RNIntent;
import PermissionPopup from '../popups/PermissionPopup';
var { popupActions, popupConst } = require('../popups/PopupManager');
const { globalVariableManager } = require('../modules/GlobalVariableManager');
import RDActions from '../../actions/RDActions'
const Define = require('../../Define');

class PushNotiManager {
  // static defaultProps = {}
  // static propTypes = {}
  constructor() {
    this.handlePermission = this.handlePermission.bind(this);
    this.checkAndRequestPermissionNotify = this.checkAndRequestPermissionNotify.bind(this);
  }
  checkAndRequestPermissionNotify(){
    return new Promise((resolve,reject)=>{
      if (Platform.OS === 'android' && Platform.Version >= 33) {
        PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS)
        .then((ret)=>{
          if (ret) {
            return Promise.resolve(true);
          }else{
            return PermissionsAndroid.request(
              PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS
            )
          }
        })
        .then((granted)=>{
          if (granted) {
            resolve()
          }else{
            reject()
          }
        })
      }else{
        resolve()
      }
    })
  }
  openAppSetting() {
    Linking.openSettings();
  }

  handlePermission() {
    if (Platform.OS === 'ios') {
      PushNotificationIOS.requestPermissions()
      .then(() => {
        // VoipPushNotification.requestPermissions();
      })
    } else {
      this.checkAndRequestPermissionNotify()
    }

    globalVariableManager.reduxManager.dispatch(RDActions.AppSetting.checkPopupNativeNotify(1))
  }

  handlePopup() {
    let detail = 'Để có thể nhận thông tin từ ứng dụng và chính quyền bạn cần phải cho phép ứng dụng gửi thông báo cho bạn.';
    if (globalVariableManager.reduxManager.state.AppSetting.openedNativeNotiPopup) {
      detail += ' Bạn vui lòng mở chức năng Thông báo trong Cài đặt. Xin cảm ơn.'
    }
    let popupShown = _.includes(popupActions.getPopupStack(), 'PermissionPopup');

    const showPopup = () => {
      if (!popupShown) {
        popupActions.setRenderContentAndShow(PermissionPopup, {
          title: `Nhận thông báo từ ${Define.constants.displayName}`,
          detail,
          textButton: 'Đồng ý nhận thông báo',
          image: 'https://media.heyu.asia/uploads/new-img-service/2021-06-23-iconNoti.png',
          onPress: !globalVariableManager.reduxManager.state.AppSetting.openedNativeNotiPopup ? this.handlePermission : this.openAppSetting,
        })
      }
    }

    const hidePopup = () => {
      if (popupShown) {
        popupActions.popPopup(undefined, undefined, popupConst.PERMISSIONS_GROUP);
      }
    }

    if (Platform.OS === 'ios') {
      PushNotificationIOS.checkPermissions((inf) => {
        if (inf.authorizationStatus !== PushNotificationIOS.AuthorizationStatus.UNAuthorizationStatusAuthorized) {
          showPopup();
        } else {
          hidePopup();
        }
      })
    } else {
      RNIntent.checkAllowNotify((ret)=>{
        if (!ret) {
          showPopup();
        } else {
          hidePopup();
        }
      })
    }
  }

}

const pushNotiManager = new PushNotiManager()
module.exports = pushNotiManager;