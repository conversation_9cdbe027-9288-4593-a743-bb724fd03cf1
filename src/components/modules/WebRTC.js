/**
 * rewebrtc-server project
 *
 * <PERSON><PERSON> <PERSON> <<EMAIL>>
 * Feb 12, 2017
 */
 import {
   RTCPeerConnection,
   RTCIceCandidate,
   RTCSessionDescription,
   RTCView,
   MediaStream,
   MediaStreamTrack,
   mediaDevices
 } from 'react-native-webrtc';
import _ from 'lodash';
import socketIOClient from 'socket.io-client';
import {globalVariableManager} from './GlobalVariableManager';
var Define = require('../../Define');
import {
  Vibration,
  Platform
} from 'react-native'
const noOp = () => {}

class WebRTCManager {
   constructor(options) {
     this.localStream = null;
     this.socketURL = Define.servers[Define.constants.serverSignalWebRTC] || Define.constants.serverSignalWebRTC;
     this.socket = null;
     this.peerConnections = null;
     this.configuration = {
       "iceServers": [
         {"urls": "turn:turn-hcm.heyu.asia:2222", "credential": "123456aA@", "username": "tuanhm"},
         {"urls": "turn:turn.heyu.asia:2222", "credential": "123456aA@", "username": "tuanhm"}
       ]
     };
     this.hasConnect = false;

     // function like callback
     this.onError = noOp;
     this.onFriendConnected = noOp;
     this.onStartCall = noOp;
     this.onConnecting = noOp;
     this.onFriendLeft = noOp;
     this.onRinging = noOp;
     this.onCancel = noOp;
     this.onDisconectVideo = noOp;
     this.onReconnectVideo = noOp;
   }

   connectSocket() {
     return new Promise((resolve, reject) => {
       this.socket = socketIOClient(this.socketURL, {
        transports: ['websocket'],
        jsonp: false, reconnection: true,
        query: {
          region: globalVariableManager.reduxManager.state.AppSetting.regionNew,
          mode: globalVariableManager.reduxManager.state.AppSetting.mode,
          memberToken: _.get(globalVariableManager, 'reduxManager.state.User.memberInfo.member.memberToken', ''),
          nativeVersion: Define.constants.nativeVersion,
          versionCodePush: Define.constants.versionCodePush,
          appName: Define.constants.appName
        }
      });

      this.socket.on('reconnect_attempt', () => {
        this.socket.io.opts.query = {
          region: globalVariableManager.reduxManager.state.AppSetting.regionNew,
          mode: globalVariableManager.reduxManager.state.AppSetting.mode,
          memberToken: _.get(globalVariableManager, 'reduxManager.state.User.memberInfo.member.memberToken', ''),
          nativeVersion: Define.constants.nativeVersion,
          versionCodePush: Define.constants.versionCodePush,
          appName: Define.constants.appName
        }
      })

       this.socket.on('exchange', (data) => {
         this.exchange(data);
       });

       this.socket.on('leave', (data) => {
         this.leave(data);
       });

       this.socket.on('connect', (data) => {
         this.hasConnect = true;
         return resolve();
       });

       this.socket.on('rejectCall', (data) => {
         this.onError({
           message: 'Người dùng đã từ chối cuộc gọi của bạn. Có thể do chất lượng âm thanh không rõ. Vui lòng gọi lại bằng số điện thoại theo cách thông thường'
         })
       });

       this.socket.on('cancelCall', (data) => {
         this.onCancel();
       });

       this.socket.on('userNotReply', (data) => {
         this.onError({
           message: 'Người dùng không phản hồi cuộc gọi của bạn. Vui lòng gọi lại bằng số điện thoại theo cách thông thường'
         })
       })

       this.socket.on('disconnectVideo',() => {
         this.onDisconectVideo()
       })

       this.socket.on('reconnectVideo',() => {
         this.onReconnectVideo()
       })

       this.socket.on('disconnect', (data) => {
       })

       this.socket.on('connect_error', (err) => {
         if(!this.hasConnect) {
           return reject();
         }
       });
     })
   }

   login(memberToken) {
     return new Promise((resolve, reject) => {
       this.socket.emit('login', {memberToken}, (result) => {

         if(result.code === 200) {
           return resolve();
         }

         return reject();
       })
     });
   }

   getLocalStream(video, cameraType) {
     var self = this;
     return new Promise((resolve, reject) => {
       if(cameraType && cameraType === 'environment') {
         isFront = false
       } else {
         isFront = true
       }
       mediaDevices.enumerateDevices().then(sourceInfos => {
        let videoSourceId;
        if(video) {
          for (let i = 0; i < sourceInfos.length; i++) {
            const sourceInfo = sourceInfos[i];
            if(sourceInfo.kind == "videoinput" && sourceInfo.facing == (isFront ? "front" : "environment")) {
            videoSourceId = sourceInfo.deviceId;
            }
          }
        }


        mediaDevices.getUserMedia({
         audio: true,
         video: video ? {
            width: 640,
            height: 480,
            frameRate: 30,
            facingMode: isFront ? "user" : "environment",
            deviceId: videoSourceId
          } : false
         }).then(stream=>{
           self.localStream = stream;
           resolve(stream);
         }).catch(err => {
           reject(err)
         });
       });
     })
   }

   callToMember(userId, video, cameraType, callFrom , callToApp) {
     return new Promise((resolve, reject) => {
      let appInfo ={
        platform: Platform.OS,
        version: Define.constants.nativeVersion,
        versionCodePush: Define.constants.versionCodePush,
        regionName: globalVariableManager.reduxManager.state.AppSetting.regionNew,
        callToApp: callToApp,
      }
       this.socket.emit('callToMember', {userId, video, cameraType, callFrom, appInfo, callToApp }, (result) => {
         if(result.code === 200) {
           this.roomId = result.roomId;

           this.onRinging();
           return resolve();
         }

         if(result.code === 301) {
           return reject({
             message: result.message || 'Người dùng đang bận'
           })
         }

         if(result.code === 302) {
           return reject({
             message: result.message || 'Không thể tạo cuộc gọi với người dùng này'
           })
         }

         return reject();
       });
     })
   }

   answer(userId, roomId, video, cameraType, callFrom,  callbacks) {
     this.roomId = roomId;
     this.createPeerConnection({userId, roomId, video, cameraType, callFrom}, true);
   }

   rejectCall(userId, roomId) {
     if(this.socket) {
       this.socket.emit('rejectCall', {userId, roomId});
     }
   }

   cancelCall() {
     if(this.socket) {
       this.socket.emit('cancelCall');
     }
   }

   exchange(data) {
     var userId = data.from;

     !this.peerConnections && this.createPeerConnection({userId: userId, video: data.video, cameraType: data.cameraType, callFrom: data.callFrom})
     var pc = this.peerConnections;

     if (data.sdp) {
       pc.setRemoteDescription(new RTCSessionDescription(data.sdp)).then(() => {
         if (pc.remoteDescription.type == "offer")
         pc.createAnswer().then(desc => {
           pc.setLocalDescription(desc).then(() => {
             this.socket.emit('exchange', {'to': userId, 'sdp': pc.localDescription, video: data.video , cameraType: data.cameraType, callFrom: data.callFrom });
           }).catch(this.onError)
         }).catch(this.onError)
       }).catch(this.onError)
     } else {
       pc.addIceCandidate(new RTCIceCandidate(data.candidate));
     }
   }

   createPeerConnection(friend, isOffer) {
     let userId = friend.userId;
     var self =this;
     var retVal = new RTCPeerConnection(this.configuration);
     this.peerConnections = retVal;

     retVal.onicecandidate =  (event) =>  {
       if (event.candidate) {
         this.socket.emit('exchange', {'to': userId, 'candidate': event.candidate, video: friend.video,cameraType: friend.cameraType, callFrom: friend.callFrom});
       }
     };

     function createOffer() {
       retVal.createOffer().then((desc) => {
         retVal.setLocalDescription(desc).then(() => {
           const sdp = retVal.localDescription.sdp.replace(`\r\na=extmap-allow-mixed\r\n`,`\r\n`)
           self.socket.emit('acceptCall', {userId, 'sdp': {type:'offer',sdp}, roomId: friend.roomId, video: friend.video, cameraType: friend.cameraType, callFrom: friend.callFrom}, (result) => {
             if(result.code !== 200) {
               self.onError();
             }
           });
         }).catch(self.onError);
       }).catch(self.onError);
     }

     retVal.onnegotiationneeded =  () => {
       if (isOffer) {
         createOffer.bind(this)();
       }
     }

     retVal.oniceconnectionstatechange = (event) => {
       if (event.target.iceConnectionState === 'connected') {
         this.onStartCall();
         // this.createDataChannel();
       }

       if (event.target.iceConnectionState === 'checking') {
         this.onConnecting();
       }

       if (event.target.iceConnectionState === 'disconnected') {
         this.leave();
       }

       if(event.target.iceConnectionState === 'failed') {
         this.onError();
       }
     };

     retVal.onsignalingstatechange = (event) => {
     };

     retVal.onaddstream =  (event) => {
       this.onFriendConnected(userId, event.stream);
     };

     if(!self.localStream) {
       this
        .getLocalStream(friend.video, friend.cameraType, friend.callFrom)
        .then((stream) => {
          // stream?.getTracks().forEach((track) => retVal.addTrack(track, stream))
          retVal.addStream(stream);
        })
        .catch((err) => {
          this.onError();
        })
     } else {
       // self.localStream?.getTracks().forEach((track) => retVal.addTrack(track, self.localStream))
       retVal.addStream(self.localStream);
     }

     return retVal;
   }

   sendMessage(data) {
     if(this.peerConnections && this.peerConnections.textDataChannel) {
       this.peerConnections.textDataChannel.send(data);
     }
   }

   createDataChannel() {
     if (this.peerConnections.textDataChannel) {
       return;
     }

     const dataChannel = this.peerConnections.createDataChannel("text");

     dataChannel.onmessage = (event) => {
       if(event.data === 'endCall') {
         this.leave();
       }
     }

     this.peerConnections.textDataChannel = dataChannel;
   }

   leave(data) {
     if(!this.hasLeave) {
       this.hasLeave = true;
       this.onFriendLeft(data);
     }
   }

   endCall() {
     // this.sendMessage("endCall");

     if(this.socket && this.roomId) {
       this.socket.emit('endCall', this.roomId);
     }
   }

   disconnectVideo() {
     if(this.socket && this.roomId) {
       this.socket.emit('disconnectVideo', this.roomId);
     }
   }

   reconnectVideo() {
     if(this.socket && this.roomId) {
       this.socket.emit('reconnectVideo', this.roomId);
     }
   }

   destroy() {
     this._isDestroy = true;

     if(this.socket) {
       this.socket.disconnect();
     }

     if(this.peerConnections) {
       this.peerConnections.close();
     }
   }
 }

 export default WebRTCManager;
