import React, { Component } from "react";
import MapView,{Polyline} from "react-native-maps";
var _ = require('lodash')
export default class AnimatedPolyline extends Component {
  constructor(props) {
    super(props);
    this.state = {
      coords: []
    };
    this.count = 1
    this._index = 0;
  }

  componentDidMount() {
    setTimeout(() => {
      this._animate();
    }, 1000);
  }

  _animate = () => {
    if (this._index >= this.props.coordinates.length) {
      if (this.count < 3) {
        this._index = 0;
        this.count++;
        setTimeout(() => {
          this.setState({coords: []}, this._animate);
        }, 1000);
      }
    } else {
      this.setState(
        ({ coords }) => ({ coords: [...coords, this.props.coordinates[this._index]] }),
        () => {
          this._index++;
          setTimeout(this._animate, 1);
        }
      );
    }
  }

  render() {
    return (
      <Polyline
        {...this.props}
        coordinates={this.state.coords}
      />
    );
  }
}