import {
  Platform,
  PermissionsAndroid,
  Linking
} from 'react-native';

const Debug = require('../../Util/Debug')
const Util = require('../../Util/Util')
import {globalVariableManager} from './GlobalVariableManager';
import UserActions_MiddleWare from '../../actions/UserActions_MiddleWare';
import { Actions } from 'react-native-router-flux';

class IncomingLinkingManager {
  constructor() {
    this.init();
  }

  init() {
    Linking.getInitialURL().then((url) => {
      if (url) {
        this.handleURL(url);
      }
    })

    this.listenerLinking = Linking.addEventListener('url', (event) => {
      this.handleURL(event.url);
    });
  }

  handleURL(url) {
    const appName = url.replace("iochongbangvietnam://", "").split("/")[0];
    if(appName === 'vimowallet') {
      this.handleViMoLinking(url)
    } else if(appName === 'tickbox') {
      if(globalVariableManager.reduxManager.state.User.memberInfo && globalVariableManager.reduxManager.state.User.memberInfo?.member?.memberToken){
        this.handleTickBox(url)
      }
    }

    if (url.includes(`&linkingReferenceId=`) && url.includes(`&result=100`) && url.includes(`?authCode=`)) {
      globalVariableManager.reduxManager.dispatch(UserActions_MiddleWare.getAccessToken({authCode: url.replace('iochongbangvietnam:?', '').split('&')[0].replace('authCode=', '')}))
    }
  }

  handleTickBox(url) {
    const path = url.replace("aihaiphongvietnam://tickbox/", "").split("/");
    if(path.length === 2) {
      setTimeout(() => {
        Actions.ListStoreScreen({
          storeId: path[1],
          serviceId: path[0],
          link: 1
        })
      }, 1000);
    } else {
      globalVariableManager.rootView.showToast("Không tìm thấy cửa hàng");
    }
  }

  handleViMoLinking(url) {
    const path = url.replace("aihaiphongvietnam://vimowallet/", "").split("/");
    if(path.length === 2) {
      setTimeout(() => {
        globalVariableManager.reduxManager.dispatch(UserActions_MiddleWare.getInfoCheckout({
         token_code: path[0],
         transaction_code: path[1]
        }));
      }, 1000);
    } else {
      globalVariableManager.rootView.showToast("Giao dịch không thành công");
    }
  }
}

const incomingLinkingManager = new IncomingLinkingManager()

module.exports= incomingLinkingManager;
