import React from 'react';

import {
  AppState,
  Platform
} from 'react-native';
import _ from 'lodash';

import RNCallKit from 'react-native-callkeep';
// import InCallManager from 'react-native-incall-manager';
import VoipPushNotification from 'react-native-voip-push-notification';
import DeviceInfo from 'react-native-device-info';
import {popupActions} from '../popups/PopupManager';
// import WebRTC from '../popups/WebRTC'
const UserActions_MiddleWare = require('../../actions/UserActions_MiddleWare');
var {Actions} = require('react-native-router-flux');
var {globalVariableManager}= require('../modules/GlobalVariableManager');
var Define = require('../../Define');

class CallKitManager {
  constructor(props) {
    this.setUpSuccess = false;
    this.isCalling = false;
    this.currentCallInf = null;
    this.token = null;
    this.onRNCallKitDidReceiveStartCallAction = this.onRNCallKitDidReceiveStartCallAction.bind(this);
    this.onRNCallKitPerformAnswerCallAction = this.onRNCallKitPerformAnswerCallAction.bind(this);
    this.onRNCallKitPerformEndCallAction = this.onRNCallKitPerformEndCallAction.bind(this);
    this.onRNCallKitDidActivateAudioSession = this.onRNCallKitDidActivateAudioSession.bind(this);

    this.init();
  }

  init() {
    if(Platform.OS === 'ios' && (parseInt(DeviceInfo.getSystemVersion().split(".")[0]) >= 10)) {
      this.initCallKit();
      this.setUpVoipPush();
    }
  }

  initCallKit() {
    try {
      RNCallKit.setup({
          ios: {
            appName: 'HeyU'
          }
      });

      this.setUpSuccess = true;
      this.registerEventCallKit();
    } catch (err) {

    }
  }

  getToken() {
    return this.token;
  }

  setUpVoipPush() {
    VoipPushNotification.addEventListener('register', (voipToken) => {
      Define.config.voipToken = voipToken;
      this.token = voipToken;
      setTimeout(() => {
        const memberToken = _.get(globalVariableManager, 'reduxManager.state.User.memberInfo.member.memberToken', '');
        if(memberToken) {
          globalVariableManager.reduxManager.dispatch(UserActions_MiddleWare.sentVoipNotifyToken({memberToken: memberToken}));
        }
      }, 3000);
    });

    VoipPushNotification.addEventListener('notification', (notify) => {
      this.lastNotifyData = notify;
      // if(notify._data.link === 'WebRTC' && InCallManager.recordPermission === 'granted') {
      //   this.onIncomingCall(notify);
      // } else {
      //   this.displayLocalNotification(notify);
      // }

      if (VoipPushNotification.wakeupByPush) {
        VoipPushNotification.wakeupByPush = false;
      }
    });
  }

  onRNCallKitDidReceiveStartCallAction(data) {
    Actions.FreeCallSearchScreen({
      phone: data.handle
    });
  }

  onRNCallKitPerformAnswerCallAction(data) {
    // if(InCallManager.recordPermission === 'granted' && this.currentCallInf) {
    //   clearTimeout(this.timeoutCall);
    //   this.currentCallInf.hasAnswer = true;

    //   popupActions.setRenderContentAndShow(WebRTC, {
    //     userId: this.currentCallInf.callerId,
    //     roomId: this.currentCallInf.roomId,
    //     callUUID: data.callUUID,
    //     wakeupByPush: this.currentCallInf.wakeupByPush,
    //     video: this.currentCallInf.video,
    //     cameraType: this.currentCallInf.cameraType,
    //     callFrom: this.currentCallInf.callFrom
    //   })
    // } else {
    //   const roomID = _.get(this.lastNotifyData, '_data.extras.roomId', '');
    //   if(roomID) {
    //     RNCallKit.endCall(roomID);
    //   }
    // }
  }

  onRNCallKitPerformEndCallAction(data) {
    clearTimeout(this.timeoutCall);

    if(this.currentCallInf && !this.currentCallInf.hasAnswer) {
      popupActions.setRenderContentAndShow(WebRTC, {
        userId: this.currentCallInf.callerId,
        roomId: this.currentCallInf.roomId,
        forEnd: 1
      })
    }

    this.isCalling = false;
    this.currentCallInf = null;
  }

  onRNCallKitDidActivateAudioSession(data) {

  }

  onIncomingCall(notify) {
    if(this.checkCondition()) {
      const roomId = notify._data.extras.roomId;
      const callerId = notify._data.extras.userId;
      const phone = notify._data.phone;
      const video = notify._data.extras.video;

      this.isCalling = true;
      this.currentCallInf = {
        roomId,
        callerId,
        wakeupByPush: VoipPushNotification.wakeupByPush,
        hasAnswer: false,
        video
      }

      // RNCallKit.displayIncomingCall(roomId, phone);
      this.checkCallTimeout(notify);
    } else {
      this.displayLocalNotification(notify);
    }
  }

  checkCallTimeout(notify) {
    this.timeoutCall = setTimeout(() => {
      if(this.currentCallInf && !this.currentCallInf.hasAnswer) {
        VoipPushNotification.presentLocalNotification({
          alertBody: `Bạn đã bỏ lỡ cuộc gọi từ ${notify._data.phone}`,
          userInfo: {
            link: 'FreeCallSearchScreen',
            icon: notify._data.icon,
            extras: {
              phone: notify._data.phone
            }
          }
        });

        RNCallKit.endCall(this.currentCallInf.roomId);
      }
    }, 30000);
  }

  checkCondition() {
    let canCall = true;

    if(this.isCalling
   || !this.setUpSuccess
   || (parseInt(DeviceInfo.getSystemVersion().split(".")[0]) < 10)) {
      canCall =  false;
    }

    return canCall;
  }

  displayLocalNotification(notify) {
    VoipPushNotification.presentLocalNotification({
      alertBody: notify._alert.body,
      alertTitle: notify._alert.title,
      soundName: 'newcall.aiff',
      userInfo: notify._data
    });
  }

  registerEventCallKit() {
    RNCallKit.addEventListener('didReceiveStartCallAction', this.onRNCallKitDidReceiveStartCallAction);
    RNCallKit.addEventListener('answerCall', this.onRNCallKitPerformAnswerCallAction);
    RNCallKit.addEventListener('endCall', this.onRNCallKitPerformEndCallAction);
    RNCallKit.addEventListener('didActivateAudioSession', this.onRNCallKitDidActivateAudioSession);
  }
}

module.exports = new CallKitManager;
