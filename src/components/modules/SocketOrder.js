import _ from 'lodash';
import moment from 'moment';
import socketIOClient from 'socket.io-client';
import {globalVariableManager} from './GlobalVariableManager';
import RDActions from '../../actions/RDActions';
import FeedsSystemActions_MiddleWare from '../../actions/FeedsSystemActions_MiddleWare';
import UserActions_MiddleWare from '../../actions/UserActions_MiddleWare';
import AppStateActions_MiddleWare from '../../actions/AppStateActions_MiddleWare';
import BikeActions_MiddleWare from '../../actions/BikeActions_MiddleWare';
import HubSystemActions_MiddleWare from '../../actions/HubSystemActions_MiddleWare';
import SBHStoreActions_MiddleWare from '../../actions/SBHStoreActions_MiddleWare';
import HireDriverActions_MiddleWare from '../../actions/HireDriverActions_MiddleWare';
import HeyCareActions_MiddleWare from '../../actions/HeyCareActions_MiddleWare';
import CleaningActions_MiddleWare from '../../actions/CleaningActions_MiddleWare';
import IHeyUActions_MiddleWare from '../../actions/IHeyUActions_MiddleWare';
import IOCHBActions_MiddleWare from '../../actions/IOCHBActions_MiddleWare';

const Define = require('../../Define');
import NotifyPopup from '../popups/NotifyPopup'
// import WebRTC from '../popups/WebRTC'
var {popupActions} = require('../popups/PopupManager');
var NotifyUtil = require('../../Util/notify');
import TrackPlayer from 'react-native-track-player';

import {
  Platform
} from 'react-native';
import { navigationRef } from '../../../RootNavigation';

class SocketManager {
  constructor() {
    this.hasConnect = false;
    this.socket = null;
    this.customEvent = {}
  }

  init() {
    this.socketURL = `${Define.servers[Define.constants.serverSocketAddr] || Define.constants.serverSocketAddr}/client`;
    this.initTrackPlayer()
    this.socket = socketIOClient(this.socketURL, {
      transports: ['websocket'],
      jsonp: false,
      reconnection: true,
      query: {
        memberToken: _.get(globalVariableManager, 'reduxManager.state.User.memberInfo.memberToken', ''),
        nativeVersion: Define.constants.nativeVersion,
        versionCodePush: Define.constants.versionCodePush,
        appName: Define.constants.appName
      }
    });

    this.socket.on('reconnect_attempt', () => {
      this.socket.io.opts.query = {
        memberToken: _.get(globalVariableManager, 'reduxManager.state.User.memberInfo.memberToken', ''),
        nativeVersion: Define.constants.nativeVersion,
        versionCodePush: Define.constants.versionCodePush,
        appName: Define.constants.appName
      }
    })

    this.socket.on('connect', (data) => {
      this.hasConnect = true;
      this.login();
    });

    this.socket.on('connect_error', (err) => {
      // this.socket = null;
      this.hasConnect = false;
    });
    this.socket.on('profile_update', (data, cb) => {
      this.showNotify(data, 1);
      globalVariableManager.reduxManager.dispatch(UserActions_MiddleWare.get());
    })
  }
  initCustomEvent() {
    Object.keys(this.customEvent).forEach((eventName) => {
      const defaultFunc = (...arg) => {
        this.customEvent[eventName].fnc = this.customEvent[eventName].fnc || [];
        this.customEvent[eventName].fnc.forEach((funct) => {
          funct(...arg);
        })
      }

      this.customEvent[eventName].defaultFunc = defaultFunc;


      this.socket.on(eventName, defaultFunc);
    })
  }
  addEvent(eventName, func) {
    if(this.customEvent[eventName]) {
      this.customEvent[eventName].fnc.push(func)
    } else {
      const defaultFunc = (...arg) => {
        this.customEvent[eventName].fnc = this.customEvent[eventName].fnc || [];
        this.customEvent[eventName].fnc.forEach((funct) => {
          funct(...arg);
        })
      }

      this.customEvent[eventName] = {
        "defaultFunc": defaultFunc,
        "fnc": [func]
      }

      this.socket.on(eventName, defaultFunc);
    }
  }
  async initTrackPlayer() {
    await TrackPlayer.setupPlayer();
  }
  async playMessageSound() {
    try {
      await TrackPlayer.reset();
      await TrackPlayer.add({
        id: 'message',
        url: require('../../../assets/message.mp3'),
        title: 'Message',
        artist: 'App',
      });
      await TrackPlayer.play();
    } catch (e) {}
  }

  async playHeyUSound() {
    try {
      await TrackPlayer.reset();
      await TrackPlayer.add({
        id: 'sound_heyu',
        url: require('../../../assets/sound_heyu.wav'),
        title: 'HeyU',
        artist: 'App',
      });
      await TrackPlayer.play();
    } catch (e) {}
  }

  stopSoundHeyU() {
    TrackPlayer.stop();
  }

  showNotify(data, playSound) {
    const notiObj = {
      _id: Date.now(),
      notifiedAt: Date.now(),
      title: _.get(data, 'title', '').trim(),
      description: _.get(data, 'description', '').trim(),
      icon: _.get(data, 'icon', ''),
      link: _.get(data, 'data.link', ''),
      extras: _.get(data, 'data.extras', {}),
      fromSocket: true
    }

    if(notiObj.title || notiObj.description) {
      // popupActions.setRenderContentAndShow(NotifyPopup, {notiObj})
      NotifyUtil.pushNotify(notiObj)

      if (playSound) {
        this.playMessageSound();
      }

      globalVariableManager.reduxManager.dispatch(RDActions['Notify']['addOnRequest'](notiObj));
    }
  }

  reCheck() {
    if(!this.socket || !this.socket.connected) {
      this.destroy();
      const memberToken = _.get(globalVariableManager, 'reduxManager.state.User.memberInfo.memberToken', '');
      if(memberToken) {
        this.init();
      }
    }
  }

  destroy() {
    if(this.socket) {
      this.socket.disconnect();
    }
  }

  emitEvent(eventName, data) {
    if(this.socket && this.socket.connected) {
      this.socket.emit(eventName, data);
    }
  }

  login() {
    const memberToken = _.get(globalVariableManager, 'reduxManager.state.User.memberInfo.memberToken', '');
    const appName = Define.constants.appName
    if(memberToken) {
      this.socket.emit('login', {memberToken, appName});
    }
  }
}

module.exports = new SocketManager;