import notifee, { AndroidImportance,AndroidStyle,EventType } from '@notifee/react-native';

const listSound = ['default', 'message', 'new_move_order', 'new_order', 'sound_heyu', 'sound_popup']
class AndroidNotifyChannelManager{
  constructor(){
    this.init = this.init.bind(this);
  }
  init(){
    listSound.forEach((sound, i) => {
      const  channelId = `iochongbang_notification_${sound}`
      notifee.createChannel({
        id: channelId,
        name: `Channel for sound: ${sound}`,
        sound: sound,
        importance: AndroidImportance.HIGH,
        lightUpScreen: true
      })
    });
  }
}

const androidNotifyChannelManager = new AndroidNotifyChannelManager();

module.exports={androidNotifyChannelManager};
