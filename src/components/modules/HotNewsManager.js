
var _ = require('lodash')
//LIB

// import { connect } from 'react-redux';

//action

//components
var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Util = require('../../Util/Util');
// var Themes = require('../../Themes');
// var Include = require('../../Include');
var ButtonWrap = require('../elements/ButtonWrap');

var {popupActions} = require('../popups/PopupManager');
var {globalVariableManager} = require('../modules/GlobalVariableManager');

//Actions
var NotificationsActions_MiddleWare = require('../../actions/NotificationsActions_MiddleWare');
var RDActions = require('../../actions/RDActions');

import HotNewsPopup from '../popups/HotNewsPopup';

class HotNewsManager {
  // static defaultProps = {}
  // static propTypes = {}
  constructor() {
    this.canShow = true;
  }

  setShowHotNews = (value) => {
    this.canShow = value;
  }

  showHotNews = (type, serviceId) => {
    const user = globalVariableManager.reduxManager.state.User;
    const data = user.hotnews && serviceId && user.hotnews[serviceId] && user.hotnews[serviceId].shift();

    if(data) {
      popupActions.setRenderContentAndShow(HotNewsPopup, {
        data,
        setShowHotNews: this.setShowHotNews
      });
    } else {
      this.canShow = true;
    }
  }

  getHotNews = (query) => {
    globalVariableManager.reduxManager.dispatch(NotificationsActions_MiddleWare.getHotNew(query))
    .then(() => {
      if (this.canShow) {
        this.canShow = false;
        this.showHotNews(query.type, query.serviceId || 'general');
      }
    })
  }
}

const hotNewsManager = new HotNewsManager()
module.exports = hotNewsManager;
