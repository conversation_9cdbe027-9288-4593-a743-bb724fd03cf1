import React, { PureComponent } from 'react';
import PropTypes from 'deprecated-react-native-prop-types';
import { PixelRatio } from 'react-native';
import createIconSet from '@react-native-vector-icons/icomoon';
import penguinIconConfig from './config.json';
const Icon = createIconSet(penguinIconConfig, 'PeguginBoldicon', 'PenguinBoldIcon.ttf');
export default class extends PureComponent {
  static propTypes = {
  };
  static defaultProps = {
    size: PixelRatio.getFontScale() * 35
  };
  renderIcon() {
    return <Icon {...this.props} />;
  }
  render() {
    return this.renderIcon();
  }
}