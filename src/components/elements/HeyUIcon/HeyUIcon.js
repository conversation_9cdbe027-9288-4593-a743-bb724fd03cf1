import React, { PureComponent } from 'react';
import { PixelRatio } from 'react-native';
import createIconSet from '@react-native-vector-icons/fontello';
import fontelloConfig from './config.json';
const Icon = createIconSet(fontelloConfig);
export default class extends PureComponent {
  static defaultProps = {
    size: PixelRatio.getFontScale() * 35
  };
  renderIcon() {
    return <Icon{...this.props} />;
  }
  render() {
    return this.renderIcon();
  }
}