import React, { Component } from 'react';
// import { <PERSON><PERSON><PERSON><PERSON>, FBLoginManager } from 'react-native-facebook-login';
import axios from 'axios';
import DeviceInfo from 'react-native-device-info';
import { connect } from 'react-redux';

class facebook extends Component {

    handleMemberInfo(token) {

        axios.get('https://graph.facebook.com/v2.8/me?fields=email,name,locale,timezone&access_token=' + token)
            .then((response) => {
                if(response.status === 200) {
                    let memberResonse = response.data;
                    let member = {
                        "profile": {
                            "id": memberResonse.id,
                            "displayName": memberResonse.name,
                            "_json": {
                                "email": memberResonse.email,
                                "locale": memberResonse.locale,
                                "timezone": memberResonse.timezone
                            }
                        },
                        "access_token": token,
                        "deviceInfo": {
                            "device_id": Define.constants.deviceId,
                            "os": DeviceInfo.getSystemName(),
                            "app_version": DeviceInfo.getVersion(),
                            "type": DeviceInfo.getBrand() === 'Apple' ? 'ios' : 'android'
                        }
                    }

                    this.props.memberLogin(member);
                    this.props.replaceRoute('feeds');
                }
            })

    }

    handleMemberLogout() {
        this.props.memberLogout();
        this.props.popRoute();
    }

    render() {

        let _this = this;

        return( null
            // <FBLogin style={{ marginBottom: 10 }}
            //      ref={(fbLogin) => { this.fbLogin = fbLogin }}
            //      permissions={["email","user_friends"]} readPermissions={['public_profile']}
            //      loginBehavior={FBLoginManager.LoginBehaviors.Native}
            //      onLogin={function(data){
            //          _this.setState({ user : data.credentials });
            //          _this.handleMemberInfo(data.credentials.token);
            //      }}
            //      onLogout={function(){
            //          _this.handleMemberLogout();
            //      }}
            //      onLoginFound={function(data){
            //          _this.setState({ user : data.credentials });
            //      }}
            //      onLoginNotFound={function(){
            //          _this.setState({ user : null });
            //      }}
            //      onError={function(data){
            //          console.log("ERROR");
            //          console.log(data);
            //      }}
            //      onCancel={function(){
            //          console.log("User cancelled.");
            //      }}
            //      onPermissionsMissing={function(data){
            //          console.log("Check permissions!");
            //          console.log(data);
            //      }}
            // />
        )

    }

}

function bindAction(dispatch) {

    return {
        memberLogin: (data) => dispatch(memberLogin(data)),
        memberLogout: () => dispatch(memberLogout()),
        popRoute: () => dispatch(popRoute()),
        replaceRoute: (route) => dispatch(replaceRoute(route))
    }

}

const mapStateToProps = (state) => {

    return {
        member: state
    }

}

export default connect(mapStateToProps, bindAction)(facebook)
