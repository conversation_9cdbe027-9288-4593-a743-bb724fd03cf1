var _ = require('lodash')
//LIB
import React  from 'react';
import {
  View,
  TouchableOpacity,
  InteractionManager,
  LayoutAnimation,
  Linking,
  Platform,
  Text,
  Image,
  FlatList
} from 'react-native';

import moment from 'moment';

//components
var Define = require('../../../Define');
var Debug = require('../../../Util/Debug');
var Themes = require('../../../Themes');
var Util = require('../../../Util/Util');
var Include = require('../../../Include');

import ReactComponent from '../../ReactComponent'

import styles from './styles';
var ButtonWrap = require('../ButtonWrap');

var {popupActions} = require('../../popups/PopupManager');
var {globalVariableManager}= require('../../modules/GlobalVariableManager');

import DefaultPopup from '../../popups/DefaultPopup';
import LinearGradient from 'react-native-linear-gradient';

class ServiceList extends ReactComponent{
  static componentName = 'ServiceList'
  static defaultProps = {
    onHead:()=>{},
    onScroll:()=>{},
    onContentSizeChange:()=>{}
  }
  // static propTypes = {}
  constructor(props){
    super(props);
    this.state = _.merge(this.state,
    {})
    this.renderItem = this.renderItem.bind(this);
  }

  renderAdditionAction(rowData, sectionID, rowID){
    return null;
  }

  renderItem({item}){
    let image;
    if(item.name === 'Gói ngày') {
      image = Define.assets.Images.day
    } else if(item.name === 'Gói tuần') {
      image = Define.assets.Images.week
    } else {
      image = Define.assets.Images.month
    }
    return (
      <ButtonWrap
        onPress={item.onPress}>
        <View style={{flexDirection:'row'}}>
          <View style={{margin:10, padding: 10,  alignItems:'center', justifyContent:'center', borderRadius:25, borderColor:'#1697B4', borderWidth:1}}>
            <Image
              resizeMode={'stretch'}
              style={{width: 30, height: 30}}
              source={image}
            />
          </View>
          <View style={{flex:1, flexDirection:'row',  borderBottomWidth:1, borderBottomColor:'#DADCE0', paddingRight:5}}>
            <View style={{flex:4, flexDirection: 'column', justifyContent:'center'}}>
              <Include.Text style={{fontSize:16, color:'#000'}}>{item.name} {item.price.toString().replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1,")} {this.props.currencyUnit}.</Include.Text>
              <Include.Text style={{fontSize:13, color:'#828282'}}>Bạn sử dụng được trong {item.dateUsed} ngày</Include.Text>
              {item.bonus ?
                <Include.Text style={{fontSize:13, color:'#828282'}}>Khuyến mại: {item.bonus}%</Include.Text>
              :null}
            </View>
            <LinearGradient
              colors= {['#FFA957', '#FE5D76']}
              style= {{width:60, height:35, flex:1, justifyContent:'center',borderRadius:4, alignSelf:'center', alignItems:'center'}}
            >
              <View>
                <Include.Text style={{backgroundColor:'transparent', fontSize:14, color:'#fff'}}>MUA</Include.Text>
              </View>
            </LinearGradient>
          </View>
        </View>
      </ButtonWrap>
    );
  }

  rowHasChanged(r1, r2) {
    const value = true;
    return value;
  }

  UNSAFE_componentWillUpdate(){
    super.UNSAFE_componentWillUpdate();
    // LayoutAnimation.easeInEaseOut();
  }
  renderContent(){
    var content = null;
    content=(
      <FlatList
        data={this.props.infos}
        containerStyle={{
          justifyContent: 'space-around',
          flexDirection: 'column',
        }}
        removeClippedSubviews={false}
        ref={(listView)=>{this.listView=listView}}
        refreshControl={this.props.refreshControl}
        initialListSize = {6}
        onScroll={(event) => {
        }}
        scrollEventThrottle={200}
        style={[this.props.style]}
        rowHasChanged={this.rowHasChanged}
        renderItem={this.renderItem}
        />
    )
    return(content)
  }
}

export default ServiceList