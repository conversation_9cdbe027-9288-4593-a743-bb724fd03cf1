'use strict';

var React = require('react-native');

var { StyleSheet, Dimensions, Platform } = React;

var deviceHeight = Dimensions.get('window').height;
var deviceWidth = Dimensions.get('window').width;

module.exports = {
    container: {
        flex: 1,
        width: null,
        height: null
    },
    newsContent: {
        flexDirection: 'column',
        paddingTop: 5,
        paddingLeft: 10,
        paddingRight: 10,
        flex: 1,
        borderBottomWidth: 1,
        borderBottomColor: '#ddd'
    },
    newsHeader: {
        color: '#444',
        paddingTop: 5,
        textAlign: 'justify'
    },
    name: {
        color: '#01cca1',
        fontSize: 13,
        marginTop: 2,
    },
    time: {
        marginBottom: 3,
        marginRight: 30,
        fontSize: 11,
        color: '#888888'
    },
    newsLink: {
        color: '#666',
        fontSize: 12,
        fontWeight: 'bold'
    },
    linkToFacebook: {
      color: '#3498db',
      fontSize: 12,
      fontWeight: 'bold'
    },
    newsTypeView: {
        borderBottomWidth: 1,
        borderBottomColor: '#666',
        alignSelf: 'flex-end'
    },
    newsTypeText: {
        color: '#666',
        fontSize: 12,
        fontWeight: 'bold',
        paddingBottom: 5
    },
    elementFooter: {
      alignItems: 'center',
      flexDirection: 'row',
    },
    newsPoster: {
        height: null,
        width: null,
        resizeMode: 'cover',
        flex: 1,
        position: 'relative'
    },
    newsPosterHeader: {
        fontWeight: '900'
    },
    newsPosterLink: {
        opacity: 0.8,
        fontSize: 12,
        alignSelf: 'flex-start',
        fontWeight: 'bold'
    },
    newsPosterTypeView: {
        borderBottomWidth: 1,
        borderBottomColor: '#666',
        alignSelf: 'flex-end'
    },
    newsPosterTypeText: {
        fontSize: 12,
        fontWeight: 'bold',
    },
    timeIcon: {
        fontSize: 14,
        marginLeft: Platform.OS === 'android' ? 15 : 0,
        paddingLeft: Platform.OS === 'android' ? 0 : 20,
        marginTop: Platform.OS === 'android' ? -2 : 5,
        color: '#666'
    },
    commentsIcon: {
        fontSize: 20,
        marginLeft: Platform.OS === 'android' ? 15 : 0,
        paddingLeft: Platform.OS === 'android' ? 0 : 0,
        paddingRight: 5,
        marginTop: Platform.OS === 'android' ? -2 : 3,
        color: '#666'
    },
    othersIcon: {
        fontSize: 20,
        marginLeft: Platform.OS === 'android' ? 15 : 0,
        paddingLeft: Platform.OS === 'android' ? 0 : 0,
        paddingRight: 5,
        marginTop: Platform.OS === 'android' ? -2 : 3,
        color: '#666'
    },
    iconLinkFacebook: {
      fontSize: 20,
      marginLeft: Platform.OS === 'android' ? 15 : 0,
      paddingLeft: Platform.OS === 'android' ? 0 : 0,
      paddingRight: 5,
      marginTop: Platform.OS === 'android' ? -2 : 3,
      color: '#3498db'
    },
    headertimeIcon: {
        fontSize: 20,
        marginLeft: Platform.OS === 'android' ? 15 : 0,
        paddingLeft: Platform.OS === 'android' ? 0 : 20,
        paddingRight: 10,
        marginTop: Platform.OS === 'android' ? -2 : 5,
        color: '#fff'
    },
    slide: {
        flex: 1,
        width: null,
        backgroundColor: 'transparent'
    },
    swiperTextContent: {
        // marginBottom: 170,
        marginTop: deviceHeight/4 + 20,
        width: deviceWidth,
        padding: 20
    },
    swiperDot: {
        backgroundColor:'rgba(0,0,0,.8)',
        width: 8,
        height: 8,
        borderRadius: 4,
        marginLeft: 3,
        marginRight: 3,
        marginTop: 3,
        marginBottom: 0
    },
    swiperActiveDot: {
        backgroundColor: '#fff',
        width: 8,
        height: 8,
        borderRadius: 4,
        marginLeft: 3,
        marginRight: 3,
        marginTop: 3,
        marginBottom: 0
    },
    swiperContentBox: {
        flexDirection: 'row',
        justifyContent: 'space-between'
    },
    swiperContentBoxFooter: {
        paddingTop: 5,
        paddingBottom: 5,
        justifyContent: 'space-between'
    },
    swiperContentBoxButtons: {
        paddingBottom: 20
    },

    pickup: {
        marginLeft: 15
    },

    Button: {
        alignItems: 'flex-end',
        justifyContent: 'flex-end'
    }
};
