import React, { Component } from 'react';
import { TouchableOpacity, View, Text } from 'react-native';
import {Ionicons as Icon} from '@react-native-vector-icons/ionicons';
import styles from './styles';

export default class Save extends Component {

    onClick() {
        // console.log(456)
    }

    render() {

        return (

            <View style={styles.button}>
                <Icon name='swap' style={styles.othersIcon} />
                <Text allowFontScaling={false} style={styles.newsLink}><PERSON><PERSON><PERSON> lại</Text>
            </View>


        )

    }

}
