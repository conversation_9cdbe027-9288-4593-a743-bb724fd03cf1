import React, { Component } from 'react';
import { TouchableOpacity, Text } from 'react-native';
import {Ionicons as Icon} from '@react-native-vector-icons/ionicons';

export default class Save extends Component {

    render() {

        return (

            <TouchableOpacity style={{marginRight: 25}}>
                <View style={{flexDirection:'row'}}>
                    <Icon name='swap' style={styles.othersIcon} />
                    <Text allowFontScaling={false} style={styles.newsLink}><PERSON><PERSON><PERSON> lạ<PERSON></Text>
                </View>
            </TouchableOpacity>

        )

    }

}
