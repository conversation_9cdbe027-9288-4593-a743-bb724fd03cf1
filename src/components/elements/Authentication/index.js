// 'use strict';

import React, { Component } from 'react';
import { Image, View, Switch, TouchableOpacity, Platform ,TextInput, ActivityIndicator} from 'react-native';
import { connect } from 'react-redux';
import moment from 'moment';
import ImagePicker from 'react-native-image-picker';
import PropTypes from 'deprecated-react-native-prop-types'

React.PropTypes = PropTypes;


var Define = require('../../../Define');
var Debug = require('../../../Util/Debug');
var Themes = require('../../../Themes');
var Util = require('../../../Util/Util');
var NotifyUtil = require('../../../Util/notify');
var Include = require('../../../Include');
import HeyUIcon from '../../elements/HeyUIcon/HeyUIcon';
import RDActions from '../../../actions/RDActions'

var {popupActions} = require('../../popups/PopupManager');
import DefaultPopup from '../../popups/DefaultPopup'
var {globalVariableManager}= require('../../modules/GlobalVariableManager');
var uploadFileManager = require('../../modules/UploadFileManager');
import isEmail from 'validator/lib/isEmail';

import FadeDownDefaultPopup from '../../popups/FadeDownDefaultPopup'

import ButtonWrap from '../ButtonWrap'

import UserActions_MiddleWare from '../../../actions/UserActions_MiddleWare'
import styles from './styles';
var primary = Themes.current.factor.brandPrimary;
let _ = require('lodash');

class Authentication extends Component {

    constructor(props) {
        super(props);
        let {user,authenInf} = this.props;

        // this.setState({
        //   id: nextProps.authenInf._id,
        //   avatarImage: nextProps.authenInf.avatar,
        //   frontCerImage: nextProps.authenInf.frontCer,
        //   backCerImage: nextProps.authenInf.backCer,
        //   status: nextProps.authenInf.status
        // })

        this.state = {
         id: (authenInf&&authenInf._id)?authenInf._id:'',
         avatarImage:(authenInf&&authenInf.avatar)?authenInf.avatar:'',
         frontCerImage:(authenInf&&authenInf.frontCer)?authenInf.frontCer:'',
         backCerImage:(authenInf&&authenInf.backCer)?authenInf.backCer:'',
         status: null,
         disable: 0
      };

      this.currentInf = {
        id: (authenInf&&authenInf._id)?authenInf._id:'',
        avatarImage:(authenInf&&authenInf.avatar)?authenInf.avatar:'',
        frontCerImage:(authenInf&&authenInf.frontCer)?authenInf.frontCer:'',
        backCerImage:(authenInf&&authenInf.backCer)?authenInf.backCer:'',
      }

      this.handleAuthentication = this.handleAuthentication.bind(this);
      this.idImg = []
      this.uploadSuccess = []
      // this.constructor.childContextTypes = {
      //   theme: React.PropTypes.object,
      // }

      this.handlePickImage = this.handlePickImage.bind(this);
    }

   resetRoute(route) {
        this.props.resetRoute(route);
    }

    handleAuthentication() {
      let {dispatch, user} = this.props;
      var self = this;
      let message = '';

      if (this.state.frontCerImage === '') {
        message += 'Thiếu ảnh CCCD/CMTND mặt trước \n'
      }
      if (this.state.backCerImage === '') {
        message += 'Thiếu ảnh CCCD/CMTND mặt sau \n'
      }
      if (this.state.avatarImage === '') {
        message += 'Thiếu ảnh chân dung'
      }

      if (message) {
        NotifyUtil.pushAlertTopNotify({
          type: 'warning',
          content: message,
          timeClose: 3000,
        })
      } else {
        const objUpload = {
          phone: _.get(user, 'memberInfo.member.phone', '')
        };
        if(this.currentInf.avatarImage !== this.state.avatarImage) {
          objUpload.avatar = this.state.avatarImage;

        }
        if(this.currentInf.frontCerImage !== this.state.frontCerImage) {
          objUpload.frontCer = this.state.frontCerImage;
        }

        if(this.currentInf.backCerImage !== this.state.backCerImage) {
          objUpload.backCer = this.state.backCerImage;
        }

        if(Object.keys(objUpload).length === 0) {
          return   popupActions.setRenderContentAndShow(DefaultPopup,
            {
              title: 'Thông báo',
              description: 'Bạn chưa thay đổi thông tin gì',
              buttonTitle:'OK',
              onPress:() => {popupActions.popPopup()}
            })
        }

        if(this.uploadSuccess.includes(false)) {
          return   popupActions.setRenderContentAndShow(DefaultPopup,
            {
              title: 'Thông báo',
              description: "Bạn vui lòng đợi ảnh tải lên thành công",
              buttonTitle: 'Đồng ý',
              onPress: () => { popupActions.popPopup() }
            })
        }

        objUpload.id = this.state.id;

        if(this.props.authenInf && this.props.authenInf.status === 1) {
          return   popupActions.setRenderContentAndShow(DefaultPopup,
            {
              title: 'Thông báo',
              description: 'Hiện tại bạn đã là thành viên xác thực của hệ thống nếu bạn chỉnh sửa thông tin thì sẽ phải chờ duyệt xét lại',
              buttonTitle: 'OK',
              buttonTitle2: 'Huỷ',
              onPress: () => {
                popupActions.popPopup();
                updateOrCreate.bind(this)();
              },
              onPress2: () => {
                popupActions.popPopup();
              }
            })
        }

        function updateOrCreate() {
          let method = dispatch(UserActions_MiddleWare.createAuthen(objUpload))
          if(this.state.id){
            method = dispatch(UserActions_MiddleWare.modifyAuthen(objUpload))
          }
          method
          .then((result)=>{
            if(!this.state.id) {
              this.setState({
                status: 0,
                id: result.res._id,
                updatedAt: result.res.updatedAt,
                disable: 1
              })
            } else {
              this.setState({
                status: 0,
                disable: 1
              })
            }

            this.currentInf = this.state;
            NotifyUtil.pushAlertTopNotify({
              content: 'Cập nhật thông tin thành công',
              type: 'success',
              timeClose: 3000,
            })
          })
          .catch(err => {
            NotifyUtil.pushAlertTopNotify({
              content: 'Cập nhật thông tin thất bại. Vui lòng thử lại',
              type: 'waring',
              timeClose: 3000,
            })
          })
        }

        updateOrCreate.bind(this)();
      }
    }
    handlePickImage(stateVariable){
      var options = {
        title: '',
        customButtons: [],
        cameraType:'back',
        quality:0.5,
        rotation:(stateVariable === 'avatarImage')? 90:0,
        storageOptions: {
          skipBackup: true,
          path: 'images'
        }
      };

      /**
       * The first arg is the options object for customization (it can also be null or omitted for default options),
       * The second arg is the callback which sends object: response (more info below in README)
       */
      ImagePicker.launchCamera(options, (response) => {
        Debug.log('Response = ', response);

        if (response.didCancel) {
          Debug.log('User cancelled image picker');
        }
        else if (response.error) {
          Debug.log2('ImagePicker Error: ', response.error);
        }
        else if (response.customButton) {
          Debug.log2('User tapped custom button: ', response.customButton);
        }
        else {
          // let source = { uri: response.uri };
          // You can also display the image using data:
          // let source = { uri: 'data:image/jpeg;base64,' + response.data };
          let index = 0
          if(stateVariable === 'avatarImage'){
            index = 2
          } else if(stateVariable === 'backCerImage'){
            index = 1
          }
          this.uploadSuccess[index] = false;
          this.setState({
            [stateVariable]: response.uri
          });
          this.uploadFile(index, response, stateVariable);
        }
      });
    }

    uploadFile(index, image, type) {
      let images = [this.state.frontCerImage, this.state.backCerImage, this.state.avatarImage ]
      this.idImg[index] = `SS_${Date.now()}`;
      uploadFileManager.addFile(this.idImg[index], 'local', image.uri);

      const dataUpload = {
        fileUpload: image.uri,
        folder: 'authenticate-shop',
        fileName: image.fileName || 'image.jpg',
        id: this.idImg[index]
      }

      this.uploadSuccess[index] = false;

      uploadFileManager.upload(dataUpload)
        .then((result) => {
          index = this.idImg.indexOf(result.arg.id);
          if (index === -1) {
            uploadFileManager.declineFile(result.arg.id);
          } else {
            this.uploadSuccess[index] = true;
            images[index] = `${Define.constants.serverMediaAddr}${result.res.filename}`;
            if (type === 'avatarImage') {
              this.setState({
                avatarImage: images[2]
              });
            } else if(type === 'backCerImage') {
              this.setState({
                backCerImage: images[1]
              });
            } else{
              this.setState({
                frontCerImage: images[0]
              })
            }
          }
        })
        .catch((err) => {
          images.splice(0, 1);
          this.idImg.splice(index, 1);
          this.uploadSuccess.splice(index, 1);
          if (type === 'avatarImage') {
            this.setState({
              avatarImage: images
            });
          } else if(type === 'backCerImage') {
            this.setState({
              backCerImage: images
            });
          } else{
            this.setState({
              frontCerImage: images
            })
          }
        })
    }

    render() {
      var {user,dispatch} = this.props;
      var self = this;
      let textStatus = "";
      let textError = "";

      if(this.state.status !== null) {
        switch (this.state.status) {
          case 0: textStatus = 'Hồ sơ của bạn đang được xem xét';
                  break;
          case 1: textStatus = 'Hồ sơ đã được chấp thuận';
                  break;
          case 2: textStatus = 'Hồ sơ của bạn đã bị từ chối';
                  textError = this.props.authenInf.reason;
                  break;
        }
      }
      return (
        <View style={{ backgroundColor: '#fff', paddingHorizontal: 16 }}>
          <View style={{}}>
            {textStatus ?
              <Include.Text style={{ textAlign: 'center', color: '#f1c40f', fontSize: 15, paddingVertical: 3 }}>{textStatus}</Include.Text>
              : null}
            {textError ?
              <Include.Text style={{ textAlign: 'center', color: '#e74c3c', fontSize: 15, paddingVertical: 3 }}>{textError}</Include.Text>
              : null}
            {this.props.authenInf ?
              <Include.Text style={{ textAlign: 'center', fontSize: 15, paddingBottom: 5, fontStyle: 'italic', color: '#ecf0f1' }}>Cập nhật lần cuối: {moment(this.props.authenInf.updatedAt).format('HH:mm:ss DD/MM/YYYY')}</Include.Text>
              : null}
            <View style={{ paddingTop: 8 }}>
              <Include.Text>Bạn vui lòng chụp ảnh chân dung và 2 mặt ảnh CCCD/CMTND để chúng tôi xác thực tài khoản của bạn. Những thông tin này sẽ được bảo mật tuyệt đối. Xin cảm ơn.</Include.Text>
              <Include.Text style={{ color: '#161616', fontSize: 16, marginVertical: 16, fontFamily: Define.constants.fontBold500 }}>CHỤP ẢNH CCCD/CMTND</Include.Text>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <View style={{ alignItems: 'center', marginRight: 16 }}>
                  <TouchableOpacity style={{ borderRadius: 8, borderWidth: 1, borderStyle: 'dashed', padding: 16, alignItems: 'center' }}
                    onPress={() => {
                      if (this.state.frontCerImage) {
                        popupActions.setRenderContentAndShow(DefaultPopup,
                          {
                            title: 'Thông báo',
                            description: 'Bạn có chắc chắn muốn thay đổi ảnh không?',
                            buttonTitle: 'Đồng ý',
                            onPress: () => {
                              popupActions.popPopup()
                              self.handlePickImage('frontCerImage');
                            },
                            buttonTitle2: 'Không',
                            onPress2: () => {
                              popupActions.popPopup()
                            }
                          })
                      } else {
                        self.handlePickImage('frontCerImage');
                      }
                    }}>
                    {this.uploadSuccess.length && this.uploadSuccess[0] === false ?
                      <View style={{ width: '100%', height: '100%', position: 'absolute', zIndex: Platform.OS === 'ios' ? 1 : null, justifyContent: 'center', alignItems: 'center', elevation: 2 }}>
                        <ActivityIndicator size={'large'} color={'#0473CD'} />
                      </View> : null}
                    {this.state.frontCerImage ?
                      <Image source={{uri: this.state.frontCerImage}} style={{opacity: this.uploadSuccess.length && this.uploadSuccess[0] ? 1 : 0.5, resizeMode: 'stretch', borderRadius: 3, width: (Define.constants.widthScreen - 112) / 2, height: (Define.constants.widthScreen - 112) / 3 }} />
                      :
                      <Image source={{uri:'https://media.heyu.asia/uploads/new-img-service/2022-02-26-icon-back-cmt.png'}} style={{opacity: this.uploadSuccess.length && this.uploadSuccess[0] ? 1 : 0.5, resizeMode: 'stretch', borderRadius: 3, width: (Define.constants.widthScreen - 112) / 2, height: (Define.constants.widthScreen - 112) / 3 }} />}
                    <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 6 }}>
                      <HeyUIcon name={'fi-rr-camera'} color={'#00a8ff'} size={18} />
                      <Include.Text style={{ marginLeft: 6, fontFamily: Define.constants.fontBold500, color: '#00a8ff' }}>Chụp ảnh</Include.Text>
                    </View>
                  </TouchableOpacity>
                  <Include.Text style={{}}>Mặt trước</Include.Text>
                </View>
                <View style={{ alignItems: 'center' }}>
                  <TouchableOpacity style={{ borderRadius: 8, borderWidth: 1, borderStyle: 'dashed', padding: 16, alignItems: 'center' }}
                    onPress={() => {
                      if (this.state.backCerImage) {
                        popupActions.setRenderContentAndShow(DefaultPopup,
                          {
                            title: 'Thông báo',
                            description: 'Bạn có chắc chắn muốn thay đổi ảnh không?',
                            buttonTitle: 'Đồng ý',
                            onPress: () => {
                              popupActions.popPopup()
                              self.handlePickImage('backCerImage');
                            },
                            buttonTitle2: 'Không',
                            onPress2: () => {
                              popupActions.popPopup()
                            }
                          })
                      } else {
                        self.handlePickImage('backCerImage');
                      }
                    }}>
                    {this.uploadSuccess.length && this.uploadSuccess[1] === false ?
                      <View style={{ width: '100%', height: '100%', position: 'absolute', zIndex: Platform.OS === 'ios' ? 1 : null, justifyContent: 'center', alignItems: 'center', elevation: 2 }}>
                        <ActivityIndicator size={'large'} color={'#0473CD'} />
                      </View> : null}
                    {this.state.backCerImage ?
                      <Image source={{uri: this.state.backCerImage}} style={{opacity: this.uploadSuccess.length && this.uploadSuccess[1] ? 1 : 0.5, resizeMode: 'stretch', borderRadius: 3, width: (Define.constants.widthScreen - 112) / 2, height: (Define.constants.widthScreen - 112) / 3 }} />
                      :
                      <Image source={{uri: 'https://media.heyu.asia/uploads/new-img-service/2022-02-26-icon-back-cmt.png'}} style={{opacity: this.uploadSuccess.length && this.uploadSuccess[1] ? 1 : 0.5, resizeMode: 'stretch', borderRadius: 3, width: (Define.constants.widthScreen - 112) / 2, height: (Define.constants.widthScreen - 112) / 3 }} />}
                    <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 6 }}>
                      <HeyUIcon name={'fi-rr-camera'} color={'#00a8ff'} size={18} />
                      <Include.Text style={{ marginLeft: 6, fontFamily: Define.constants.fontBold500, color: '#00a8ff' }}>Chụp ảnh</Include.Text>
                    </View>
                  </TouchableOpacity>
                  <Include.Text style={{}}>Mặt sau</Include.Text>
                </View>
              </View>
              <Include.Text style={{ color: '#161616', fontSize: 16, marginVertical: 16, fontFamily: Define.constants.fontBold500 }}>CHỤP ẢNH CHÂN DUNG</Include.Text>
              <View style={{ alignSelf: 'center' }}>
                <TouchableOpacity style={{ borderRadius: 8, borderWidth: 1, borderStyle: 'dashed', padding: 16, alignItems: 'center' }}
                  onPress={() => {
                    if (this.state.avatarImage) {
                      popupActions.setRenderContentAndShow(DefaultPopup,
                        {
                          title: 'Thông báo',
                          description: 'Bạn có chắc chắn muốn thay đổi ảnh không?',
                          buttonTitle: 'Đồng ý',
                          onPress: () => {
                            popupActions.popPopup()
                            self.handlePickImage('avatarImage');
                          },
                          buttonTitle2: 'Không',
                          onPress2: () => {
                            popupActions.popPopup()
                          }
                        })
                    } else {
                      self.handlePickImage('avatarImage');
                    }
                  }}>
                  {this.uploadSuccess.length && this.uploadSuccess[2] === false ?
                    <View style={{ width: '100%', height: '100%', position: 'absolute', zIndex: Platform.OS === 'ios' ? 1 : null, justifyContent: 'center', alignItems: 'center', elevation: 2 }}>
                      <ActivityIndicator size={'large'} color={'#0473CD'} />
                    </View> : null}
                  {this.state.avatarImage ?
                    <Image source={{uri: this.state.avatarImage}} style={{opacity: this.uploadSuccess.length && this.uploadSuccess[2] ? 1 : 0.5, resizeMode: 'stretch', borderRadius: 3, width: (Define.constants.widthScreen - 112) / 2, height: (Define.constants.widthScreen - 112) / 3 }} />
                    :
                    <Image source={{uri:'https://media.heyu.asia/uploads/new-img-service/2022-02-26-icon-avatar-cmt.png'}} style={{opacity: this.uploadSuccess.length && this.uploadSuccess[2] ? 1 : 0.5, resizeMode: 'stretch', borderRadius: 3, width: (Define.constants.widthScreen - 112) / 2, height: (Define.constants.widthScreen - 112) / 3 }} />}
                  <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 6 }}>
                    <HeyUIcon name={'fi-rr-camera'} color={'#00a8ff'} size={18} />
                    <Include.Text style={{ marginLeft: 6, fontFamily: Define.constants.fontBold500, color: '#00a8ff' }}>Chụp ảnh</Include.Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
            <TouchableOpacity style={{ backgroundColor: this.state.disable ? '#ddd' : '#0473CD', padding: 16, borderRadius: 16, marginTop: 20, marginHorizontal: 16, justifyContent: 'center', alignItems: 'center' }}
              disabled={this.state.disable }
              onPress={this.handleAuthentication}>
              <Include.Text style={{ color: '#fff', fontSize: 20, fontFamily: Define.constants.fontBold500 }}>GỬI XÁC THỰC</Include.Text>
            </TouchableOpacity>
          </View>
        </View>
      )
    }
    UNSAFE_componentWillReceiveProps(nextProps) {
      if(this.props.authenInf !== nextProps.authenInf) {
        this.setState({
          id: nextProps.authenInf._id,
          avatarImage: nextProps.authenInf.avatar,
          frontCerImage: nextProps.authenInf.frontCer,
          backCerImage: nextProps.authenInf.backCer,
          status: nextProps.authenInf.status
        })

        this.currentInf = {
          id: nextProps.authenInf._id,
          avatarImage: nextProps.authenInf.avatar,
          frontCerImage: nextProps.authenInf.frontCer,
          backCerImage: nextProps.authenInf.backCer,
        }
      }
    }
}

// function bindAction(dispatch) {
//     return {
//     }
// }
//
// export default connect(null, bindAction)(Settings);
export default Authentication;
