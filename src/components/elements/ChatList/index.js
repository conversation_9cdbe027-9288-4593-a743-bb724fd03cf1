var _ = require('lodash')
//LIB
import React  from 'react';
import {
  Modal,
  View,
  TouchableOpacity,
  InteractionManager,
  LayoutAnimation,
  Linking,
  Platform,
  NativeModules,
  PermissionsAndroid,
  Keyboard,
  Image,
  ActivityIndicator,
  FlatList,
  SafeAreaView,
  ImageBackground,
  StatusBar,
  Component,
  StyleSheet,
  Text,
  Animated,
  KeyboardAvoidingView,
  ScrollView,
} from 'react-native';

import moment from 'moment';
import {Ionicons as Icon} from '@react-native-vector-icons/ionicons';
import { connect } from 'react-redux';
var RNIntent = NativeModules.RNIntent;
import ParsedText from 'react-native-parsed-text';
import ImageViewer from 'react-native-image-zoom-viewer';
import { CameraRoll } from "@react-native-camera-roll/camera-roll"
import Marked from 'react-native-marked';
import Clipboard from '@react-native-community/clipboard';
var NotifyUtil = require('../../../Util/notify');
import LinearGradient from 'react-native-linear-gradient';

//action

//components
var Define = require('../../../Define');
var Debug = require('../../../Util/Debug');
var facebookUtils = require('../../../Util/facebook');
var Themes = require('../../../Themes');
var Util = require('../../../Util/Util');
var UserUtil = require('../../../Util/user');
var Include = require('../../../Include');
import HeyUIcon from '../../elements/HeyUIcon/HeyUIcon';

import ReactComponent from '../../ReactComponent'

var ButtonWrap = require('../ButtonWrap');
import SaveButton from '../buttons/save'

var {popupActions} = require('../../popups/PopupManager');
import ImageViewerPopup from '../../popups/ImageViewerPopup'
var {globalVariableManager}= require('../../modules/GlobalVariableManager');
var Spinner = require('react-native-spinkit');

import FeedsActions_MiddleWare from '../../../actions/FeedsActions_MiddleWare'

import DefaultPopup from '../../popups/DefaultPopup';
import ChatItem from './ChatItem';
import PenguinLinearIcon from '../PenguinLinearIcon/PenguinLinearIcon';
import { getStatusBarHeight } from 'react-native-status-bar-height';

var md = require('markdown-it')(),
    mathjax3 = require('markdown-it-mathjax3');
import Markdown, {
  MarkdownIt,
} from 'react-native-markdown-display';
import MathJax from 'react-native-mathjax-svg';

const rules = {
  math_inline: (node, children) => {
    if (Platform.OS === 'ios') {
      return (
        <View style={{ maxWidth: '100%', alignItems: 'flex-start' }}>
          <MathJax
            fontSize={16}
            color={'#78ebfa'}
            style={{ maxWidth: '100%' }}
          >
            {node.content}
          </MathJax>
        </View>
      )
    }

    return (
      <ScrollView
        nestedScrollEnabled
        horizontal
        showsHorizontalScrollIndicator={false}
        removeClippedSubviews={true}
        contentContainerStyle={{
          alignItems: Platform.OS === 'ios' ? 'center' : null,
          justifyContent: Platform.OS === 'ios' ? 'center' : null,
          flexGrow: 1,
          paddingBottom: Platform.OS === 'ios' ? -16 : 0
        }}>
        <MathJax
          fontSize={16}
          color={'#78ebfa'}
        >
          {node.content}
        </MathJax>
      </ScrollView>
    )
  },
  math_block: (node, children) => {
    return (
      <ScrollView
        nestedScrollEnabled
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{paddingBottom: 8}}>
        <MathJax
          fontSize={16}
          color={'#78ebfa'}
        >
          {node.content}
        </MathJax>
      </ScrollView>
    )
  },
}

const markdownit = new MarkdownIt({
  typographer: true,
}).use(mathjax3, {
  inlineOpen: '\\(',
  inlineClose: '\\)',
  blockOpen: '\\[',
  blockClose: '\\]',
  tex: {
    inlineMath: [['$', '$']],
    displayMath: [['$$', '$$']],
    processEscapes: true,
  },
  options: {
    output: {
      renderer: 'svg',
    }
  }
})
class ChatList extends ReactComponent{
  static componentName = 'ChatList'
  static defaultProps = {
    onHead:()=>{},
    onScroll:()=>{},
    onContentSizeChange:()=>{}
  }
  // static propTypes = {}
  constructor(props){
    super(props);
    this.state = _.merge(this.state,
    {
      minIndex: 0,
      havePadding: false,
    })
    this.animated = new Animated.Value(0)
    this.animation = null
    this.questionHeight = 0;
    this.contentHeight = 0;
    this.scrollPosition = { x: 0, y: 0 }
    this.paddingTop = 32
  }
  renderAdditionAction(rowData, sectionID, rowID){
    return null;
  }
  fadeInOut = () => {
    // Will change animated value to 1 in 5 seconds
    this.animation = Animated.loop(
      Animated.sequence([
        Animated.timing(this.animated, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(this.animated, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        })
      ])
    );
    this.animation.start();
  };
  stopAnimation = () => {
    if (this.animation) {
      this.animation.stop();
    }
  };
  handleCall(phone, matchIndex){
    var url = `tel:${phone}`;
    if(Platform.OS === 'ios') {
      url = `telprompt:${phone}`;
    }
    Linking
      .canOpenURL(url)
      .then((supported) => {
        if(supported) {
          return Linking.openURL(url);
        }
        return Promise.reject();
      })
      .catch((err) => {
        Debug.log(err,Debug.level.ERROR)
      })
  }

  hintPlace(latitude,longitude){
    if(Platform.OS === 'ios') {
      const linkGooglemaps = `comgooglemaps://?daddr=${latitude},${longitude}&directionsmode=driving`;
      Linking
        .canOpenURL(linkGooglemaps)
        .then((supported) => {
          if (!supported) {
            Linking.openURL(`http://maps.apple.com/?daddr=${latitude},${longitude}`)
          } else {
            return Linking.openURL(linkGooglemaps);
          }
        })
        .catch((err) => {
        });
    } else {
      Linking.openURL(`http://maps.google.com/maps?daddr=${latitude},${longitude}`)
    }
    popupActions.popPopup();
  }

  onDownloadImagePress = (indexUrl) => {
    indexUrl = indexUrl || 0;

    if (this.props.imageArr.length && this.props.imageArr[indexUrl].url) {
      globalVariableManager.rootView.showToast('Tải ảnh thành công', 2000);
      // CameraRoll.saveToCameraRoll(this.props.imageArr[indexUrl].url);
      const image = CameraRoll.save(this.props.imageArr[indexUrl].url);
    }
  }
  copyRouter(answer) {
    Clipboard.setString(answer)
      NotifyUtil.pushAlertTopNotify({
        type: 'success',
        content: 'Sao chép câu trả lời thành công',
        timeClose: 2000
      })
  }

  keyboardWillShow() {
    // setTimeout(() => {
    //   if(this.listView) {
    //     this.listView.scrollToOffset({y:0});
    //   }
    // },300)
  }
  UNSAFE_componentWillUpdate(){
    super.UNSAFE_componentWillUpdate();
  }

  UNSAFE_componentWillMount() {
    this.keyboardDidShowSubscription = Keyboard.addListener('keyboardDidShow', this.keyboardWillShow)
  }
  componentWillUnmount() {
    this.keyboardDidShowSubscription.remove()
  }

  renderFooter = () =>{
    if(!this.props.isGettingMore){ return null }
    return(
      <View style={{alignSelf:'center', padding: 10}}>
        <ActivityIndicator size={'small'} color={'#3498db'} />
      </View>
    )
  }
  handleScroll = (event) => {
    const { x, y } = event.nativeEvent.contentOffset
    if (this.scrollPosition.y !== y && this.state.havePadding && this.touch) {
      // this.paddingBottom = (Define.constants.heightScreen / 1.3) - (Define.constants.heightScreen - this.state.bottom);
      this.setState({ havePadding: false }, () => {
        // this.scrollPosition = { x, y };
        this.paddingTop = 32
      })
    }
    // setTimeout(() => {
      this.scrollPosition = { x, y }
    // }, 100);
  };
  onLayoutQuestion = (event) => {
    this.questionHeight = event.nativeEvent.layout.height;
  }
  triggerPadding = (bottomKeyboardHeight) => {
    let {heightTextInput, heightNewConversation, heightProfile} = this.props
    let {heightScreen} = Define.constants
    let statusBarHeight = Platform.OS === 'ios' ? getStatusBarHeight(true) : 0;
    let bottomTextInput = heightTextInput //71.467
    let newConversationButton = heightNewConversation //33.067
    let textPaddingOnTop = 10
    let paddingTop = heightScreen - bottomTextInput - this.questionHeight - newConversationButton - statusBarHeight - textPaddingOnTop - bottomKeyboardHeight - heightProfile
    this.paddingTop = paddingTop
  }
  renderContent(){
    var content = null;
    if (this.props.thinkingText) {
      this.fadeInOut()
    }
    else this.stopAnimation()
    // this.paddingBottom = (Define.constants.heightScreen / 1.3) - (Define.constants.heightScreen - this.state.bottom);
    // if (Define.constants.heightScreen / 1.3 - this.paddingBottom >= this.contentHeight || this.scrollPosition.y > 50 || this.paddingBottom < 0) {
    //   this.paddingBottom = 0;
    // }
    content=(
    <View style={{flex: 1}}>
      <FlatList
        {...this.props}
        data={this.props.infos}
        contentContainerStyle={{
          // paddingTop: this.paddingTop,
          // flexGrow: 1,
          // justifyContent: 'flex-end',
          paddingBottom: this.paddingTop
        }}
        onTouchStart={() => {
          this.touch = true;
        }}
        onTouchEnd={() => {
          this.touch = false;
        }}
        nestedScrollEnabled={true}
        keyExtractor={(item, index) => index}
        extraData={this.state}
        removeClippedSubviews={false}
        inverted={false}
        // onScroll={this.handleScroll}
        onContentSizeChange={(contentWidth, contentHeight) => {
          this.contentHeight = contentHeight;

          if (this.props.triggerScroll) {
          //   this.scrollPosition = { x: 0, y: 0 };
            this.triggerPadding(this.props.bottomKeyboardHeight)
            this.setState({havePadding: true}, () => {
              InteractionManager.runAfterInteractions(() => {
                setTimeout(() => {
                  this.listView?.scrollToOffset({ offset: contentHeight + this.paddingTop, animated: true });
                }, 100)
              });
            })
          }
        }}
        maintainVisibleContentPosition={{
          minIndexForVisible: 1,
        }}
        ref={(listView)=>{this.listView = listView}}
        refreshControl={this.props.refreshControl}
        initialListSize = {6}
        onLayout={(event)=>{
          this.scrollHeight = event.nativeEvent.layout.height;
        }}
        onEndReachedThreshold={0.3}
        onEndReached={this.props.onEndReached}
        scrollEventThrottle={200}
        style={[this.props.style]}
        renderItem={({ item, index }) =>{
          let markDownText = this.props.markdownText && this.props.markdownText.trim()
          let markDownReasoning = this.props.markdownTextReasoning && this.props.markdownTextReasoning.trim()
          if (this.props.isLoading && index === this.props.infos.length - 1) {
            item.isLoading = this.props.isLoading
          }
            if (this.props.thinkingText && index === this.props.infos.length - 1) {
              return (
                <View style={{marginBottom: 10, }}>
                  {/* {item.isRenderTime && item.createdAt ?
                    <Include.Text style={{ textAlign: 'center', fontSize: 15.5, paddingBottom: 5, color: '#fff', fontStyle: 'italic'}}>
                      {Util.date2String(new Date(), 'dd/mm  HH:MM')}
                    </Include.Text>
                  : null} */}
                  {item.question?<View style={{flexDirection: 'row', alignItems: 'flex-end',  marginBottom: 10}}>
                    <View style={{width: 100, borderRadius: 20, }}></View>
                    <View style={{flex:1, padding: 12, backgroundColor:'#007CFE',
                      borderTopLeftRadius: 16,
                      borderTopRightRadius: 16,
                      borderBottomLeftRadius: 16,
                      marginRight: 12,
                    }}>
                      <Include.Text style={{color:'#fff', fontSize: 15.5, fontFamily: Define.constants.fontBold500,}}>{item.question}</Include.Text>
                    </View>
                  </View>:null}
                    <View
                      style={{flexDirection: 'row', alignItems: 'flex-start',}}>
                      <Image style={{ width: 47, height: 47, borderRadius: 20, alignSelf: 'flex-start', }} source={Define.assets.Images.chatbot} />
                      <View
                        style={{
                          flex:1,
                          paddingHorizontal: 10,
                          backgroundColor:'#000',
                          borderBottomLeftRadius: 16,
                          borderTopRightRadius: 16,
                          borderBottomRightRadius: 16,
                        }}>
                        {this.props.markdownTextReasoning ?
                          <View
                            style={{
                              borderLeftWidth: 2,
                              borderColor: '#b3b5b67a',
                              paddingLeft: 12,
                              marginTop: 8,
                            }}>
                            {Platform.OS === 'ios' ?
                              <Markdown
                                rules={rules}
                                markdownit={markdownit}
                                style={styles.markDownDisplayReason}>
                                {markDownReasoning}
                              </Markdown>
                              :
                              <Marked
                                value={markDownReasoning}
                                flatListProps={{
                                  style: {
                                    backgroundColor: '#000',
                                  },
                                }}
                                styles={styles.markDownReason}
                              />
                            }
                          </View>
                        : null}
                        <Animated.View
                          style={{
                            opacity: this.animated
                          }}>
                          {Platform.OS === 'ios' ?
                            <Markdown
                              rules={rules}
                              markdownit={markdownit}
                              style={styles.markDownDisplayReason}>
                              {this.props.thinkingText}
                            </Markdown>
                            :
                            <Marked
                              value={this.props.thinkingText}
                              flatListProps={{
                                style: {
                                  backgroundColor: '#000',
                                },
                              }}
                              styles={styles.markDownReason}
                            />
                          }
                        </Animated.View>
                      </View>
                      <View style={{width: 20, height: 20, borderRadius: 10, }}></View>
                    </View>
                </View>
              )
            }
            // else if (this.props.isLoading && index === 0) {
            //   return (
            //     <View style={{marginBottom: 10, }}>
            //       {item.isRenderTime && item.createdAt ?
            //         <Include.Text style={{ textAlign: 'center', fontSize: 15.5, paddingBottom: 5, color: '#fff', fontStyle: 'italic'}}>
            //           {Util.date2String(new Date(), 'dd/mm  HH:MM')}
            //         </Include.Text>
            //       : null}
            //       {item.question?<View style={{flexDirection: 'row', alignItems: 'flex-end',  marginBottom: 10}}>
            //         <View style={{width: 100, borderRadius: 20, }}></View>
            //         <View onLayout={(e) => {
            //           this.questionHeight = e.nativeEvent.layout.height
            //         }} style={{flex:1, padding: 12, backgroundColor:'#007CFE',
            //           borderTopLeftRadius: 16,
            //           borderTopRightRadius: 16,
            //           borderBottomLeftRadius: 16,
            //           marginRight: 12,
            //         }}>
            //           <Include.Text style={{color:'#fff', fontSize: 15.5, fontFamily: Define.constants.fontBold500,}}>{item.question}</Include.Text>
            //         </View>
            //       </View>:null}
            //       <View style={{flexDirection: 'row', alignItems: 'center', marginRight:12}}>
            //         <Image style={{width: 47, height: 47, borderRadius: 20}} source={Define.assets.Images.chatbot}/>
            //         <Spinner type={'ThreeBounce'} color={'#0084ff'} size={25} style={{marginTop: 10}}/>
            //       </View>
            //     </View>
            //   )
            // }
            else if((this.props.markdownText || this.props.markdownTextReasoning) && index === this.props.infos.length - 1) {
              return (
                <View style={{marginBottom: 10, }}>
                  {/* {item.isRenderTime && item.createdAt ?
                    <Include.Text style={{ textAlign: 'center', fontSize: 15.5, paddingBottom: 5, color: '#fff', fontStyle: 'italic'}}>
                      {Util.date2String(new Date(), 'dd/mm  HH:MM')}
                    </Include.Text>
                  : null} */}
                  {item.question?<View style={{flexDirection: 'row', alignItems: 'flex-end',  marginBottom: 10}}>
                    <View style={{width: 100, borderRadius: 20, }}></View>
                    <View style={{flex:1, padding: 12, backgroundColor:'#007CFE',
                          borderTopLeftRadius: 16,
                          borderTopRightRadius: 16,
                          borderBottomLeftRadius: 16,
                          marginRight: 12,
                        }}>
                        <Include.Text style={{ color: '#fff', fontSize: 15.5, fontFamily: Define.constants.fontBold500 }}>{item.question}</Include.Text>
                    </View>
                  </View>:null}
                  {this.props.markdownText || this.props.markdownTextReasoning?
                  <View
                    style={{flexDirection: 'row', alignItems: 'flex-start',}}>
                    <Image style={{ width: 47, height: 47, borderRadius: 20, alignSelf: 'flex-start', }} source={Define.assets.Images.chatbot} />
                    <View
                      style={{
                        flex:1,
                        paddingHorizontal: 10,
                        backgroundColor:'#000',
                        borderBottomLeftRadius: 16,
                        borderTopRightRadius: 16,
                        borderBottomRightRadius: 16,
                        paddingVertical: 8
                      }}>
                      {item.textReasoning ?
                        <View
                          style={{marginTop: 4, marginHorizontal: 8}}>
                          <Text allowFontScaling={false}
                            style={{
                              fontSize: 13,
                              fontFamily: Define.constants.fontBold500,
                              color: '#1589d8',
                            }}>
                            {item.textReasoning}
                          </Text>
                        </View>
                      : null}
                      {this.props.markdownTextReasoning ?
                        <View
                          style={{
                            borderLeftWidth: 2,
                            borderColor: '#b3b5b67a',
                            paddingLeft: 12,
                          }}>
                          {Platform.OS === 'ios' ?
                            <Markdown
                              rules={rules}
                              markdownit={markdownit}
                              style={styles.markDownDisplayReason}>
                              {markDownReasoning}
                            </Markdown>
                            :
                            <Marked
                              value={markDownReasoning}
                              flatListProps={{
                                style: {
                                  backgroundColor: '#000',
                                },
                              }}
                              styles={styles.markDownReason}
                            />
                          }
                        </View>
                      : null}
                      {this.props.markdownText ?
                        Platform.OS === 'ios' ?
                        <Markdown
                          rules={rules}
                          markdownit={markdownit}
                          style={styles.markDownDisplayMessage}>
                          {markDownText}
                        </Markdown>
                        :
                        <Marked
                          value={markDownText}
                          flatListProps={{
                            style: {
                              backgroundColor: '#000',
                            },
                          }}
                          styles={styles.markDownMessage}
                        />
                      : null}
                    </View>
                    <View style={{width: 20, height: 20, borderRadius: 10, }}></View>
                  </View>
                  :null}
                </View>
              )
            }
            return <ChatItem item={item} index={index} stopStream={this.props.stopStream} isLoading={this.props.isLoading} infos={this.props.infos} onLayoutQuestion={this.onLayoutQuestion} getMessage={this.props.getMessage} />
          }
        }
        // ListFooterComponent={this.renderFooter}
        />
        {/* {this.state.scrollPosition.y > 150 ?
          <TouchableOpacity
            onPress={() => {
              this.listView && this.listView.scrollToOffset({offset: 0})
            }}
            style={{
              width: 40,
              height: 40,
              borderRadius: 48,
              position: 'absolute',
              bottom: -4,
              right: 12,
              backgroundColor: '#021E38',
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <PenguinLinearIcon name={'arrow-down'} size={24} color={'#fff'}/>
          </TouchableOpacity>
          : null} */}
      </View>
    )
    return(content)
  }

  UNSAFE_componentWillUpdate(){
    super.UNSAFE_componentWillUpdate();
  }
  componentWillUnmount() {
    this.keyboardDidShowSubscription.remove()
    this.keyboardDidHideSubscription.remove()
  }
  keyboardWillShow = (e) => {
    const heightKeyboard = e.endCoordinates.height;
    if (Platform.OS === 'ios') {
      // if (this.contentHeight < this.paddingTop) {
      //   this.triggerPadding(heightKeyboard)
      //   setTimeout(() => {
      //     this.listView.scrollToOffset({
      //       offset: 0,
      //       animated: true
      //     })
      //   }, 100)
      // }
      this.setState({
        bottom: heightKeyboard,
        keyboardShow: true
      });
    } else {
      this.setState({
        bottom: heightKeyboard,
        keyboardShow: true
      })
    }
  }

  keyboardWillHide = (e) => {
    if (Platform.OS === 'ios') {
      // if (this.contentHeight < this.paddingTop) {
      //   this.triggerPadding(heightKeyboard)
      //   setTimeout(() => {
      //     this.listView.scrollToOffset({
      //       offset: 0,
      //       animated: true
      //     })
      //   }, 100)
      // }
      this.setState({
        bottom: 0,
        keyboardShow: false
      });
    } else {
      this.setState({
        bottom: 0,
        keyboardShow: false
      })
    }
  }

  UNSAFE_componentWillMount() {
    super.UNSAFE_componentWillMount();

    if (Platform.OS === 'android') {
      this.keyboardDidShowSubscription = Keyboard.addListener('keyboardDidShow', this.keyboardWillShow)
      this.keyboardDidHideSubscription = Keyboard.addListener('keyboardDidHide', this.keyboardWillHide)
    } else {
      this.keyboardDidShowSubscription = Keyboard.addListener('keyboardWillShow', this.keyboardWillShow)
      this.keyboardDidHideSubscription = Keyboard.addListener('keyboardWillHide', this.keyboardWillHide)
    }
  }
}
const styles = StyleSheet.create({
  markDownDisplayMessage: {
    text: {
      color: '#fff',
      fontFamily: Define.constants.fontBold400,
    },
    em: {
      color: '#fff',
    },
    body: {
      fontSize: 15.5,
      color: '#fff',
      fontFamily: Define.constants.fontBold400,
    },
    code_inline: {backgroundColor: '#000', color: '#fff'},
    code_block: {backgroundColor: '#000', color: '#fff'},
    hr: {backgroundColor: '#fff', height: 1, color: '#fff'},
    blockquote: {backgroundColor: '#000'},
    heading1: { color: '#fff', fontSize: 19},
    heading2: { color: '#fff', fontSize: 18.5},
    heading3: { color: '#fff', fontSize: 18},
    heading4: { color: '#fff', fontSize: 17},
    heading5: { color: '#fff', fontSize: 16.5},
    heading6: { color: '#fff', fontSize: 16},
    paragraph: { color: '#fff'},
    ordered_list: { color: '#fff'},
    bullet_list: {color: '#fff'},
    strong: { color: '#fff', fontSize: 15.5},
    link: { color: '#007DFF'},
    strikethrough: { color: '#fff'},
    codespan: { color: '#fff'},
  },
  markDownDisplayReason: {
    text: {
      color: '#b3b5b6',
      fontFamily: Define.constants.fontBold400
    },
    em: {
      color: '#b3b5b6',
    },
    body: {
      fontSize: 13.5,
      color: '#b3b5b6',
      fontFamily: Define.constants.fontBold400,
    },
    code_inline: {backgroundColor: '#000', color: '#b3b5b6'},
    code_block: {backgroundColor: '#000', color: '#b3b5b6'},
    hr: {backgroundColor: '#b3b5b6', height: 1, color: '#b3b5b6'},
    blockquote: {backgroundColor: '#000'},
    heading1: { color: '#b3b5b6', fontSize: 19},
    heading2: { color: '#b3b5b6', fontSize: 18.5},
    heading3: { color: '#b3b5b6', fontSize: 18},
    heading4: { color: '#b3b5b6', fontSize: 17},
    heading5: { color: '#b3b5b6', fontSize: 16.5},
    heading6: { color: '#b3b5b6', fontSize: 16},
    paragraph: { color: '#b3b5b6'},
    ordered_list: { color: '#b3b5b6'},
    bullet_list: {color: '#b3b5b6'},
    strong: { color: '#b3b5b6', fontSize: 15.5},
    link: { color: '#007DFF7a'},
    strikethrough: { color: '#b3b5b6'},
    codespan: { color: '#b3b5b6'},
  },
  markDownMessage: {
    text: {
      color: '#fff',
      fontSize: 15.5,
      fontFamily: Define.constants.fontBold400,
    },
    em: {
      color: '#fff',
    },
    h1: { color: '#fff', fontSize: 19},
    h2: { color: '#fff', fontSize: 18.5},
    h3: { color: '#fff', fontSize: 18},
    h4: { color: '#fff', fontSize: 17},
    h5: { color: '#fff', fontSize: 16.5},
    h6: { color: '#fff', fontSize: 16},
    paragraph: { color: '#fff'},
    li: { color: '#fff'},
    strong: { color: '#fff', fontSize: 15.5},
    link: { color: '#007DFF'},
    strikethrough: { color: '#fff'},
    codespan: { color: '#fff'},
  },
  markDownReason: {
    text: {
      color: '#b3b5b6',
      fontSize: 13.5,
      fontFamily: Define.constants.fontBold400
    },
    em: {
      color: '#b3b5b6',
    },
    h1: { color: '#b3b5b6', fontSize: 19},
    h2: { color: '#b3b5b6', fontSize: 18.5},
    h3: { color: '#b3b5b6', fontSize: 18},
    h4: { color: '#b3b5b6', fontSize: 17},
    h5: { color: '#b3b5b6', fontSize: 16.5},
    h6: { color: '#b3b5b6', fontSize: 16},
    paragraph: { color: '#b3b5b6'},
    li: { color: '#b3b5b6'},
    strong: { color: '#b3b5b6', fontSize: 15.5},
    link: { color: '#007DFF7a'},
    strikethrough: { color: '#b3b5b6'},
    codespan: { color: '#b3b5b6'},
  }
})
/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  return {
    navigator: state.Navigator
  }
}

export default connect(selectActions, undefined, undefined, {withRef: true})(ChatList);
// export default ChatList