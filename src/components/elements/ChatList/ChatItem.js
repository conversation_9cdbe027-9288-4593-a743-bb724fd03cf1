var _ = require('lodash')
//LIB
import React  from 'react';
import {
  Modal,
  View,
  TouchableWithoutFeedback,
  InteractionManager,
  LayoutAnimation,
  Linking,
  Platform,
  NativeModules,
  PermissionsAndroid,
  Keyboard,
  Image,
  ActivityIndicator,
  FlatList,
  SafeAreaView,
  ImageBackground,
  StatusBar,
  Component,
  StyleSheet,
  Text,
  TouchableOpacity,
  Animated,
  Vibration,
  ScrollView,
} from 'react-native';
import moment from 'moment';
import { connect } from 'react-redux';
var RNIntent = NativeModules.RNIntent;
import ParsedText from 'react-native-parsed-text';
import ImageViewer from 'react-native-image-zoom-viewer';
import { CameraRoll } from "@react-native-camera-roll/camera-roll"
var md = require('markdown-it')(),
    mathjax3 = require('markdown-it-mathjax3');
import MarkdownIt from 'markdown-it';
import Markdown, {
} from 'react-native-markdown-display';
import MathJax from 'react-native-mathjax-svg';
import Marked, { <PERSON><PERSON><PERSON>, MarkedTokenizer, MarkedLexer } from 'react-native-marked'
import Clipboard from '@react-native-community/clipboard';
var Spinner = require('react-native-spinkit');
var NotifyUtil = require('../../../Util/notify');
import LinearGradient from 'react-native-linear-gradient';

//action

//components
var Define = require('../../../Define');
var Debug = require('../../../Util/Debug');
var facebookUtils = require('../../../Util/facebook');
var Themes = require('../../../Themes');
var Util = require('../../../Util/Util');
var UserUtil = require('../../../Util/user');
var Include = require('../../../Include');
import HeyUIcon from '../../elements/HeyUIcon/HeyUIcon';

import ReactComponent from '../../ReactComponent'

var ButtonWrap = require('../ButtonWrap');
import SaveButton from '../buttons/save'
import PenguinBoldIcon from '../PenguinBoldIcon/PenguinBoldIcon';
import PenguinLinearIcon from '../PenguinLinearIcon/PenguinLinearIcon';
var {popupActions} = require('../../popups/PopupManager');
import ImageViewerPopup from '../../popups/ImageViewerPopup'
var {globalVariableManager}= require('../../modules/GlobalVariableManager');

import FeedsActions_MiddleWare from '../../../actions/FeedsActions_MiddleWare'
import IHeyUActions_MiddleWare from '../../../actions/IHeyUActions_MiddleWare'

import DefaultPopup from '../../popups/DefaultPopup';
const rules = {
  math_inline: (node, children) => {
    if (Platform.OS === 'ios') {
      return (
        <View style={{maxWidth: '100%', alignItems: 'flex-start'}}>
          <MathJax
            fontSize={16}
            color={'#78ebfa'}
            style={{maxWidth: '100%'}}
          >
            {node.content}
          </MathJax>
        </View>
      )
    }

    return (
      <ScrollView
        nestedScrollEnabled
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{
          alignItems: Platform.OS === 'ios' ? 'center' : null,
          justifyContent: Platform.OS === 'ios' ? 'center' : null,
          flexGrow: 1,
          paddingBottom: Platform.OS === 'ios' ? -16 : 0
        }}>
        <MathJax
          fontSize={16}
          color={'#78ebfa'}
        >
          {node.content}
        </MathJax>
      </ScrollView>
    )
  },
  math_block: (node, children) => {
    return (
      <ScrollView
        nestedScrollEnabled
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{paddingBottom: 8}}>
        <MathJax
          fontSize={16}
          color={'#78ebfa'}
        >
          {node.content}
        </MathJax>
      </ScrollView>
    )
  },
}

const markdownit = new MarkdownIt({
  typographer: true,
}).use(mathjax3, {
  inlineOpen: '\\(',
  inlineClose: '\\)',
  blockOpen: '\\[',
  blockClose: '\\]',
  tex: {
    inlineMath: [['$', '$']],
    displayMath: [['$$', '$$']],
    processEscapes: true,
  },
  options: {
    output: {
      renderer: 'svg',
    }
  }
})
class ChatItem extends React.PureComponent{
  static componentName = 'ChatItem'

  // static propTypes = {}
  constructor(props){
    super(props);
    this.state = _.merge(this.state,
    {
      menuVisible: false,
      fadeAnim: new Animated.Value(0),
      bubbleLayout: 0,
      heigtMessage: {}
    })
  }
  copyRouter(answer) {
    Clipboard.setString(answer)
      NotifyUtil.pushAlertTopNotify({
        type: 'success',
        content: 'Sao chép thành công',
        timeClose: 2000
      })
    this.setState({ menuVisible: false});
  }
  toggleMenu = (vibrate = true) => {
    const { menuVisible, fadeAnim } = this.state;
    if (vibrate) {
      Vibration.vibrate(50);
    }

    if (!menuVisible) {
      Animated.timing(fadeAnim, { toValue: 1, duration: 200, useNativeDriver: true }).start();
    } else {
      Animated.timing(fadeAnim, { toValue: 0, duration: 200, useNativeDriver: true }).start(() =>
        this.setState({ menuVisible: false })
      );
    }
    this.setState({ menuVisible: !menuVisible });
    this.messageRef.measureInWindow((x, y, width, height) => {
      this.setState({
        bubbleLayout: y
      })
    });
  };

  getRatingChatbot = (rate, item) => {
   const { dispatch } = this.props;
      dispatch(
        IHeyUActions_MiddleWare.ratingChatBot({rate, _id: item._id}),
      ).then(res => {
          this.props.getMessage && this.props.getMessage(undefined, item.conversation)
      });
  }

  render() {
    const {item, index, infos} = this.props;
    let answer = item.answer && item.answer.trim()
    let reasoning = item.reasoning && item.reasoning.trim()
    return (
      <View style={{marginBottom: 10, }}>
        {/* {item.isRenderTime && item.createdAt ? <Include.Text style={{ textAlign: 'center', fontSize: 15.5, paddingBottom: 5, color: '#fff', fontStyle: 'italic'}}>{Util.date2String(new Date(item.createdAt), 'dd/mm  HH:MM')}</Include.Text> : null} */}
        {item.question ? <TouchableOpacity
          ref={(ref) => this.messageRef = ref}
          onLongPress={this.toggleMenu}
          onLayout={(event) => {
            if (index === 0) {
              this.props.onLayoutQuestion && this.props.onLayoutQuestion(event)
            }
            this.setState({ heigtMessage: event.nativeEvent.layout })
          }}
          style={{ flexDirection: 'row', alignItems: 'flex-end', marginBottom: 10 }}>
          <View style={{ width: 100, borderRadius: 20, }}></View>
          <View
            style={{
              flex: 1, padding: 12,
              backgroundColor: '#007cfe',
              borderTopLeftRadius: 16,
              borderTopRightRadius: 16,
              borderBottomLeftRadius: 16,
              marginRight: 12,
            }}>
            <Include.Text style={{ color: '#fff', fontSize: 15.5, fontFamily: Define.constants.fontBold500 }}>{item.question}</Include.Text>
          </View>
        </TouchableOpacity>
          : null}
        <Modal transparent visible={this.state.menuVisible}>
          <Animated.View style={[styles2.overlay, { opacity: this.state.fadeAnim }]} />
          <TouchableOpacity style={styles2.modalContainer} onPress={() => {this.toggleMenu(false)}}>
            <View
              style={{ flexDirection: 'row', alignItems: 'flex-end', marginBottom: 10 }}>
              <View style={{ width: 105, borderRadius: 20, }}></View>
              <View style={{
                flex: 1, padding: 12, backgroundColor: '#007CFE',
                borderTopLeftRadius: 16,
                borderTopRightRadius: 16,
                borderBottomLeftRadius: 16,
                marginRight: 15,
                top: this.state.bubbleLayout
              }}>
                <Include.Text style={{ color: '#fff', fontSize: 15.5, fontFamily: Define.constants.fontBold500 }}>{item.question}</Include.Text>
              </View>
            </View>
            {this.state.heigtMessage && (
              <View style={[
                styles2.menu,
                {
                  position: 'absolute',
                  top: this.state.bubbleLayout + this.state.heigtMessage.height + 8,
                  right: 15,
                  width: 150,
                }
              ]}>
                <TouchableOpacity
                  style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems:'center', backgroundColor:'#fff'}}
                  onPress={() => this.copyRouter(item.question)}>
                  <Text allowFontScaling={false} style={{ color: '#000', fontSize: 15, fontFamily: Define.constants.fontBold400 }}>Sao chép</Text>
                  <PenguinLinearIcon name={'copy'} size={18} color={'#000'} />
                </TouchableOpacity>
              </View>
            )}
          </TouchableOpacity>
        </Modal>
        {item.answer?
        <View>
          <View style={{flexDirection: 'row', alignItems: 'flex-start'}}>
          <Image style={{width: 47, height: 47, borderRadius: 20, alignSelf: 'flex-start', }} source={Define.assets.Images.chatbot}/>
          <View
            style={{
              flex:1,
              paddingHorizontal: 10,
              backgroundColor:'#000',
              borderTopRightRadius: 16,
              borderBottomRightRadius: 16,
              borderBottomLeftRadius: 16,
              marginRight:20,
              paddingVertical: 8,
            }}
          >
            {item.textReasoning ?
              <View
                style={{marginTop: 4, marginHorizontal: 13}}>
                <Text allowFontScaling={false}
                  style={{
                    fontSize: 13,
                    fontFamily: Define.constants.fontBold500,
                    color: '#1589d8',
                  }}>
                  {item.textReasoning}
                </Text>
              </View>
            : null}
            {reasoning ?
            <View
              style={{
                borderLeftWidth: 2,
                borderColor: '#b3b5b67a',
                paddingLeft: 12,
              }}>
              <Markdown
                rules={rules}
                markdownit={markdownit}
                style={styles.markDownDisplayReason}>
                {`${item.reasoning}`}
              </Markdown>
              {/* <Marked
                value={reasoning}
                flatListProps={{
                  style: {
                    backgroundColor: '#000',
                  },
                }}
                styles={styles.markDownReason}
              /> */}
            </View>
            : null}
            {answer ?
              <Markdown
                rules={rules}
                markdownit={markdownit}
                style={styles.markDownDisplayMessage}>
                {answer}
              </Markdown>
              // <Marked
              //   value={answer}
              //   markdownit={markdownit}
              //   flatListProps={{
              //     style: {
              //       backgroundColor: '#000',
              //     },
              //   }}
              //   renderer={renderer}
              //   styles={styles.markDownMessage}
              // />
            : null}
            <View style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
              <TouchableOpacity
                disabled={ item.rate === 1 || item.rate === 0 ? true : false}
                onPress={() => {
                  this.getRatingChatbot(1, item)
                }}
                style={{ alignItems: 'flex-end', zIndex: 10}}>
                {item.rate === 1 ?
                  <PenguinBoldIcon name={'like-1'} size={20} color={'#fff'} />
                  :
                  <PenguinLinearIcon name={'like-1'} size={20} color={'#fff'} />}
              </TouchableOpacity>
              <TouchableOpacity
                disabled={ item.rate === 1 || item.rate === 0 ? true : false}
                onPress={() => {
                  this.getRatingChatbot(0, item)
                }}
                style={{ alignItems: 'flex-end', zIndex: 10, marginLeft: 12}}>
                {item.rate === 0 ?
                  <PenguinBoldIcon name={'dislike'} size={20} color={'#fff'} />
                  :
                  <PenguinLinearIcon name={'dislike'} size={20} color={'#fff'} />}
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  this.copyRouter(item.answer)
                }}
                style={{ alignItems: 'flex-end', zIndex: 10, marginLeft: 12}}>
                <PenguinLinearIcon name={'copy'} size={20} color={'#fff'} />
              </TouchableOpacity>
            </View>
          </View>
        </View>
        </View>
        :null}
        {item.isLoading && !this.props.stopStream && index === infos.length - 1 ?
          <View style={{ flexDirection: 'row', alignItems: 'center', marginRight: 12, height: 47 }}>
            <Image style={{ width: 47, height: 47, borderRadius: 20 }} source={Define.assets.Images.chatbot} />
            <Spinner type={'ThreeBounce'} color={'#0084ff'} size={25} style={{ marginTop: 10 }} />
          </View> : null
        }
      </View>
    )
  }
}
const styles2 = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'flex-end',
    padding: 20,
  },
  bubble: {
    backgroundColor: '#D1E8FF',
    padding: 12,
    borderRadius: 20,
    zIndex: 0, // Đảm bảo tin nhắn luôn trên overlay
  },
  text: {
    fontSize: 16,
    color: '#000',
  },
  menu: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 10,
    width: 150,
    elevation: 5,
    zIndex: 20, // Đảm bảo menu luôn nổi lên
  },
  menuText: {
    fontSize: 16,
    paddingVertical: 10,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.5)',
    zIndex: 0, // Đảm bảo overlay không che tin nhắn
  },
  modalContainer: {
    flex: 1,
  },
});

const styles = StyleSheet.create({
  markDownDisplayMessage: {
    text: {
      color: '#fff',
      fontFamily: Define.constants.fontBold400,
    },
    em: {
      color: '#fff',
    },
    body: {
      fontSize: 15.5,
      color: '#fff',
      fontFamily: Define.constants.fontBold400,
    },
    code_inline: {backgroundColor: '#000', color: '#fff'},
    code_block: {backgroundColor: '#000', color: '#fff'},
    hr: {backgroundColor: '#fff', height: 1, color: '#fff'},
    blockquote: {backgroundColor: '#000'},
    heading1: { color: '#fff', fontSize: 19},
    heading2: { color: '#fff', fontSize: 18.5},
    heading3: { color: '#fff', fontSize: 18},
    heading4: { color: '#fff', fontSize: 17},
    heading5: { color: '#fff', fontSize: 16.5},
    heading6: { color: '#fff', fontSize: 16},
    paragraph: { color: '#fff'},
    ordered_list: { color: '#fff'},
    bullet_list: {color: '#fff'},
    strong: { color: '#fff', fontSize: 15.5},
    link: { color: '#007DFF'},
    strikethrough: { color: '#fff'},
    codespan: { color: '#fff'},
  },
  markDownDisplayReason: {
    text: {
      color: '#b3b5b6',
      fontFamily: Define.constants.fontBold400
    },
    em: {
      color: '#b3b5b6',
    },
    body: {
      fontSize: 13.5,
      color: '#b3b5b6',
      fontFamily: Define.constants.fontBold400,
    },
    code_inline: {backgroundColor: '#000', color: '#b3b5b6'},
    code_block: {backgroundColor: '#000', color: '#b3b5b6'},
    hr: {backgroundColor: '#b3b5b6', height: 1, color: '#b3b5b6'},
    blockquote: {backgroundColor: '#000'},
    heading1: { color: '#b3b5b6', fontSize: 19},
    heading2: { color: '#b3b5b6', fontSize: 18.5},
    heading3: { color: '#b3b5b6', fontSize: 18},
    heading4: { color: '#b3b5b6', fontSize: 17},
    heading5: { color: '#b3b5b6', fontSize: 16.5},
    heading6: { color: '#b3b5b6', fontSize: 16},
    paragraph: { color: '#b3b5b6'},
    ordered_list: { color: '#b3b5b6'},
    bullet_list: {color: '#b3b5b6'},
    strong: { color: '#b3b5b6', fontSize: 15.5},
    link: { color: '#007DFF7a'},
    strikethrough: { color: '#b3b5b6'},
    codespan: { color: '#b3b5b6'},
  },
  markDownMessage: {
    text: {
      color: '#fff',
      fontSize: 15.5,
      fontFamily: Define.constants.fontBold400,
    },
    em: {
      color: '#fff',
    },
    h1: { color: '#fff', fontSize: 19},
    h2: { color: '#fff', fontSize: 18.5},
    h3: { color: '#fff', fontSize: 18},
    h4: { color: '#fff', fontSize: 17},
    h5: { color: '#fff', fontSize: 16.5},
    h6: { color: '#fff', fontSize: 16},
    paragraph: { color: '#fff'},
    li: { color: '#fff'},
    strong: { color: '#fff', fontSize: 15.5},
    link: { color: '#007DFF'},
    strikethrough: { color: '#fff'},
    codespan: { color: '#fff'},
  },
  markDownReason: {
    text: {
      color: '#b3b5b6',
      fontSize: 13.5,
      fontFamily: Define.constants.fontBold400
    },
    em: {
      color: '#b3b5b6',
    },
    h1: { color: '#b3b5b6', fontSize: 19},
    h2: { color: '#b3b5b6', fontSize: 18.5},
    h3: { color: '#b3b5b6', fontSize: 18},
    h4: { color: '#b3b5b6', fontSize: 17},
    h5: { color: '#b3b5b6', fontSize: 16.5},
    h6: { color: '#b3b5b6', fontSize: 16},
    paragraph: { color: '#b3b5b6'},
    li: { color: '#b3b5b6'},
    strong: { color: '#b3b5b6', fontSize: 15.5},
    link: { color: '#007DFF7a'},
    strikethrough: { color: '#b3b5b6'},
    codespan: { color: '#b3b5b6'},
  }
})
/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  return {}
}

export default connect(selectActions, undefined, undefined, {withRef: true})(ChatItem);
// export default ChatItem
