
var _ = require('lodash')
//LIB
import React  from 'react';
import {
  View,
  Image,
  TouchableOpacity,
  FlatList
} from 'react-native';

import { connect } from 'react-redux';

//action

//components
var Define = require('../../../Define');
var Debug = require('../../../Util/Debug');
var Themes = require('../../../Themes');
var Util = require('../../../Util/Util');
var Include = require('../../../Include');


import ReactComponent from '../../ReactComponent'
var ButtonWrap = require('../ButtonWrap');
var facebookUtils = require('../../../Util/facebook');
import {Ionicons as Icon} from '@s/ionicons';
// import WebRTC from '../../popups/WebRTC'
import DefaultPopup from '../../popups/DefaultPopup';
var {popupActions} = require('../../popups/PopupManager');
var {globalVariableManager}= require('../../modules/GlobalVariableManager');

class CallUserList extends ReactComponent{
  static componentName = 'CallUserList'
  // static defaultProps = {}
  // static propTypes = {}
  constructor(props){
    super(props);
    this.state = _.merge(this.state,
    {})
    this.renderItem = this.renderItem.bind(this);
  }
  renderItem({item}) {
    var {user} = this.props;
    return(
      <View style={{flexDirection: 'row', paddingVertical: 10, justifyContent: 'space-around', alignItems: 'center', marginHorizontal:10,borderBottomWidth:0.4, borderBottomColor:'#06C2A5'}}>
        <Image style={{width: 50, height: 50, borderRadius: 25}} source={{uri: item.facebook.picture}} />
        <Include.Text style={{color: '#365899', fontSize: 17, fontWeight: '500',fontStyle: 'italic', marginLeft: 30, flex:1, flexWrap: 'wrap', paddingRight: 3}}>{item.facebook.name}</Include.Text>
        <TouchableOpacity
          style={{width:50, height: 50, alignItems: 'center', justifyContent: 'center', backgroundColor: '#06C2A5', borderRadius: 25,}}
          onPress={() => {
            if(this.props.phone === _.get(user, 'memberInfo.member.phone', '')) {
              popupActions.setRenderContentAndShow(
                DefaultPopup,
                {
                  title:'Lưu ý',
                  description:`Bạn không thể tự gọi cho mình`,
                  buttonTitle:'Xong',
                  onPress:() => {popupActions.popPopup()}
                }
              );
            }else {
              popupActions.popPopup();
              popupActions.setRenderContentAndShow(WebRTC, {
                userId: item._id,
                userInf: item,
                video: false,
                caller: true
              })
            }
          }}>
            <Icon style={{color:'#fff', fontSize: 16}} name={'call'}/>
        </TouchableOpacity>
        {this.props.canCallVideo ?
          <TouchableOpacity
            style={{width:50, height: 50, alignItems: 'center', justifyContent: 'center', backgroundColor: '#0984e3', borderRadius: 25, marginLeft: 10}}
            onPress={() => {
              if(this.props.phone === _.get(user, 'memberInfo.member.phone', '')) {
                popupActions.setRenderContentAndShow(
                  DefaultPopup,
                  {
                    title:'Lưu ý',
                    description:`Bạn không thể tự gọi cho mình`,
                    buttonTitle:'Xong',
                    onPress:() => {popupActions.popPopup()}
                  }
                );
              }else {
                popupActions.popPopup();
                popupActions.setRenderContentAndShow(WebRTC, {
                  userId: item._id,
                  userInf: item,
                  video: true,
                  caller: true
                })
              }
            }}>
              <Icon style={{color:'#fff', fontSize: 16}} name={'videocam'}/>
          </TouchableOpacity>
        :null}
      </View>
    );
  }
  rowHasChanged(r1, r2) {
    return true
  }
  renderContent(){
    var content = null;
    content=(
      <FlatList
        containerStyle={{
          justifyContent: 'space-around',
          flexDirection: 'column',
        }}
        scrollEnabled={false}
        data={this.props.infos}
        renderItem={this.renderItem}
      />
    )
    return content;
  }
}

/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
 function selectActions(state) {
   return {
     user: state.User,
   }
 }

 export default connect(selectActions, undefined, undefined, {withRef: true})(CallUserList);
//export default CallUserList
