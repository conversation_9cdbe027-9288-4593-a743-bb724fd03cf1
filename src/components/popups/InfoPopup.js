var _ = require('lodash')
//LIB
import React  from 'react';
import {
  View,
  ScrollView,
  Image,
  Platform,
  Keyboard,
  TouchableOpacity,
  Text
} from 'react-native';

var Define = require('../../Define');
var Themes = require('../../Themes');
var Include = require('../../Include');

import LinearGradient from 'react-native-linear-gradient';

import Popup from './Popup';
var {popupActions,popupConst} = require('../popups/PopupManager');

class InfoPopup extends Popup{
  static componentName = 'InfoPopup'
  static config = {
    ...Popup.config,
    noDetectGes: true,
    movePopupIn:(contentObject)=>{
      var contentRefObject =  contentObject.objRef;
      return contentRefObject.fadeInUp(100);
    },
    movePopupOut:(contentObject)=>{
      var contentRefObject =  contentObject.objRef;
      return contentRefObject.fadeOutDown(200);
    }
  }
  static containerStyle = {
    ...Popup.containerStyle,
    backgroundColor: 'rgba(0,0,0,0.4)',
    justifyContent: 'flex-end',
    alignItems: 'center',
    alignSelf: 'stretch'
  }

  constructor(props) {
    super(props);
    this.state = _.merge(this.state, {

    });

    this.keyboardWillShow = this.keyboardWillShow.bind(this);
    this.keyboardWillHide = this.keyboardWillHide.bind(this);
  }

  renderPopupContent(){
    let {image, message, title, titleWeight, tintColor, buttonColor, textColor} = this.props
    return(
      <View style={{
        backgroundColor: 'rgba(0,0,0,0)',
        justifyContent: 'flex-end',
        alignItems: 'center',
        alignSelf: 'stretch',
        width: Define.constants.widthScreen,
        height: Define.constants.heightScreen
      }}>
        <TouchableOpacity
          onPress={() => {popupActions.popPopup()}}
          style={{
            flex: 1,
            width: Define.constants.widthScreen,
            backgroundColor: 'transparent'
          }}
        />
        <View style={{
          width: Define.constants.widthScreen,
          backgroundColor: '#fff',
          borderTopLeftRadius: 12,
          borderTopRightRadius: 12,
          bottom: this.state.bottom,
          maxHeight: Define.constants.heightScreen * 0.7
        }}>
          <View style={{justifyContent: 'center', alignItems: 'center', borderBottomWidth: 2, borderColor: '#E2E2E2', width: '100%', padding: 16, flexDirection: 'row'}}>
            <Text allowFontScaling={false} style={{fontSize: 20, fontFamily: Define.constants.fontBold600, textAlign: 'center', color: '#012548'}}>
                {title}
            </Text>
          </View>
          <ScrollView showsVerticalScrollIndicator={false}>
            <View style={{ flexDirection: 'row', alignItems: 'center', margin: 16}}>
              {image ?
                tintColor ?
                <Image source={{ uri: image }} style={{ width: 36, height: 36, backgroundColor: 'transparent', tintColor:tintColor}} tintColor={tintColor}/>
                :
                <Image source={{ uri: image }} style={{ width: 36, height: 36, backgroundColor: 'transparent',}} />
                : null}
              <Text allowFontScaling={false} style={{ flex: 1, fontSize: 16, color: '#143250', fontWeight: '400', marginHorizontal: 16 }}>
              {message}
              </Text>
            </View>
          </ScrollView>

          <View style={{ borderRadius: 6, width: Define.constants.widthScreen, paddingHorizontal: 16, marginBottom: 16 }}>
              <TouchableOpacity
                style={{ paddingVertical: 16, borderRadius: 16, alignItems: 'center', justifyContent: 'center', backgroundColor: buttonColor ? buttonColor : '#1589D8' }}
                onPress={() => {
                  popupActions.popPopup();

                  setTimeout(() => {
                    this.props.onPress && this.props.onPress()
                  }, 200)
                }}>
                <Text allowFontScaling={false} style={{ backgroundColor: 'transparent', color: textColor ? textColor : '#fff', fontSize: 18, fontFamily: Define.constants.fontBold600 }}>
                  {this.props.textButton ? this.props.textButton : 'Đóng'}
                </Text>
              </TouchableOpacity>
          </View>
        </View>
      </View>
    )
  }

  keyboardWillShow (e) {
    const heightKeyboard = e.endCoordinates.height;
    if (Platform.OS === 'ios') {
      this.setState({
        bottom: heightKeyboard
      });
    }else{
      // for Android
    }
  }

  keyboardWillHide (e) {
    if (Platform.OS === 'ios') {
      this.setState({
        bottom: 0
      });
    }else{
      // for Android
    }
  }

  UNSAFE_componentWillMount() {
    super.UNSAFE_componentWillMount();

    if (Platform.OS === 'android') {
      this.keyboardDidShowSubscription = Keyboard.addListener('keyboardDidShow', this.keyboardWillShow)
      this.keyboardDidHideSubscription = Keyboard.addListener('keyboardDidHide', this.keyboardWillHide)
    } else {
      this.keyboardDidShowSubscription = Keyboard.addListener('keyboardWillShow', this.keyboardWillShow)
      this.keyboardDidHideSubscription = Keyboard.addListener('keyboardWillHide', this.keyboardWillHide)
    }
  }

  componentDidMount(){
    super.componentDidMount();
  }
}

/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
// function selectActions(state) {
//   return {}
// }

// export default connect(selectActions, undefined, undefined, {withRef: true})(DefaultPopup);
export default InfoPopup