var _ = require('lodash');
//LIB
import React from 'react';
import {
  View,
  Image,
  TouchableOpacity,
  Text,
  // InteractionManager
} from 'react-native';

import {connect} from 'react-redux';

//action

//components
var Define = require('../../Define');
// var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
// var Util = require('../../Util/Util');
var Include = require('../../Include');

var StyleConfig = require('../../Themes/StyleConfig');
var RectButton = require('../elements/RectButton');
var ButtonWrap = require('../elements/ButtonWrap');
import HeyUIcon from '../elements/HeyUIcon/HeyUIcon';
import Popup from './Popup';
import PenguinBoldIcon from '../elements/PenguinBoldIcon/PenguinBoldIcon';
var {popupActions, popupConst} = require('../popups/PopupManager');
// var {globalVariableManager}= require('../modules/GlobalVariableManager');

class DefaultInfoPopup extends Popup {
  static componentName = 'DefaultInfoPopup';
  static config = {
    ...Popup.config,
    group: popupConst.POPUP_GROUP,
    tapToExit: true,
    videoMotion: false,
    // movePopupIn:()=>{return new Promise((resolve)=>{resolve()});},
    // movePopupOut:()=>{return new Promise((resolve)=>{resolve()});},
  };
  static containerStyle = {
    ...Popup.containerStyle,
    flexDirection: 'column',
    justifyContent: 'center',
  };
  static defaultProps = {
    disableClose: true,
  };
  // static propTypes = {}
  constructor(props) {
    super(props);
    this.state = _.merge(this.state, {
      dontAsk: false
    });
  }
  UNSAFE_componentWillMount() {
    super.UNSAFE_componentWillMount();
    if (this.props.onWillMount) {
      this.props.onWillMount();
    }
  }
  renderPopupContent() {
    var self = this;
    const {
      disableClose,
      title,
      description,
      description2,
      buttonTitle,
      onPress,
      buttonTitle2,
      onPress2,
      onPressPopup,
      imageUrl,
      info,
      onPressInfo,
      dontAskAgain,
      onPressDontAsk,
    } = self.props;
    var descriptionText = null;
    if (description) {
      descriptionText = (
        <View style={{width: '100%', paddingTop: 10}}>
          <Text allowFontScaling={false}
            style={{
              fontSize: 14,
              fontFamily: Define.constants.fontBold400,
              color: '#012548',
            }}>
            {description}
          </Text>
        </View>
      );
    }
    var dontAskBox = null;
    if (dontAskAgain) {
      dontAskBox = (
        <View
          style={{flexDirection: 'row', alignItems: 'center', paddingTop: 12}}>
          <TouchableOpacity
            onPress={() => {
              this.setState({dontAsk: !this.state.dontAsk});
            }}>
            {this.state.dontAsk ?
              <HeyUIcon name={'fi-sr-checkbox'} size={20} color={'#1589D8'} />
              :
              <HeyUIcon name={'fi-rr-square'} size={20} color={'#929394'} />
            }
          </TouchableOpacity>
          <Text allowFontScaling={false}
            style={{
              fontSize: 14,
              fontFamily: Define.constants.fontBold400,
              color: '#929394',
              marginLeft: 8,
            }}>
            {dontAskAgain}
          </Text>
        </View>
      );
    }
    var infoBox = null;
    if (info) {
      infoBox = (
        <View
          style={{
            backgroundColor: '#E3F2FF',
            borderRadius: 16,
            paddingVertical: 8,
            paddingHorizontal: 12,
            marginTop: 12,
          }}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
            }}>
            <PenguinBoldIcon name={'info-circle'} size={16} color={'#376FFF'} />
            <Text allowFontScaling={false}
              style={{
                flex: 1,
                marginLeft: 8,
                fontSize: 12,
                fontFamily: Define.constants.fontBold400,
                color: '#012548',
              }}>
              {info}
            </Text>
          </View>
          <View style={{marginLeft: 23}}>
            {onPressInfo ? (
              <TouchableOpacity
                onPress={() => {
                  onPressInfo && onPressInfo();
                }}>
                <Text allowFontScaling={false}
                  style={{
                    fontSize: 12,
                    fontFamily: Define.constants.fontBold600,
                    color: '#376FFF',
                    marginTop: 4,
                    textDecorationLine: 'underline',
                  }}>
                  Tìm hiểu thêm
                </Text>
              </TouchableOpacity>
            ) : null}
          </View>
        </View>
      );
    }
    var description2Text = null;
    if (description2) {
      description2Text = (
        <Include.Text
          style={{
            fontSize: 16,
            fontFamily: Define.constants.fontBold400,
            color: '#131313',
          }}>
          {description2}
        </Include.Text>
      );
    }
    var titleComponent = null;
    if (title) {
      titleComponent = (
        <View
          style={{
            alignItems: 'center',
            justifyContent: 'center',
            borderBottomWidth: 1,
            borderColor: '#eef0f0',
            paddingBottom: 16,
          }}>
          <Text allowFontScaling={false}
            style={{
              fontSize: 16,
              fontFamily: Define.constants.fontBold600,
              color: '#012548',
            }}>
            {title}
          </Text>
          {closeButton}
        </View>
      );
    }
    var closeButton = null;
    if (!disableClose) {
      closeButton = (
        <ButtonWrap
          onPress={() => {
            popupActions.popPopup(undefined, undefined, undefined, undefined, [
              popupConst.INFO_GROUP,
            ]);
          }}>
          <View
            style={{
              position: 'absolute',
              top: 0,
              bottom: 0,
              left: 0,
              right: 0,
              justifyContent: 'flex-end',
              padding: 3,
            }}>
            <Image
              tintColor={'#000'}
              style={Themes.current.image.closeIcon}
              source={Define.assets.Home.close}
            />
          </View>
        </ButtonWrap>
      );
    }
    var button = null;
    if (buttonTitle) {
      button = (
        <TouchableOpacity
          activeOpacity={0.6}
          style={{flex: 1}}
          onPress={() => {
            onPress();
          }}>
          <View
            style={{
              flexDirection: 'row',
              backgroundColor: '#1589D8',
              height: 40,
              borderRadius: 10,
              alignItems: 'center',
              justifyContent: 'center',
              marginLeft: 6,
            }}>
            <Text allowFontScaling={false}
              style={{
                fontSize: 16,
                fontFamily: Define.constants.fontBold600,
                color: '#fff',
              }}>
              {buttonTitle}
            </Text>
          </View>
        </TouchableOpacity>
      );
    }
    var button2 = null;
    if (buttonTitle2) {
      button2 = (
        <TouchableOpacity
          activeOpacity={0.6}
          style={{flex: 1}}
          onPress={() => {
            onPress2();
            if (onPressDontAsk) {
              onPressDontAsk && onPressDontAsk(this.state.dontAsk);
            }
          }}>
          <View
            style={{
              backgroundColor: '#F5F6FA',
              borderRadius: 10,
              alignItems: 'center',
              justifyContent: 'center',
              height: 40,
              marginRight: 6,
            }}>
            <Text allowFontScaling={false}
              style={{
                fontSize: 16,
                fontFamily: Define.constants.fontBold600,
                color: '#012548',
              }}>
              {buttonTitle2}
            </Text>
          </View>
        </TouchableOpacity>
      );
    }

    let image = null;
    if (imageUrl) {
      image = (
        <View
          style={{
            height: Define.constants.heightScreen * 0.4,
            alignSelf: 'center',
            marginTop: 4,
          }}>
          <Image
            style={{
              flex: 1,
              width: Define.constants.widthScreen * 0.8,
              height: null,
              resizeMode: 'contain',
            }}
            source={{uri: imageUrl}}
          />
        </View>
      );
    }

    if (onPressPopup && !(buttonTitle && buttonTitle2)) {
      return (
        <ButtonWrap
          onPress={() => {
            onPressPopup();
          }}>
          <View
            style={{
              backgroundColor: '#fff',
              paddingVertical: 16,
              width: Define.constants.widthScreen * 0.92,
              borderRadius: 16,
            }}>
            {titleComponent}
            <View
              style={{
                paddingHorizontal: 16,
              }}>
              {descriptionText}
              {description2Text}
              {infoBox}
              {dontAskBox}
              {self.props.children}
              {image}
              {button && button2 ? (
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-around',
                    marginTop: 16,
                  }}>
                  {button2}
                  {button}
                </View>
              ) : (
                <View style={{flexDirection: 'row', marginTop: 16}}>
                  {button}
                </View>
              )}
            </View>
          </View>
        </ButtonWrap>
      );
    } else {
      return (
        <View
          style={{
            backgroundColor: '#fff',
            paddingVertical: 16,
            width: Define.constants.widthScreen * 0.92,
            borderRadius: 16,
          }}>
          {titleComponent}
          <View
            style={{
              paddingHorizontal: 16,
            }}>
            {descriptionText}
            {description2Text}
            {infoBox}
            {dontAskBox}
            {self.props.children}
            {image}
            {button && button2 ? (
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-around',
                  marginTop: 16,
                }}>
                {button2}
                {button}
              </View>
            ) : (
              <View
                style={{
                  flexDirection: 'row',
                  marginTop: 16,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                {button}
              </View>
            )}
          </View>
        </View>
      );
    }
  }
  componentWillUnmount() {
    super.componentWillUnmount();
    if (this.props.onWillUnmount) {
      this.props.onWillUnmount();
    }
  }
}

/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  return {
    user: state.User,
  };
}

export default connect(selectActions, undefined, undefined, {withRef: true})(
  DefaultInfoPopup,
);
// export default DefaultInfoPopup;
