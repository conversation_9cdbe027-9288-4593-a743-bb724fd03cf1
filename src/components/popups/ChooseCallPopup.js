
var _ = require('lodash')
//LIB
import React  from 'react';
import {
  View,
  InteractionManager,
  Text,
  Platform,
  Linking,
  PermissionsAndroid,
  NativeModules,
  TouchableOpacity,
  Image
} from 'react-native';

import {Ionicons as Icon} from '@react-native-vector-icons/ionicons';
import LinearGradient from 'react-native-linear-gradient';
var RNIntent = NativeModules.RNIntent;

// import { connect } from 'react-redux';

//action

//components
var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
var Util = require('../../Util/Util');
var Include = require('../../Include');

var ButtonWrap= require('../elements/ButtonWrap');

import Popup from './Popup'
import WebRTC from './WebRTC'

var {popupActions,popupConst} = require('./PopupManager');
var {globalVariableManager}= require('../modules/GlobalVariableManager');

import DefaultPopup from './DefaultPopup';

class ChooseCallPopup extends Popup{
  static componentName = 'ChooseCallPopup'
  static config = {
    ...Popup.config,
    movePopupIn:(contentObject) => {
      var contentRefObject =  contentObject.objRef;
      return contentRefObject.fadeInUp(200);
    },
    movePopupOut:(contentObject) => {
      var contentRefObject =  contentObject.objRef;
      return contentRefObject.fadeOutDown(300);
    }
  }
  static containerStyle={
    ...Popup.containerStyle,
  }
  static defaultProps = {
    disableClose:true,
  }
  // static defaultProps = {}
  // static propTypes = {}
  constructor(props){
    super(props);
    this.state = _.merge(this.state,
    {})
  }
  checkAndRequestPermission = async (phone) => {
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.CALL_PHONE,
      );
      if (granted === PermissionsAndroid.RESULTS.GRANTED) {
        RNIntent.makeCall(`${phone}`, true)
      } else {
        popupActions.setRenderContentAndShow(DefaultPopup,{
          title:'Thông báo',
          description:'Bạn hãy cho phép HeyU quyền gọi điện trong (Cài đặt -> Quyền -> Điện thoại -> Cho phép) để có thể tạo cuộc gọi nhanh',
          buttonTitle2:'Bỏ qua',
          buttonTitle:'Đến cài đặt',
          onPress2:() => {popupActions.popPopup()},
          onPress:() => {
            Linking.openSettings()
            popupActions.popPopup()
          }
        })
      }
    } catch (err) {
      console.warn(err);
    }
  };
  onPressCallNormal(phone) {
    var url = `tel:${phone}`;
    if(Platform.OS === 'android') {
      this.checkAndRequestPermission(phone)
    } else {
      url = `telprompt:${phone}`;
      Linking
        .canOpenURL(url)
        .then((supported) => {
          if(supported) {
            return Linking.openURL(url);
          }

          return Promise.reject();
        })
        .catch((err) => {
          Debug.log(err,Debug.level.ERROR)
        })
    }
  }

  renderFreeCall = () => {
    const {feedInf, trackCall, userId, userInf,} = this.props;
    const TYPE_CALL = {
      CALL_FREE: 0,
      CALL_NORMAL: 1
    }

    if (!globalVariableManager.reduxManager.state.FeedSystem.configOrderSystem.isOpenCall || (globalVariableManager.reduxManager.state.AppSetting.mode === 'shipper' && feedInf.phone !== feedInf.shop.phone) || (!globalVariableManager.reduxManager.state.FeedSystem.configOrderSystem.isOpenCallWeb && feedInf.platform === 'web')) {
      return;
    }

    return (
      <TouchableOpacity
        onPress={() => {
          trackCall(feedInf, TYPE_CALL.CALL_FREE);
          popupActions.popPopup();
          popupActions.setRenderContentAndShow(WebRTC, {
            userId, userInf, caller: true, callToApp: this.props.callToApp
          })
        }}
      >
        <LinearGradient start={{ x: 0, y: 0 }} end={{ x: 1, y: 0 }} colors={['#2193b0', '#6dd5ed']} style={{ height: 60, alignItems: 'center', justifyContent: 'center', flex: 1, borderRadius: 15, flexDirection: 'row', paddingHorizontal: 15 }}>
          <View
            style={{ width: 50, alignItems: 'center', position: 'absolute', top: 0, left: 0, bottom: 0, justifyContent: 'center' }}>
            <Image source={{ uri: 'https://media.heyu.asia/uploads/mobiles/2019-10-12-heyu-icon.png' }} style={{ width: 20, height: 20, resizeMode: 'contain', flex: 1 }} />
          </View>
          <Text allowFontScaling={false} style={{ color: '#fff', fontSize: 18, textAlign: 'center', fontWeight: '700', flex: 9 }}>Gọi miễn phí</Text>
        </LinearGradient>
      </TouchableOpacity>
    )
  }

  renderContent(){
    var content = null;
    const {feedInf, userId, userInf, trackCall} = this.props;
    const TYPE_CALL = {
      CALL_FREE: 0,
      CALL_NORMAL: 1
    }
    content = (
      <View style={{backgroundColor: '#ffffff', borderRadius: 5, position: 'absolute', bottom: this.props.bottomHeight ? this.props.bottomHeight + 20 : 20, width: '100%', paddingHorizontal: 20, backgroundColor: 'rgba(0, 0, 0, 0.01)'}}>
        {this.renderFreeCall()}
        {Define.constants.serviceErrand.includes(_.get(feedInf, 'orderType.service', '')) && globalVariableManager.reduxManager.state.AppSetting.mode === 'shipper' ?
        <View>
          {feedInf.phone ?
            <TouchableOpacity
              onPress={() => {
                trackCall(feedInf, TYPE_CALL.CALL_NORMAL);
                popupActions.popPopup();
                if(this.props.changePromptCallingNormal) {
                  this.props.changePromptCallingNormal(true);
                }
                this.onPressCallNormal(feedInf.phone)
              }}
            >
                <LinearGradient start={{x: 0, y: 0}} end={{x: 1, y: 0}} colors={['#33ccff', '#ff99cc']} style={{height: 60, marginTop: 10, alignItems: 'center', justifyContent: 'center', backgroundColor: '#42b883', flex: 1, borderRadius: 15, flexDirection: 'row', paddingHorizontal: 15}}>
                  <View
                    style={{width: 50, alignItems: 'center', position: 'absolute', top: 0, left: 0, bottom: 0, justifyContent: 'center'}}>
                    <Icon name='call' size={22} style={{color: '#fff', backgroundColor: 'transparent'}}/>
                  </View>
                  <Text allowFontScaling={false} style={{color: '#fff', flex: 8.5, fontSize: 18, textAlign: 'center', fontWeight: '700', backgroundColor: 'transparent'}}>Gọi người đưa đồ</Text>
                </LinearGradient>
            </TouchableOpacity>
          :null}
          <TouchableOpacity
            onPress={() => {
              trackCall(feedInf, TYPE_CALL.CALL_NORMAL);
              popupActions.popPopup();
              if(this.props.changePromptCallingNormal) {
                this.props.changePromptCallingNormal(true);
              }
              this.onPressCallNormal(feedInf.shop.phone)
            }}
          >
              <LinearGradient start={{x: 0, y: 0}} end={{x: 1, y: 0}} colors={['#56ab2f', '#a8e063']} style={{height: 60, marginTop: 10, alignItems: 'center', justifyContent: 'center', backgroundColor: '#42b883', flex: 1, borderRadius: 15, flexDirection: 'row', paddingHorizontal: 15}}>
                <View
                  style={{width: 50, alignItems: 'center', position: 'absolute', top: 0, left: 0, bottom: 0, justifyContent: 'center'}}>
                  <Icon name='call' size={22} style={{color: '#fff', backgroundColor: 'transparent'}}/>
                </View>
                <Text allowFontScaling={false} style={{color: '#fff', flex: 8.5, fontSize: 18, textAlign: 'center', fontWeight: '700', backgroundColor: 'transparent'}}>{feedInf?.cartStore ? 'Gọi người mua hàng' : 'Gọi người nhận hàng'}</Text>
              </LinearGradient>
          </TouchableOpacity>
        </View>
        : userInf.phone ?
        <TouchableOpacity
          onPress={() => {
            trackCall(feedInf, TYPE_CALL.CALL_NORMAL);
            popupActions.popPopup();
            if(this.props.changePromptCallingNormal) {
              this.props.changePromptCallingNormal(true);
            }
            this.onPressCallNormal(userInf.phone)
          }}
        >
            <LinearGradient start={{x: 0, y: 0}} end={{x: 1, y: 0}} colors={['#56ab2f', '#a8e063']} style={{height: 60, marginTop: 10, alignItems: 'center', justifyContent: 'center', backgroundColor: '#42b883', flex: 1, borderRadius: 15, flexDirection: 'row', paddingHorizontal: 15}}>
              <View
                style={{width: 50, alignItems: 'center', position: 'absolute', top: 0, left: 0, bottom: 0, justifyContent: 'center'}}>
                <Icon name='call' size={22} style={{color: '#fff', backgroundColor: 'transparent'}}/>
              </View>
              <Text allowFontScaling={false} style={{color: '#fff', flex: 8.5, fontSize: 18, textAlign: 'center', fontWeight: '700', backgroundColor: 'transparent'}}>Gọi {userInf.phone.slice(0,7)}...</Text>
            </LinearGradient>
        </TouchableOpacity> : null}

      </View>
    )
    return(content)
  }
}

// NOTE : TODO :now can't use connect ( popupManager can't read config)
/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
// function selectActions(state) {
//   return {}
// }

// export default connect(selectActions, undefined, undefined, {withRef: true})(ChooseCallPopup);

export default ChooseCallPopup
