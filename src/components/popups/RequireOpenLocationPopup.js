
var _ = require('lodash')
//LIB
import React  from 'react';
import {
  View,
  InteractionManager,
  Linking,
  Platform,
  NativeModules
} from 'react-native';

var RectButton = require('../elements/RectButton');
import { connect } from 'react-redux';
var {Actions} = require('react-native-router-flux');

var RNIntent = NativeModules.RNIntent;

//action
import UserActions_MiddleWare from '../../actions/UserActions_MiddleWare'
import AppStateActions_MiddleWare from '../../actions/AppStateActions_MiddleWare'
const BackgroundLocationManager = require('../modules/BackgroundLocationManager');

//components
var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
var Util = require('../../Util/Util');
var facebookUtils = require('../../Util/facebook');
var Include = require('../../Include');
import DefaultPopup from './DefaultPopup'
var ButtonWrap= require('../elements/ButtonWrap');

import Popup from './Popup'

var {popupActions,popupConst} = require('./PopupManager');
var {globalVariableManager}= require('../modules/GlobalVariableManager');

class RequireOpenLocationPopup extends Popup{
  static componentName = 'RequireOpenLocationPopup'
  static config=
  {
    ...Popup.config,
    tapToExit: true
  }
  static containerStyle={
    ...Popup.containerStyle,
  }
  static defaultProps = {
    disableClose:true,
  }
  // static defaultProps = {}
  // static propTypes = {}
  constructor(props){
    super(props);
    this.state = _.merge(this.state,
    {})
    this.onPressSetting= this.onPressSetting.bind(this);
  }
  onPressSetting(){
    var self = this;
    popupActions.popPopup();
    BackgroundLocationManager.showAppSettings();
  }
  renderContent(){
    var content = null;
    var {dispatch} = this.props;
    var self = this;
    let title;
    if(Platform.OS === 'ios') {
      title = this.props.title || `Bạn cần phải bật dịch vụ định vị để sử dụng ứng dụng. Có thể bạn đã tắt nó trong mục "Cài Đặt -> Quyền riêng tư -> Dịch vụ định vị" hoặc đã không cho phép ứng dụng HeyU quyền truy cập vị trí. Liên hệ 1900.633.689 để được hỗ trợ. Xin cảm ơn.`
    } else {
      title = this.props.title || `Bạn cần phải bật dịch vụ định vị để sử dụng ứng dụng. hoặc đã không cho phép ứng dụng HeyU quyền truy cập vị trí. Liên hệ 1900.633.689 để được hỗ trợ. Xin cảm ơn.`
    }

    content = (
      <View
        style={{flex: 1, alignItems: 'center', justifyContent: 'center'}}>
        <View
          style={{backgroundColor: '#ffffff', marginHorizontal: 20, padding: 5, borderRadius: 5, alignItems: 'center'}}>
          <Include.Text style={{textAlign: 'center'}}>{title}</Include.Text>
          <View style={{ flexDirection: 'row' }}>
            <RectButton
              onPress={() => { popupActions.popPopup() }}
              text={'Bỏ qua'}
              contentStyle={{ alignSelf: 'center' }}
              textStyle={{ alignSelf: 'center', marginLeft: 0, fontSize: 14, fontWeight: 'bold' }}
              style={Themes.current.component.normalBlueButton}
            />
            <RectButton
              onPress={self.onPressSetting}
              text={Platform.OS === 'ios' ? 'OK' : 'Cài đặt'}
              contentStyle={{ alignSelf: 'center' }}
              textStyle={{ alignSelf: 'center', marginLeft: 0, fontSize: 14, fontWeight: 'bold' }}
              style={Themes.current.component.normalRedButton}
            />
          </View>
        </View>
      </View>
    )


    return(content)
  }
}

// NOTE : TODO :now can't use connect ( popupManager can't read config)
/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  return {}
}

export default connect(selectActions, undefined, undefined, {withRef: true})(RequireOpenLocationPopup);

// export default RequireOpenLocationPopup
