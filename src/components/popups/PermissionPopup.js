var _ = require('lodash');
//LIB
import React from 'react';
import {View, TouchableOpacity, Image, Keyboard, Platform} from 'react-native';

import {connect} from 'react-redux';

//action

//components
var Define = require('../../Define');
var Include = require('../../Include');

var {Actions} = require('react-native-router-flux');
import Popup from './Popup';
import RDActions from '../../actions/RDActions';

var {popupActions, popupConst} = require('./PopupManager');

class PermissionPopup extends Popup {
  static componentName = 'PermissionPopup';
  static config = {
    ...Popup.config,
    group: popupConst.POPUP_GROUP,
    movePopupIn: contentObject => {
      var contentRefObject = contentObject.objRef;
      return contentRefObject.fadeInUp(200);
    },
    movePopupOut: contentObject => {
      var contentRefObject = contentObject.objRef;
      return contentRefObject.fadeOutDown(300);
    },
    tapToExit: false,
  };
  static containerStyle = {
    ...Popup.containerStyle,
  };
  static defaultProps = {
    disableClose: true,
  };
  // static defaultProps = {}
  // static propTypes = {}
  constructor(props) {
    super(props);
    this.state = _.merge(this.state, {
      bottom: this.props.bottom ? this.props.bottom : 0
    });
    this.keyboardWillHide = this.keyboardWillHide.bind(this)
    this.keyboardWillShow = this.keyboardWillShow.bind(this)
  }
  renderContent() {
    var content = null;
    const {title, detail, textButton, image, onPress, dispatch, subTitle} =
      this.props;
    content = (
      <View style={{
        backgroundColor: 'rgba(0,0,0,0.4)',
        justifyContent: 'flex-end',
        alignItems: 'center',
        alignSelf: 'stretch',
        width: Define.constants.widthScreen,
        height: Platform.OS === 'ios' ? Define.constants.heightScreen : '100%',
        position: 'absolute',
        bottom: this.state.bottom
      }}>
        <TouchableOpacity
          onPress={() => {
            popupActions.popPopup();
            this.props.checkSafeAreaView?.()
            if (Platform.OS === 'ios' && !this.props.appSetting.openedPopupNoti) {
              this.props.dispatch(RDActions.AppSetting.checkPopupPermissionNotify(1))
            }
            this.props.onPressDeny && this.props.onPressDeny()
          }}
          style={{
            flex: 1,
            width: Define.constants.widthScreen,
            backgroundColor: 'transparent',
          }}
        />
        <View style={{ bottom: 0, backgroundColor: '#fff', borderTopLeftRadius: 16, borderTopRightRadius: 16, width: Define.constants.widthScreen, position: 'absolute',paddingBottom: (Platform.OS === 'android' || this.state.bottom) ? 30 : 0}}>
        <View style={{ height: 64, alignItems: 'center', justifyContent: 'center', borderBottomWidth: 2, borderBottomColor: '#E2E2E2' }}>
          <Include.Text style={{ fontSize: 18, fontFamily: Define.constants.fontBold500, color: '#161616', textAlign: 'center' }}>{title}</Include.Text>
          {subTitle ? <Include.Text style={{ fontSize: 18, fontFamily: Define.constants.fontBold500, color: '#161616', textAlign: 'center' }}>{subTitle}</Include.Text> : null}
        </View>
        <View style={{ flexDirection: 'row', paddingHorizontal: 16, paddingVertical: 12 }}>
          <View style={{ marginRight: 16, flex: 1 }}>
            <Include.Text style={{ fontSize: 16,fontFamily: Define.constants.fontBold500, color: '#161616' }}>{detail}</Include.Text>
          </View>
          <Image
            source={{ uri: image }}
            style={{
              width: 72,
              height: 72,
            }}
            resizeMode={'contain'}
          />
        </View>
        <TouchableOpacity style={{ marginHorizontal: 16, backgroundColor: '#0473CD', borderRadius: 6, height: 56, justifyContent: 'center', alignItems: 'center'}} activeOpacity={0.6}
            onPress={() => {
              this.props.checkSafeAreaView?.()
              if (this.props?.type) {
                onPress(this.props.type);
              } else {
                onPress();
              }
              if (Platform.OS === 'ios' && !this.props.appSetting.openedPopupNoti) {
                this.props.dispatch(RDActions.AppSetting.checkPopupPermissionNotify(1))
              }
              popupActions.popPopup();
            }}>
          <Include.Text style={{ fontSize: 18, fontFamily: Define.constants.fontBold500, color: '#FFFFFF' }}>{textButton}</Include.Text>
        </TouchableOpacity>
        <TouchableOpacity style={{ justifyContent: 'center', alignItems: 'center', paddingTop: 24, paddingBottom: 10}} activeOpacity={0.6}
          onPress={() => {
            popupActions.popPopup()
            this.props.checkSafeAreaView?.()
            if (Platform.OS === 'ios' && !this.props.appSetting.openedPopupNoti) {
              this.props.dispatch(RDActions.AppSetting.checkPopupPermissionNotify(1))
            }
            this.props.onPressDeny && this.props.onPressDeny()
          }} >
          <Include.Text style={{ fontSize: 18, fontFamily: Define.constants.fontBold500, color: '#0473CD' }}>Để sau</Include.Text>
        </TouchableOpacity>
      </View>
      </View>
    );
    return content;
  }

  componentDidMount() {
    super.componentDidMount();
    if (Platform.OS === 'android') {
      this.keyboardDidShowSubscription = Keyboard.addListener('keyboardDidShow', this.keyboardWillShow)
      this.keyboardDidHideSubscription = Keyboard.addListener('keyboardDidHide', this.keyboardWillHide)
    } else {
      this.keyboardDidShowSubscription = Keyboard.addListener('keyboardWillShow', this.keyboardWillShow)
      this.keyboardDidHideSubscription = Keyboard.addListener('keyboardWillHide', this.keyboardWillHide)
    }
  }

  componentWillUnmount() {
    super.componentWillUnmount();
    this.keyboardDidShowSubscription.remove()
    this.keyboardDidHideSubscription.remove()

  }

  keyboardWillShow(e) {
    const heightKeyboard = e.endCoordinates.height;
    if (Platform.OS === 'ios') {
      this.setState({
        bottom: heightKeyboard
      });
    } else {
    }
  }

  keyboardWillHide() {
    this.setState({
      bottom: 0
    });
  }
}

// NOTE : TODO :now can't use connect ( popupManager can't read config)
/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  return {
    appSetting: state.AppSetting,
  };
}

export default connect(selectActions, undefined, undefined, {withRef: true})(
  PermissionPopup,
);

// export default PermissionPopup