var _ = require('lodash');
//LIB
import React from 'react';
import {
  View,
  InteractionManager,
  Text,
  TouchableOpacity,
  Linking,
  PermissionsAndroid,
  NativeModules,
} from 'react-native';

import {connect} from 'react-redux';
var {Actions} = require('react-native-router-flux');

//action

//components
var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
var Util = require('../../Util/Util');
var Include = require('../../Include');
var RNIntent = NativeModules.RNIntent;
var ButtonWrap = require('../elements/ButtonWrap');
var CommonUtil = require('../../Util/common');
import BikeActions_MiddleWare from '../../actions/BikeActions_MiddleWare';
import Popup from './Popup';
import DefaultPopup from './DefaultPopup';
import LinearGradient from 'react-native-linear-gradient';
import PenguinBoldIcon from '../elements/PenguinBoldIcon/PenguinBoldIcon';
import PenguinLinearIcon from '../elements/PenguinLinearIcon/PenguinLinearIcon';
var {popupActions, popupConst} = require('./PopupManager');
var {globalVariableManager} = require('../modules/GlobalVariableManager');

class OptionPopup extends Popup {
  static componentName = 'OptionPopup';
  static config = {
    ...Popup.config,
    movePopupIn: contentObject => {
      var contentRefObject = contentObject.objRef;
      return contentRefObject.fadeInUp(200);
    },
    movePopupOut: contentObject => {
      var contentRefObject = contentObject.objRef;
      return contentRefObject.fadeOutDown(200);
    },
  };
  static containerStyle = {
    ...Popup.containerStyle,
  };
  static defaultProps = {
    disableClose: true,
  };
  // static defaultProps = {}
  // static propTypes = {}
  constructor(props) {
    super(props);
    this.state = _.merge(this.state, {
      bottom: 0,
    });
  }
  renderButton = () => {
    const {onPress, buttonTitle} = this.props;
    return (
      <TouchableOpacity
        onPress={() => {
          popupActions.popPopup();
          onPress && onPress();
        }}>
        <View
          style={{
            backgroundColor: '#1589D8',
            paddingVertical: 12,
            marginHorizontal: 16,
            borderRadius: 16,
            alignItems: 'center',
          }}>
          <Text allowFontScaling={false}
            style={{
              fontSize: 18,
              fontFamily: Define.constants.fontBold600,
              color: '#fff',
            }}>
            {buttonTitle}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  renderButton2 = () => {
    const {onPress2, buttonTitle2} = this.props;
    return (
      <TouchableOpacity
        onPress={() => {
          popupActions.popPopup();
          onPress2 && onPress2();
        }}>
        <View
          style={{
            backgroundColor: '#00BF30',
            paddingVertical: 12,
            marginHorizontal: 16,
            marginBottom: 16,
            borderRadius: 16,
            alignItems: 'center',
          }}>
          <Text allowFontScaling={false}
            style={{
              fontSize: 18,
              fontFamily: Define.constants.fontBold600,
              color: '#fff',
            }}>
            {buttonTitle2}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  renderButton3 = () => {
    const {onPress3, buttonTitle3} = this.props;
    return (
      <TouchableOpacity
        onPress={() => {
          popupActions.popPopup();
          onPress3 && onPress3();
        }}>
        <View
          style={{
            backgroundColor: '#00BF30',
            paddingVertical: 12,
            marginHorizontal: 16,
            borderRadius: 16,
            marginBottom: 16,
            alignItems: 'center',
          }}>
          <Text allowFontScaling={false}
            style={{
              fontSize: 18,
              fontFamily: Define.constants.fontBold600,
              color: '#fff',
            }}>
            {buttonTitle3}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  renderContent() {
    var content = null;
    const {onPress, onPress2, onPress3, buttonTitle, buttonTitle2, buttonTitle3} = this.props;
    return (
      <View
        style={{
          width: Define.constants.widthScreen,
          backgroundColor: '#2E3236',
          borderTopLeftRadius: 16,
          borderTopRightRadius: 16,
          elevation: Define.constants.elevation,
          borderColor: '#000',
          position: 'absolute',
          bottom: this.state.bottom,
          paddingVertical: 16,
        }}>
        {onPress3 && buttonTitle3 ? this.renderButton3() : null}
        {onPress2 && buttonTitle2 ? this.renderButton2() : null}
        {onPress && buttonTitle ? this.renderButton() : null}
      </View>
    );
  }
}

// NOTE : TODO :now can't use connect ( popupManager can't read config)
/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  return {
    appSetting: state.appSetting,
    user: state.User,
  };
}

export default connect(selectActions, undefined, undefined, {withRef: true})(
  OptionPopup,
);
