var _ = require('lodash')
//LIB
import React from 'react';
import {
  View,
  Platform,
  Keyboard,
  TouchableOpacity,
  FlatList,
  ScrollView,
  Text,
  TouchableWithoutFeedback,
  TextInput
} from 'react-native';

//components
var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
var Util = require('../../Util/Util');
var Include = require('../../Include');
var NotifyUtil = require('../../Util/notify')

import { Hoshi } from 'react-native-textinput-effects';
import { TextInputMask } from 'react-native-masked-text';
import LinearGradient from 'react-native-linear-gradient';
import Popup from './Popup';
import DefaultPopup from './DefaultPopup';
import HeyUIcon from '../elements/HeyUIcon/HeyUIcon'
var { popupActions, popupConst } = require('./PopupManager');
var { globalVariableManager } = require('../modules/GlobalVariableManager');
import { connect } from 'react-redux';
import * as Animatable from 'react-native-animatable';

import FeedsSystemActions_MiddleWare from '../../actions/FeedsSystemActions_MiddleWare';

class DefaultPhoneBottomPopup extends Popup {
  static componentName = 'DefaultPhoneBottomPopup'
  static config =
    {
      ...Popup.config,
      movePopupIn: (contentObject) => {
        var contentRefObject = contentObject.objRef;
        return contentRefObject.fadeInUp(200);
      },
      movePopupOut: (contentObject) => {
        var contentRefObject = contentObject.objRef;
        return contentRefObject.fadeOutDown(300);
      }
    }
  static containerStyle = {
    ...Popup.containerStyle,
  }
  static defaultProps = {

  }
  // static defaultProps = {}
  // static propTypes = {}
  constructor(props) {
    super(props);
    this.state = _.merge(this.state, {
      bottom: 0,
      focusPhoneTemp: false,
      phoneValidate: true
    })
    this.phoneTemp = this.props.phone || ''
    this.keyboardWillHide = this.keyboardWillHide.bind(this)
    this.keyboardWillShow = this.keyboardWillShow.bind(this)
  }

  renderContent() {
    return (
      <View style={{
        width: Define.constants.widthScreen,
        backgroundColor: '#fff',
        borderTopLeftRadius: 16,
        borderTopRightRadius: 16,
        elevation: Define.constants.elevation,
        borderColor: '#000',
        position: 'absolute',
        bottom: this.state.bottom
      }}>
        <View style={{justifyContent: 'center', alignItems: 'center', borderBottomWidth: 2, borderColor: '#E2E2E2', padding: 16, borderTopLeftRadius: 16, borderTopRightRadius: 16}}>
          <Include.Text style={{ fontSize: 18, fontFamily: Define.constants.fontBold500, color: '#012548' }}>{this.props.modify ? 'Sửa số điện thoại' : 'Thêm số điện thoại'}</Include.Text>
        </View>
        <TouchableOpacity
          style={{top: 16, left: 16, position:'absolute'}}
          onPress={() => {
            popupActions.popPopup()
          }}
          >
          <HeyUIcon name={'fi-sr-cross-small'} size={24} color={'#929394'}/>
        </TouchableOpacity>
        <View style={{ marginTop: 20, paddingHorizontal: 16 }}>
          <View style={{ backgroundColor: '#fff', zIndex: 10 }}>
            <Include.Text style={{ fontSize: 12, color: '#2A425A', fontWeight: '500', fontFamily: Define.constants.fontBold500 }}>
              Số điện thoại <Text allowFontScaling={false} style={{ color: 'red'}}>*</Text></Include.Text>
          </View>
          <View style={{ backgroundColor: '#fff', borderBottomWidth: 1, borderColor: this.state.focusPhoneTemp ? '#1589D8' : '#eef0f0', paddingVertical: Platform.OS === 'ios' ? 8 : 0 }}>
            <TextInput
              ref={(ref) => this._phone = ref}
              placeholder={this.state.focusPhoneTemp ? '' : 'Nhập số điện thoại'}
              keyboardType={'numeric'}
              defaultValue={this.phoneTemp}
              placeholderTextColor={'#7F8182'}
              style={{ color: '#012548', backgroundColor: 'transparent', fontSize: 16, fontFamily: Define.constants.fontBold500 }}
              blurOnSubmit={false}
              returnKeyType={'done'}
              autoFocus={true}
              onChangeText={(text) => {
                text = text.replace(/[^+0-9]/g, '');
                if (text.startsWith('+84')) {
                  text = text.replace('+84', '0');
                }

                this.phoneTemp = text;
                if (!this.state.phoneSenderValidate) {
                  this.setState({
                    phoneValidate: true
                  });
                }

                clearTimeout(this.timeoutPhoneSender);
                this.timeoutPhoneSender = setTimeout(() => {
                  this.setState({
                    phoneValidate: Util.validatePhone(text)
                  });
                }, 500);
              }}
              onFocus={() => {
                this.setState({
                  focusPhoneTemp: true
                })
                setTimeout(() => {
                  this.scrollViewSender && this.scrollViewSender.scrollToEnd()
                }, 500)
              }}
              onBlur={() => {
                if (this.state.focusPhoneTemp) {
                  this.setState({
                    focusPhoneTemp: false
                  })
                }
              }}
            />
          </View>
          {!this.state.phoneValidate ? <Include.Text style={{ color: '#E93940', marginTop: 8, fontSize: 14, fontFamily: Define.constants.fontBold400 }}>Bạn cần nhập đúng định dạng số điện thoại để tiếp tục</Include.Text> : null}
        </View>

        <View
          style={{ borderRadius: 6, margin: 16 }}
        >
          <TouchableOpacity
            style={{ paddingVertical: 16, borderRadius: 16, alignItems: 'center', justifyContent: 'center', backgroundColor: !this.state.phoneValidate ? '#929394' : '#1589D8' }}
            onPress={() => {
              if(!Util.validatePhone(this.phoneTemp)){
                this.setState({
                  phoneValidate: Util.validatePhone(this.phoneTemp)
                })
                return
              }
              if (this.phoneTemp && this.state.phoneValidate) {
                this.props.onPress && this.props.onPress(this.phoneTemp)
                if(this.props.modify) {
                  popupActions.popPopup();
                } else {
                  NotifyUtil.pushAlertTopNotify({
                    type: 'success',
                    content: 'Thêm số điện thoại thành công',
                    timeClose: 2000
                  })
                  popupActions.popPopup();
                }
              }
              else {
                NotifyUtil.pushAlertTopNotify({
                  type: 'warning',
                  content: 'Vui lòng điền đúng định dạnh số điện thoại',
                  timeClose: 2000
                })
              }
            }}
          >
            <Include.Text style={{ backgroundColor: 'transparent', color: '#fff', fontSize: 18, fontFamily: Define.constants.fontBold500 }}>{this.props.modify ? 'Sửa' : 'Thêm'}</Include.Text>
          </TouchableOpacity>
        </View>
      </View>
    )
  }

  componentDidMount() {
    super.componentDidMount();
    if (Platform.OS === 'android') {
      this.keyboardDidShowSubscription = Keyboard.addListener('keyboardDidShow', this.keyboardWillShow)
      this.keyboardDidHideSubscription = Keyboard.addListener('keyboardDidHide', this.keyboardWillHide)
    } else {
      this.keyboardDidShowSubscription = Keyboard.addListener('keyboardWillShow', this.keyboardWillShow)
      this.keyboardDidHideSubscription = Keyboard.addListener('keyboardWillHide', this.keyboardWillHide)
    }
  }

  componentWillUnmount() {
    super.componentWillUnmount();
    this.keyboardDidShowSubscription.remove()
    this.keyboardDidHideSubscription.remove()

  }

  keyboardWillShow(e) {
    const heightKeyboard = e.endCoordinates.height;
    if (Platform.OS === 'ios') {
      this.setState({
        bottom: heightKeyboard
      });
    } else {
      // for Android
    }
  }

  keyboardWillHide() {
    this.setState({
      bottom: 0
    });
  }
}

// NOTE : TODO :now can't use connect ( popupManager can't read config)
/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  return {
    user: state.User,
  }
}

export default connect(selectActions, undefined, undefined, { withRef: true })(DefaultPhoneBottomPopup);