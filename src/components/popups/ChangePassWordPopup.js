
var _ = require('lodash')
//LIB
import React from 'react';
import {
    View,
    Image,
    TouchableOpacity,
    Text,
    ScrollView,
    TextInput,
    Platform,
    Keyboard
} from 'react-native';

// import { connect } from 'react-redux';
var NotifyUtil = require('../../Util/notify')

//action

//components
var Define = require('../../Define');
// var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
// var Util = require('../../Util/Util');
var Include = require('../../Include');

var StyleConfig = require('../../Themes/StyleConfig');
var RectButton = require('../elements/RectButton');
var ButtonWrap = require('../elements/ButtonWrap');
import PenguinLinearIcon from '../elements/PenguinLinearIcon/PenguinLinearIcon';
import PenguinBoldIcon from '../elements/PenguinBoldIcon/PenguinBoldIcon';
import UserActions_MiddleWare from '../../actions/UserActions_MiddleWare'
import Popup from './Popup'

var { popupActions, popupConst } = require('../popups/PopupManager');
var {globalVariableManager}= require('../modules/GlobalVariableManager');

class ChangePassWordPopup extends Popup {
    static componentName = 'ChangePassWordPopup'
    static config =
        {
            ...Popup.config,
            group: popupConst.POPUP_GROUP,
            tapToExit: true,
            videoMotion: false,
            // movePopupIn:()=>{return new Promise((resolve)=>{resolve()});},
            // movePopupOut:()=>{return new Promise((resolve)=>{resolve()});},
        }
    static containerStyle = {
        ...Popup.containerStyle,
        flexDirection: 'column',
        justifyContent: 'center',
    }
    static defaultProps = {
        disableClose: true,
    }

    constructor(props) {
        super(props);
        let { user } = this.props;
        this.state = _.merge(this.state, {
            keyboardShow: false,
            bottom: 0,
            newPasswordValidate: false,
            confirmNewPasswordValidate: false,
            passwordValidate: false,
            checkPassword: false,
            eyePassword: true,
            eyeNewPassword: true,
            eyeConfirmPassword: true
        })
        this.password = '';
        this.newPassword = '';
        this.confirmNewPassword = '';
        this.keyboardWillShow = this.keyboardWillShow.bind(this);
        this.keyboardWillHide = this.keyboardWillHide.bind(this);
    }

    checkNewPassword = () => {
        let objUpdate = {
            password: this.password,
            newPassword: this.newPassword,
            rePassword: this.confirmNewPassword
        }
        if (!this.password && !this.newPassword && !this.confirmNewPassword) {
            this._password && this._password.focus();
        } else if (!this.password && this.newPassword && this.confirmNewPassword) {
            this._password && this._password.focus();
        } else if (this.password && !this.newPassword && this.confirmNewPassword) {
            this._newPassword && this._newPassword.focus();
        } else if (this.password && !this.newPassword && !this.confirmNewPassword) {
            this._newPassword && this._newPassword.focus();
        } else if (this.password && !this.confirmNewPassword.includes(objUpdate.newPassword)) {
            this._newConfirmPassword && this._newConfirmPassword.focus();
            this.setState({
                checkPassword: true
            })
        } else if (objUpdate.password && this.confirmNewPassword.includes(objUpdate.newPassword)) {
            this.setState({
                checkPassword: false
            }, () => {
                this.handlePassWordChange(objUpdate)
            })
        }
    }


    handlePassWordChange = (objUpdate) => {
        var { dispatch, navigation } = this.props;
        globalVariableManager.reduxManager.dispatch(UserActions_MiddleWare.changePassword(objUpdate))
            .then((res) => {
                this.props.onPress && this.props.onPress()
                NotifyUtil.pushAlertTopNotify({
                    type: 'success',
                    content: 'Thay đổi mật khẩu thành công',
                    timeClose: 3000,
                })
                popupActions.popPopup()
            })
            .catch(err => {
                NotifyUtil.pushAlertTopNotify({
                    type: 'error',
                    content: 'Thay đổi mật khẩu không thành công',
                    timeClose: 3000,
                })
            })
    }

    keyboardWillShow(e) {
        const heightKeyboard = e.endCoordinates.height;
        if (Platform.OS === 'ios') {
            this.setState({
                bottom: heightKeyboard,
                keyboardShow: true
            });
        } else {
            this.setState({
                keyboardShow: true
            })
        }
    }

    keyboardWillHide(e) {
        if (Platform.OS === 'ios') {
            if (this.scrollProfile) {
                this.scrollProfile.scrollTo({ x: 0, y: 0, animated: false })
            }
            this.setState({
                bottom: 0,
                keyboardShow: false
            });
        } else {
            this.setState({
                keyboardShow: false
            })
        }
    }


    renderContent() {
        var content = null;

        content = (
            <View style={{ backgroundColor: '#1C1E21', paddingVertical: 16, width: Define.constants.widthScreen - 24, borderRadius: 12, justifyContent: 'center', alignItems: 'center',   bottom: this.state.keyboardShow ? this.state.bottom / 2 : 0 }}>
                <Include.Text style={{ fontSize: 18, color: '#fff', fontFamily: Define.constants.fontBold500, marginBottom: 12 }}>
                    Đổi mật khẩu
                </Include.Text>
                <ScrollView
                    showsVerticalScrollIndicator={false}
                    ref={(ref) => this.scrollProfile = ref}
                    style={{ width: '100%' }}
                    keyboardShouldPersistTaps="handled" >
                    <View
                        onLayout={e => this.setState({ heightProfile: e.nativeEvent.layout.height })}
                        style={{ justifyContent: 'center', padding: 16, zIndex: 5, }}>
                        <View>
                            <Include.Text style={{ fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold400, marginBottom: 12 }}>
                                Mật khẩu hiện tại <Include.Text style={{ color: '#E93940' }}>*</Include.Text>
                            </Include.Text>
                            <View
                                style={{
                                    flexDirection: 'row',
                                    backgroundColor: '#1C1E21',
                                    alignItems: 'center',
                                    justifyContent: 'space-between',
                                    borderWidth: 1,
                                    borderColor: '#4A4F55',
                                    padding: 12, borderRadius: 12,
                                }}>
                                <TextInput
                                    placeholder={'Nhập mật khẩu hiện tại'}
                                    placeholderTextColor={'#CCCFD3'}
                                    textContentType={'password'}
                                    secureTextEntry={this.state.eyePassword}
                                    returnKeyType="next"
                                    defaultValue={this.password}
                                    ref={(ref) => this._password = ref}
                                    style={{ backgroundColor: '#1C1E21', color: '#fff', fontSize: 16, flex: 1, fontFamily: Define.constants.fontBold500, }}
                                    blurOnSubmit={false}
                                    autoFocus={false}
                                    onSubmitEditing={() => {
                                        this._newPassword && this._newPassword.focus();
                                    }}
                                    onChangeText={(text) => {
                                        this.password = text
                                        this.forceUpdate();
                                        clearTimeout(this.timeoutPassword);
                                        this.timeoutPassword = setTimeout(() => {
                                            if (this.password.length > 5) {
                                                this.setState({ passwordValidate: true });
                                            } else {
                                                this.setState({ passwordValidate: false });
                                            }
                                        }, 500);
                                    }}
                                />
                                <View style={{ flexDirection: 'row', }}>
                                    {this.password ?
                                        <TouchableOpacity
                                            onPress={() => {
                                                this.password = ''
                                                this.forceUpdate()
                                            }}
                                            style={{ justifyContent: 'flex-end', marginRight: 16 }}>
                                            <PenguinBoldIcon name='close-circle' size={20} color={'#fff'} />
                                        </TouchableOpacity> : null}
                                    <TouchableOpacity
                                        onPress={() => {
                                            this.setState({
                                                eyePassword: !this.state.eyePassword
                                            })
                                        }}
                                        style={{ justifyContent: 'flex-end' }}>
                                        {!this.state.eyePassword ?
                                            <PenguinBoldIcon name='eye' size={20} color={'#fff'} />
                                            : <PenguinBoldIcon name='eye-slash' size={20} color={'#fff'} />}
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                        <View style={{ marginTop: 20 }}>
                            <Include.Text style={{ fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold400, marginBottom: 12 }}>
                                Mật khẩu mới <Include.Text style={{ color: '#E93940' }}>*</Include.Text>
                            </Include.Text>
                            <View
                                style={{
                                    flexDirection: 'row',
                                    backgroundColor: '#1C1E21',
                                    alignItems: 'center',
                                    justifyContent: 'space-between',
                                    borderRadius: 12,
                                    borderWidth: 1,
                                    borderColor: '#4A4F55',
                                    padding: 12,
                                }}>
                                <TextInput
                                    placeholder={'Nhập mật khẩu mới'}
                                    placeholderTextColor={'#CCCFD3'}
                                    secureTextEntry={this.state.eyeNewPassword}
                                    textContentType={'password'}
                                    returnKeyType="next"
                                    defaultValue={this.newPassword}
                                    ref={(ref) => this._newPassword = ref}
                                    style={{ backgroundColor: '#1C1E21', color: '#fff', fontSize: 16, flex: 1, fontFamily: Define.constants.fontBold500 }}
                                    blurOnSubmit={false}
                                    onSubmitEditing={() => {
                                        this._newConfirmPassword && this._newConfirmPassword.focus();
                                    }}
                                    onChangeText={(text) => {
                                        this.newPassword = text
                                        this.forceUpdate();
                                        clearTimeout(this.timeoutPassword);
                                        this.timeoutPassword = setTimeout(() => {
                                            if (this.newPassword.length > 5) {
                                                this.setState({ newPasswordValidate: true });
                                            } else {
                                                this.setState({ newPasswordValidate: false });
                                            }
                                        }, 500);
                                    }}
                                />
                                <View style={{ flexDirection: 'row', }}>
                                    {this.newPassword ?
                                        <TouchableOpacity
                                            onPress={() => {
                                                this.newPassword = ''
                                                this.forceUpdate()
                                            }}
                                            style={{ justifyContent: 'flex-end', marginRight: 16 }}>
                                            <PenguinBoldIcon name='close-circle' size={20} color={'#fff'} />
                                        </TouchableOpacity> : null}
                                    <TouchableOpacity
                                        onPress={() => {
                                            this.setState({
                                                eyeNewPassword: !this.state.eyeNewPassword
                                            })
                                        }}
                                        style={{ justifyContent: 'flex-end' }}>
                                        {!this.state.eyeNewPassword ?
                                            <PenguinBoldIcon name='eye' size={20} color={'#fff'} />
                                            : <PenguinBoldIcon name='eye-slash' size={20} color={'#fff'} />}
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                        <View style={{ marginTop: 20 }}>
                            <Include.Text style={{ fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold400, marginBottom: 12 }}>
                                Xác nhận mật khẩu mới <Include.Text style={{ color: '#E93940' }}>*</Include.Text>
                            </Include.Text>
                            <View
                                style={{
                                    flexDirection: 'row',
                                    backgroundColor: '#1C1E21',
                                    alignItems: 'center',
                                    justifyContent: 'space-between',
                                    borderRadius: 12,
                                    borderWidth: 1,
                                    borderColor: this.state.checkPassword ? '#E93940' : '#4A4F55',
                                    padding: 12,
                                }}>
                                <TextInput
                                    placeholder={'Xác nhận mật khẩu mới'}
                                    placeholderTextColor={'#CCCFD3'}
                                    textContentType={'password'}
                                    secureTextEntry={this.state.eyeConfirmPassword}
                                    returnKeyType={'done'}
                                    defaultValue={this.confirmNewPassword}
                                    ref={(ref) => this._newConfirmPassword = ref}
                                    style={{ backgroundColor: '#1C1E21', color: '#fff', fontSize: 16, flex: 1, fontFamily: Define.constants.fontBold500, }}
                                    blurOnSubmit={false}
                                    onSubmitEditing={() => {
                                        this.checkNewPassword()
                                    }}
                                    onChangeText={(text) => {
                                        this.confirmNewPassword = text
                                        this.forceUpdate();
                                        clearTimeout(this.timeoutPassword);
                                        this.timeoutPassword = setTimeout(() => {
                                            if (this.confirmNewPassword.length > 5) {
                                                this.setState({ confirmNewPasswordValidate: true });
                                            } else {
                                                this.setState({ confirmNewPasswordValidate: false, checkPassword: false });
                                            }
                                        }, 500);
                                    }}
                                />
                                <View style={{ flexDirection: 'row' }}>
                                    {this.confirmNewPassword ?
                                        <TouchableOpacity
                                            onPress={() => {
                                                this.confirmNewPassword = ''
                                                this.forceUpdate()
                                            }}
                                            style={{ justifyContent: 'flex-end', marginRight: 16 }}>
                                            <PenguinBoldIcon name='close-circle' size={20} color={'#fff'} />
                                        </TouchableOpacity> : null}
                                    <TouchableOpacity
                                        onPress={() => {
                                            this.setState({
                                                eyeConfirmPassword: !this.state.eyeConfirmPassword
                                            })
                                        }}
                                        style={{ justifyContent: 'flex-end' }}>
                                        {!this.state.eyeConfirmPassword ?
                                            <PenguinBoldIcon name='eye' size={20} color={'#fff'} />
                                            : <PenguinBoldIcon name='eye-slash' size={20} color={'#fff'} />}
                                    </TouchableOpacity>
                                </View>
                            </View>
                            {this.state.checkPassword ?
                                <Include.Text style={{ fontSize: 14, color: '#E93940', fontFamily: Define.constants.fontBold500, paddingTop: 6, paddingHorizontal: 12 }}>
                                    Mật khẩu xác nhận đang không khớp với mật khẩu mới
                                </Include.Text> : null}
                        </View>
                    </View>
                </ScrollView>
                <View
                    style={{ flexDirection: 'row', justifyContent: 'space-between', paddingHorizontal: 16, paddingTop: 16, width: '100%' }}>
                    <TouchableOpacity
                        style={{ flex: 1, justifyContent: 'center', alignItems: 'center', borderRadius: 12, marginRight: 6, borderWidth: 1, borderColor: '#fff' }}
                        onPress={() => {
                            this.props.onPress && this.props.onPress()
                        }}>
                        <Include.Text style={{ backgroundColor: 'transparent', fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold500}}>Đóng</Include.Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        style={{ flex: 1, paddingVertical: 12, justifyContent: 'center', alignItems: 'center',  backgroundColor: this.password && this.newPassword && this.confirmNewPassword ? '#007CFE' : '#F3F3F3', borderRadius: 12, marginLeft: 6}}
                        onPress={() => {
                            this.checkNewPassword()
                        }}>
                        <Include.Text style={{ backgroundColor: 'transparent', fontSize: 16, color: this.password && this.newPassword && this.confirmNewPassword ? '#fff' : '#A1A7AE', fontFamily: Define.constants.fontBold500, }}>Đổi mật khẩu</Include.Text>
                    </TouchableOpacity>
                </View>
            </View>
        )
        return (content)
    }

    UNSAFE_componentWillMount() {
        super.UNSAFE_componentWillMount();
        if (this.props.onWillMount) {
            this.props.onWillMount();
        }
        if (Platform.OS === 'android') {
            this.keyboardDidShowSubscription = Keyboard.addListener('keyboardDidShow', this.keyboardWillShow)
            this.keyboardDidHideSubscription = Keyboard.addListener('keyboardDidHide', this.keyboardWillHide)
        } else {
            this.keyboardDidShowSubscription = Keyboard.addListener('keyboardWillShow', this.keyboardWillShow)
            this.keyboardDidHideSubscription = Keyboard.addListener('keyboardWillHide', this.keyboardWillHide)
        }
    }


    componentWillUnmount() {
        super.componentWillUnmount()
        if (this.props.onWillUnmount) {
            this.props.onWillUnmount();
        }
    }
}

/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
// function selectActions(state) {
//   return {}
// }

// export default connect(selectActions, undefined, undefined, {withRef: true})(ChangePassWordPopup);
export default ChangePassWordPopup