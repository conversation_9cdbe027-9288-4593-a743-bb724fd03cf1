var _ = require('lodash');
//LIB
import React from 'react';
import {
  View,
  InteractionManager,
  Text,
  TouchableOpacity,
  Linking,
  PermissionsAndroid,
  NativeModules,
} from 'react-native';

import {connect} from 'react-redux';
var {Actions} = require('react-native-router-flux');

//action

//components
var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
var Util = require('../../Util/Util');
var Include = require('../../Include');
var RNIntent = NativeModules.RNIntent;
var ButtonWrap = require('../elements/ButtonWrap');
var CommonUtil = require('../../Util/common');
import BikeActions_MiddleWare from '../../actions/BikeActions_MiddleWare';
import Popup from './Popup';
import DefaultPopup from './DefaultPopup';
import LinearGradient from 'react-native-linear-gradient';
import PenguinBoldIcon from '../elements/PenguinBoldIcon/PenguinBoldIcon';
import PenguinLinearIcon from '../elements/PenguinLinearIcon/PenguinLinearIcon';
var {popupActions, popupConst} = require('./PopupManager');
var {globalVariableManager} = require('../modules/GlobalVariableManager');

class DefaultOptionPopup extends Popup {
  static componentName = 'DefaultOptionPopup';
  static config = {
    ...Popup.config,
    movePopupIn: contentObject => {
      var contentRefObject = contentObject.objRef;
      return contentRefObject.fadeInUp(200);
    },
    movePopupOut: contentObject => {
      var contentRefObject = contentObject.objRef;
      return contentRefObject.fadeOutDown(200);
    },
  };
  static containerStyle = {
    ...Popup.containerStyle,
  };
  static defaultProps = {
    disableClose: true,
  };
  // static defaultProps = {}
  // static propTypes = {}
  constructor(props) {
    super(props);
    this.state = _.merge(this.state, {
      bottom: 0,
    });
  }
  renderRemove = () => {
    return (
      <TouchableOpacity
        onPress={() => {
          popupActions.popPopup();
          setTimeout(() => {
            this.props.onPress && this.props.onPress();
          },100)
        }}
        style={{
          backgroundColor: '#1589D81A',
          paddingVertical: 16,
          marginHorizontal: 16,
          marginTop: 16,
          borderRadius: 16,
          alignItems: 'center',
        }}>
        <PenguinBoldIcon
          name={'box-remove'}
          size={24}
          color={'#E93940'}
          style={{position: 'absolute', top: 19, left: 16}}
        />
        <Text allowFontScaling={false}
          style={{
            fontSize: 18,
            fontFamily: Define.constants.fontBold600,
            color: '#E93940',
          }}>
          {this.props.title}
        </Text>
      </TouchableOpacity>
    );
  };
  renderModify = () => {
    return (
      <TouchableOpacity
        onPress={() => {
          popupActions.popPopup()
          setTimeout(() => {
            this.props.onPress2 && this.props.onPress2();
          },100)
        }}
        style={{
          backgroundColor: '#1589D81A',
          paddingVertical: 16,
          marginHorizontal: 16,
          marginTop: 16,
          borderRadius: 16,
          alignItems: 'center',
        }}>
        <PenguinBoldIcon
          name={'edit-2'}
          size={24}
          color={'#1589D8'}
          style={{position: 'absolute', top: 19, left: 16}}
        />
        <Text allowFontScaling={false}
          style={{
            fontSize: 18,
            fontFamily: Define.constants.fontBold600,
            color: '#1589D8',
          }}>
          {this.props.title2}
        </Text>
      </TouchableOpacity>
    );
  };
  renderChoose = () => {
    return (
      <TouchableOpacity
        onPress={() => {
          popupActions.popPopup()
          setTimeout(() => {
            this.props.onPress3 && this.props.onPress3();
          },100)
        }}
        style={{
          backgroundColor: '#1589D81A',
          paddingVertical: 16,
          marginHorizontal: 16,
          marginTop: 16,
          borderRadius: 16,
          alignItems: 'center',
        }}>
        <PenguinBoldIcon
          name={'location'}
          size={24}
          color={'#1589D8'}
          style={{position: 'absolute', top: 19, left: 16}}
        />
        <Text allowFontScaling={false}
          style={{
            fontSize: 18,
            fontFamily: Define.constants.fontBold600,
            color: '#1589D8',
          }}>
          {this.props.title3}
        </Text>
      </TouchableOpacity>
    );
  };
  renderContent() {
    var content = null;
    const {orderInf, onReject, ORDER_STATUS, appState} = this.props;
    return (
      <View
        style={{
          width: Define.constants.widthScreen,
          backgroundColor: '#fff',
          borderTopLeftRadius: 16,
          borderTopRightRadius: 16,
          elevation: Define.constants.elevation,
          borderColor: '#000',
          position: 'absolute',
          bottom: this.state.bottom,
          paddingBottom: 16,
        }}>
        {this.props.onPress3 ? this.renderChoose() : null}
        {this.props.onPress2 ? this.renderModify() : null}
        {this.props.onPress ? this.renderRemove(): null}
      </View>
    );
  }
}

// NOTE : TODO :now can't use connect ( popupManager can't read config)
/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  return {
    appSetting: state.appSetting,
    user: state.User,
  };
}

export default connect(selectActions, undefined, undefined, {withRef: true})(
  DefaultOptionPopup,
);
