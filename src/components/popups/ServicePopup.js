var _ = require('lodash')
//LIB
import React from 'react';
import {
  View,
  TouchableOpacity,
  Image,
  Text,
} from 'react-native';
import { connect } from 'react-redux';
var { Actions } = require('react-native-router-flux');
import BottomSheet, { BottomSheetFlatList, BottomSheetModal, BottomSheetModalProvider } from '@gorhom/bottom-sheet';
//components
var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
var Util = require('../../Util/Util');
var Include = require('../../Include');
import Popup from './Popup';
import DefaultPopup from './DefaultPopup';

var { popupActions, popupConst } = require('./PopupManager');
var { globalVariableManager } = require('../modules/GlobalVariableManager');
import HeyUIcon from '../elements/HeyUIcon/HeyUIcon';

class ServicePopup extends Popup {
  static componentName = 'ServicePopup'
  static config = {
    ...Popup.config,
    movePopupIn: (contentObject) => {
      var contentRefObject = contentObject.objRef;
      return contentRefObject.fadeInUp(200);
    },
    movePopupOut: (contentObject) => {
      var contentRefObject = contentObject.objRef;
      return contentRefObject.fadeOutDown(300);
    }
  }
  static containerStyle = {
    ...Popup.containerStyle,
  }
  static defaultProps = {
    disableClose: true,
    tabToExit: true
  }
  constructor(props) {
    super(props);
    this.state = _.merge(this.state,
      {
        showIcon: 0,
        services: []
      })

    this.bottomSheetRef = React.createRef();
  }

  isCloseToBottom = ({ layoutMeasurement, contentOffset, contentSize }) => {
    return layoutMeasurement.height + contentOffset.y >= contentSize.height - 1;
  };

  renderItem = ({item, index}) => {
    var {dispatch, navigation} = this.props;
    return (
      <View>
        {/* {item.title ?
          <View>
            {index !== 0 ?
              <View style={{ height: 1, backgroundColor: '#EEF0F0', marginTop: 16 }} /> : null
            }
            <Text allowFontScaling={false} style={{ color: '#143250', fontSize: 18, fontWeight: '600', marginTop: 16 }}>{item.title}</Text>
          </View> : null
        } */}
        <TouchableOpacity
          onPress={() => {
            if (item.active) {
              popupActions.popPopup()
              if (!item.blockCreate) {
                  globalVariableManager.navigatorManager.handleNavigator(item.link, {
                    ...item.extras,
                    id: item._id,
                    check: true
                  });
              }
            } else {
              popupActions.setRenderContentAndShow(DefaultPopup, {
                title: 'Thông báo',
                description: item.message,
                buttonTitle2: 'Xong',
                buttonTitle: item.source ? 'Tìm hiểu thêm' : null,
                onPress2: () => {
                  popupActions.popPopup();
                },
                onPress: () => {
                  popupActions.popPopup();
                  if (item.source) {
                    navigation.navigate('WebviewScreen',{
                      source: item.source
                    });
                  }
                }
              });
            }
          }}
          activeOpacity={0.6}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 16, marginBottom: index === this.state.services.length - 1 ? 16 : 0 }}>
            <View>
              {item.tag ?
                <View
                  pointerEvents={'none'}
                  style={{ position: 'absolute', right: -10, top: -6, padding: 4, borderRadius: 12, backgroundColor: '#e17055', alignItems: 'center', justifyContent: 'center', borderWidth: 2, borderColor: '#fff', shadowColor: '#fff', shadowOpacity: 0.5, shadowOffset: { height: 1, width: 0 }, zIndex: 10 }}>
                  <Text allowFontScaling={false} numberOfLines={1} style={{ color: '#fff', backgroundColor: 'transparent', fontSize: 10 }}>{item.tag}</Text>
                </View> : null}
              {item.tagImage ?
                <Image style={{ width: 32, height: 32, position: 'absolute', top: -6, right: -10, zIndex: 10 }} resizeMode={'contain'} source={{ uri: item.tagImage }} /> : null}
              <Image style={{ width: 42, height: 42 }} resizeMode={'stretch'} source={{ uri: item.icon }} />
            </View>
            <View style={{ marginLeft: 12, flex: 1 }}>
              <Include.Text style={{ color: item.blockCreate ? '#b2bec3' : '#143250', fontSize: 16, fontFamily: Define.constants.fontBold500 }}>{item.name}</Include.Text>
              {item.description ? <Include.Text numberOfLines={2} style={{ fontSize: 14, color: '#929394' }}>{item.description}</Include.Text> : null}
            </View>
          </View>
        </TouchableOpacity>
      </View>
    )
  }

  renderContent() {
    var content = null;
    content = (
      <BottomSheet
        ref={this.bottomSheetRef}
        index={0}
        snapPoints={[Define.constants.heightScreen * 0.8]}
        enableOverDrag={true}
        style={{}}
        dismissOnPanDown={false}
        animateOnMount={true}
        enablePanDownToClose={true}
        onChange={(index) => {
          if (index === -1) {
            popupActions.popPopup();
          }
        }}
      >
        <View style={{ paddingBottom: 16, paddingVertical: 6, borderBottomWidth: 1, borderBottomColor: '#EEF0F0', flexDirection: 'row', justifyContent: 'center', alignItems: 'center' }}>
          <Text allowFontScaling={false} style={{ color: '#143250', fontSize: 18, fontFamily: Define.constants.fontBold600 }}>{this.props.category}</Text>
          <TouchableOpacity onPress={() => { popupActions.popPopup() }} style={{ position: 'absolute', width: 40, height: '100%', right: 0, top: 10 }}>
            <HeyUIcon name={'fi-br-cross'} size={14} color={'#B3B5B6'} />
          </TouchableOpacity>
        </View>
        <BottomSheetFlatList
          data={this.state.services}
          keyExtractor={(item, index) => index}
          renderItem={this.renderItem}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ flexGrow: 1, paddingHorizontal: 16 }}
        // onContentSizeChange={(contentWidth, contentHeight) => {
        //   if (contentHeight > Define.constants.heightScreen * 0.8 - 20) {
        //     this.setState({ showIcon: 1 })
        //   }
        // }}
        // onScroll={({ nativeEvent }) => {
        //   if (this.isCloseToBottom(nativeEvent)) {
        //     this.setState({ showIcon: 0 })
        //   } else if(nativeEvent.contentSize.height > Define.constants.heightScreen * 0.8 - 20) {
        //     this.setState({ showIcon: 1 })
        //   }
        // }}
        />
        {/* {this.state.showIcon ?
            <TouchableOpacity
              activeOpacity={0.6}
              onPress={()=>{this.scrollView && this.scrollView.scrollToEnd({animated: true, duration: 500})}}
              style={{ position: 'absolute', bottom: 12, alignSelf: 'center'}}>
                <View style={{flexDirection: 'row', borderRadius: 30, paddingVertical: 12, alignItems: 'center', backgroundColor:'#1589D8', paddingHorizontal: 12}}>
                  <Icon name='angle-dobule-down' size={14} style={{color:'#fff'}}/>
                </View>
            </TouchableOpacity> : null} */}
      </BottomSheet>
    )
    return (content)
  }

  componentDidMount = () => {
    const groups = this.props.services.reduce((groups, item) => {
      const group = (groups[item.title] || []);
      group.push(item);
      groups[item.title] = group;
      return groups;
    }, {});

    let services = [];
    Object.keys(groups).map(key => {
      groups[key].map((service, index) => {
        if (index !== 0) {
          services.push({...service, title: ''});
        } else {
          services.push(service);
        }
      })
    })

    this.setState({
      services: services.length ? services : this.props.services
    })
  }
}
// NOTE : TODO :now can't use connect ( popupManager can't read config)
/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  return {
    navigator: state.Navigator,
    iHeyU: state.IHeyU
  }
}

export default connect(selectActions, undefined, undefined, {withRef: true})(ServicePopup);
