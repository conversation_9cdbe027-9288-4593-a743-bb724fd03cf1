
var _ = require('lodash')
//LIB
import React from 'react';
import {
  View,
  Image,
  TouchableOpacity,
  Linking,
  Platform,
  BackHandler
  // InteractionManager
} from 'react-native';

// import { connect } from 'react-redux';

//action

//components
var Define = require('../../Define');
// var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
// var Util = require('../../Util/Util');
var Include = require('../../Include');

var StyleConfig = require('../../Themes/StyleConfig');
var RectButton = require('../elements/RectButton');
var ButtonWrap = require('../elements/ButtonWrap');

import Popup from './Popup'

var { popupActions, popupConst } = require('../popups/PopupManager');
// var {globalVariableManager}= require('../modules/GlobalVariableManager');

class HotUpdatePopup extends Popup {
  static componentName = 'HotUpdatePopup'
  static config = {
    ...Popup.config,
    group: popupConst.POPUP_GROUP,
    tapToExit: false
  }
  static containerStyle = {
    ...Popup.containerStyle,
    flexDirection: 'column',
    justifyContent: 'center',
  }
  // static propTypes = {}
  constructor(props) {
    super(props);
    this.state = _.merge(this.state, {})
  }

  UNSAFE_componentWillMount() {
    super.UNSAFE_componentWillMount();
    if (this.props.onWillMount) {
      this.props.onWillMount();
    }
  }

  renderPopupContent() {
    const buttonTitle = this.props.buttonTitle || 'Cập nhật';
    const title = this.props.title || 'Cập nhật ứng dụng';

    return (
      <View style={{ backgroundColor: '#f9f9f9', padding: 16, width: Define.constants.widthScreen * 0.9, borderRadius: 8 }}>
        <View style={{ justifyContent: 'center' }}>
          <Include.Text style={{ fontSize: 20, fontWeight: '500', color: '#161616' }}>{title}</Include.Text>
        </View>
        <View style={{ width: '100%', paddingTop: 10 }}>
          <Include.Text style={{ fontSize: 14, color: '#696969' }}>{this.props.description}</Include.Text>
        </View>
        <View style={{ flexDirection: 'row', justifyContent: 'flex-end', marginTop: 16 }}>
          {this.props.cancel ?
            <TouchableOpacity
              activeOpacity={0.6}
              onPress={() => { popupActions.popPopup() }}
            >
              <View style={{ justifyContent: 'center', height: 48, marginRight: 10 }}>
                <Include.Text style={{ fontSize: 16, fontWeight: '500', color: '#1E5D97' }}>Để sau</Include.Text>
              </View>
            </TouchableOpacity> : null
          }
          {this.props.onPress && buttonTitle ?
            <TouchableOpacity
              activeOpacity={0.6}
              onPress={this.props.onPress}
            >
              <View style={{ backgroundColor: '#1589D8', height: 48, paddingHorizontal: 20, borderRadius: 6, justifyContent: 'center' }}>
                <Include.Text style={{ fontSize: 16, fontWeight: '500', color: '#fff' }}>{buttonTitle}</Include.Text>
              </View>
            </TouchableOpacity> : null
          }
        </View>
      </View>
    )
  }

  UNSAFE_componentWillMount() {
    this.back = BackHandler.addEventListener('hardwareBackPress', this.backHandler);
  }

  componentWillUnmount() {
    if (this.back) {
      this.back.remove()
    }
  }

  backHandler = () => {
    BackHandler.exitApp()
  }
}

/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
// function selectActions(state) {
//   return {}
// }

// export default connect(selectActions, undefined, undefined, {withRef: true})(DefaultPopup);
export default HotUpdatePopup