var _ = require('lodash')
//LIB
import React  from 'react';
import {
  View,
  Modal,
  TouchableOpacity,
  SafeAreaView
} from 'react-native';

import ImageViewer from 'react-native-image-zoom-viewer';
import {Ionicons as Icon} from '@react-native-vector-icons/ionicons';

// import { connect } from 'react-redux';

//action

//components
var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
var Util = require('../../Util/Util');
var Include = require('../../Include');

import Popup from './Popup'
import HeyUIcon from '../elements/HeyUIcon/HeyUIcon';

var {popupActions,popupConst} = require('./PopupManager');
var {globalVariableManager}= require('../modules/GlobalVariableManager');

class ImageOrderCreatePopup extends Popup{
  static componentName = 'ImageOrderCreatePopup'
  static config=
  {
    ...Popup.config,
    group: popupConst.POPUP_BLOCK
  }
  static containerStyle={
    ...Popup.containerStyle,
  }
  static defaultProps = {
    disableClose: true,
  }
  // static defaultProps = {}
  // static propTypes = {}
  constructor(props){
    super(props);
    this.state = _.merge(this.state,
    {
      images: this.props.images
    })

    this.index = this.props.index || 0;
  }

  onPressDeclineFile = () => {
    const images = this.state.images;
    this.props.declineFile(this.index);

    images.splice(this.index, 1);
    if (!images.length) {
      popupActions.popPopup();
    }

    if (this.index === images.length) {
      this.index = this.index - 1;
    }

    this.setState({images});
  }

  renderContent(){
    const images = this.state.images;

    var content = (
      <View style={{flex: 1}}>
        <Modal visible={true} transparent={true}>
          <SafeAreaView style={{right: 16, top: 32, position: 'absolute', zIndex: 13, elevation:13}}>
            <TouchableOpacity
              onPress={() => {
                popupActions.popPopup();
              }}
              style={{
                alignItems: 'center',
                justifyContent: 'center',
                width: 32,
                height: 32,
                backgroundColor: '#e74c3c',
                borderRadius: 32,
              }}
            >
              <HeyUIcon name='fi-rr-cross-small' size={20} color='#fff' style={{marginTop: -2}}/>
            </TouchableOpacity>
          </SafeAreaView>
          <ImageViewer
            enableSwipeDown={true}
            saveToLocalByLongPress={false}
            imageUrls={images}
            onChange={(index) => {
              this.index = index;
            }}
            index={this.index}
            onCancel={() => popupActions.popPopup()}
            backgroundColor={'rgba(122, 122, 122, 0.8)'}
          />
          {this.props.isDeclineFile ?
            <SafeAreaView style={{position: 'absolute', zIndex: 13, width: '100%', alignItems: 'center', elevation:13,bottom: 10}}>
              <TouchableOpacity
                onPress={this.onPressDeclineFile}
                style={{alignItems: 'center', bottom: 10}}
              >
                <Icon name='trash' style={{fontSize: 30, color: '#e74c3c'}} />
              </TouchableOpacity>
            </SafeAreaView> : null
          }
        </Modal>
      </View>
    );
    return(content)
  }
}

// NOTE : TODO :now can't use connect ( popupManager can't read config)
/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
// function selectActions(state) {
//   return {}
// }

// export default connect(selectActions, undefined, undefined, {withRef: true})(ImageOrderCreatePopup);

export default ImageOrderCreatePopup
