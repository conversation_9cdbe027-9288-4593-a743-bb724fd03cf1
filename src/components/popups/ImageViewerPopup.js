
var _ = require('lodash')
//LIB
import React from 'react';
import {
  View,
  TouchableOpacity,
  Platform,
  StatusBar
} from 'react-native';
import ImageViewer from 'react-native-image-zoom-viewer';


//action

//components
import HeyUIcon from '../elements/HeyUIcon/HeyUIcon'
var Define = require('../../Define');

import Popup from './Popup'

var { popupActions, popupConst } = require('./PopupManager');

class ImageViewerPopup extends Popup {
  static componentName = 'ImageViewerPopup'
  static config =
    {
      ...Popup.config,
    }
  static containerStyle = {
    ...Popup.containerStyle,
  }
  static defaultProps = {
    disableClose: true,
  }
  constructor(props) {
    super(props);
    this.state = _.merge(this.state,
      {})
  }
  renderContent() {
    var content = null;

    content = (
      <View style={{ flex: 1, width: Define.constants.widthScreen }}>
        <ImageViewer
          imageUrls={this.props.images}
          saveToLocalByLongPress={false}
          onCancel={() => popupActions.popPopup()}
          backgroundColor={'rgba(0,0,0,0.5)'}
          enableSwipeDown={true}
          useNativeDriver={true}
        />
        <TouchableOpacity style={{ position: 'absolute', top: Platform.OS === 'android' ? StatusBar.currentHeight + 10 : 10, right: 10, height: 30, width: 30, backgroundColor: '#0473CD', borderRadius: 99, justifyContent: 'center', alignItems: 'center', zIndex: 10 }}
          activeOpacity={0.6}
          onPress={() => { popupActions.popPopup() }}>
          <HeyUIcon name={'fi-br-cross-small'} size={22} color={'#fff'} />
        </TouchableOpacity>
      </View>
    )
    return (content)
  }
}

// NOTE : TODO :now can't use connect ( popupManager can't read config)
/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
// function selectActions(state) {
//   return {}
// }

// export default connect(selectActions, undefined, undefined, {withRef: true})(TemplatePopup);

export default ImageViewerPopup
