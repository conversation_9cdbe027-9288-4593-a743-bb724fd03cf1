var _ = require('lodash');
//LIB
import React from 'react';
import {
  Image,
  Text,
  View,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  AppState,
  SafeAreaView,
  FlatList,
  StatusBar,
  Platform,
} from 'react-native';
import {connect} from 'react-redux';
//action
import UserActions_MiddleWare from '../../actions/UserActions_MiddleWare';
//components
var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
var Util = require('../../Util/Util');
var Include = require('../../Include');
import Popup from './Popup';

var {popupActions, popupConst} = require('./PopupManager');
var {globalVariableManager} = require('../modules/GlobalVariableManager');
import Carousel from 'react-native-snap-carousel';

import HeyUIcon from '../elements/HeyUIcon/HeyUIcon';
import * as Animatable from 'react-native-animatable';

class ChooseBlockAccountPopup extends Popup {
  static componentName = 'ChooseBlockAccountPopup';
  static config = {
    ...Popup.config,
    movePopupIn: contentObject => {
      var contentRefObject = contentObject.objRef;
      return contentRefObject.fadeInUp(200);
    },
    movePopupOut: contentObject => {
      var contentRefObject = contentObject.objRef;
      return contentRefObject.fadeOutDown(300);
    },
  };
  static containerStyle = {
    ...Popup.containerStyle,
  };
  static defaultProps = {};
  constructor(props) {
    super(props);
    this.state = _.merge(this.state, {});
  }

  renderContent() {
    const {user, actions} = this.props;
    return (
      <View
        style={{
          width: Define.constants.widthScreen,
          backgroundColor: '#fff',
          borderTopLeftRadius: 16,
          borderTopRightRadius: 16,
          elevation: Define.constants.elevation,
          borderColor: '#000',
          position: 'absolute',
          bottom: 0,
          padding: 15,
        }}>
        <View
          style={{
            flex: 1,
            backgroundColor: '#fff',
            borderTopLeftRadius: 12,
            borderTopRightRadius: 12,
          }}>
          <Text allowFontScaling={false}
            style={{
              fontFamily: Define.constants.fontBold,
              fontSize: 18,
              marginBottom: 10,
              color: '#012548',
            }}>
            Bạn có thể cho biết lý do bạn muốn xóa tài khoản không?
          </Text>
          {actions.map((action, index) => {
            return (
              <TouchableOpacity
                key={index}
                onPress={() => {
                  popupActions.popPopup();
                  if (action.data && action.data.link) {
                    globalVariableManager.navigatorManager.handleNavigator(
                      action.data.link,
                      action.data.extras || {},
                    );
                  }
                }}
                style={{
                  flex: 1,
                  flexDirection: 'row',
                  borderBottomWidth: index === actions.length - 1 ? 0 : 0.5,
                  borderBottomColor: '#b2bec3',
                  paddingVertical: 15,
                  alignItems: 'center',
                }}>
                <View style={{flex: 1}}>
                  <Text allowFontScaling={false}
                    style={{
                      fontFamily: Define.constants.fontBold,
                      fontSize: 16,
                      paddingBottom: 5,
                      color: '#012548',
                    }}>
                    {action.title}
                  </Text>
                  <Text allowFontScaling={false} style={{fontSize: 14, color: '#143250'}}>
                    {action.description}
                  </Text>
                </View>
                <HeyUIcon
                  name={'fi-br-angle-small-right'}
                  size={20}
                  color={'#b2bec3'}
                  style={{}}
                />
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    );
  }

  componentDidUpdate(prevProps, prevState) {
    super.componentDidUpdate();
  }

  UNSAFE_componentWillMount() {
    super.UNSAFE_componentWillMount();
  }

  componentDidMount() {}

  componentWillUnmount() {
    super.componentWillUnmount();
  }
}
// NOTE : TODO :now can't use connect ( popupManager can't read config)
/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  return {
    feedSystem: state.FeedSystem,
  };
}
export default connect(selectActions, undefined, undefined, {withRef: true})(
  ChooseBlockAccountPopup,
);
