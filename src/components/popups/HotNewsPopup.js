var _ = require('lodash')
//LIB
import React  from 'react';
import {
  View,
  InteractionManager,
  Image,
  ScrollView,
  TouchableOpacity,
  ImageBackground
} from 'react-native';
import HTML from 'react-native-render-html';
import LinearGradient from 'react-native-linear-gradient';
// import { connect } from 'react-redux';

//action

//components
var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
var Util = require('../../Util/Util');
var Include = require('../../Include');

var ButtonWrap= require('../elements/ButtonWrap');
import HeyUIcon from '../elements/HeyUIcon/HeyUIcon';
import Popup from './Popup'

var {popupActions,popupConst} = require('./PopupManager');
var {globalVariableManager}= require('../modules/GlobalVariableManager');
var {Actions} = require('react-native-router-flux');

class HotNewsPopup extends Popup{
  static componentName = 'HotNewsPopup'
  static config=
  {
    ...Popup.config,
    noDetectGes: true,
    group:popupConst.HOTNEWS_GROUP
  }
  static containerStyle={
    ...Popup.containerStyle,
  }
  static defaultProps = {
    disableClose:true,
  }
  // static defaultProps = {}
  // static propTypes = {}
  constructor(props){
    super(props);
    this.state = _.merge(this.state,
    {})
  }
  renderContent(){
    const htmlContent = this.props.data.body
    const nativeVersion = Number(Define.constants.nativeVersion)
    var content = (
      <View style={{borderBottomLeftRadius: 12, borderBottomRightRadius: 12, maxHeight: Define.constants.heightScreen*0.85}}>
        <View style={{width: Define.constants.widthScreen*0.9, backgroundColor:'#ffffff',  borderRadius: 12, alignItems:'center'}}>
          {this.props.data.backgroundImage ?
            <View style={{borderTopLeftRadius: 12, borderTopRightRadius: 12, overflow: 'hidden'}}>
              <Image source={{uri:this.props.data.backgroundImage}} style={{resizeMode: 'stretch', alignSelf:'center', width: Define.constants.widthScreen*0.9, height: Define.constants.widthScreen*0.9*273/500}}/>
            </View> : null}

          {this.props.data.title ?
            <LinearGradient start={{x: 0, y: 0}} end={{x: 1, y: 0}} colors={['#469CEE', '#3981F4']} style={{width: '100%', alignItems: 'center', justifyContent: 'center', backgroundColor: '#3a83f5', paddingVertical: 16, borderTopLeftRadius: this.props.data.backgroundImage ? 0 : 12, borderTopRightRadius: this.props.data.backgroundImage ? 0 : 12}}>
              <View
                style={{
                  backgroundColor: 'transparent',
                  zIndex: 10,
                  borderWidth: 2,
                  borderColor: '#fff',
                  width: Define.constants.widthScreen*0.9 - 32,
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderStyle: 'dashed'
                }}
              >
                <Include.Text style={{ textAlign:'center', alignSelf:'center', color:'#fff', fontWeight: '700', fontSize: 22, padding: 10}}>{this.props.data.title.toUpperCase()}</Include.Text>
              </View>
            </LinearGradient>
          : null}
          <ImageBackground source={{uri: 'https://media.heyu.asia/uploads/new-image-service/2021-10-27-hotnewbase.png'}} style={{width: '100%', maxHeight: Define.constants.heightScreen*0.46, borderBottomLeftRadius: 12, borderBottomRightRadius: 12}} imageStyle={{borderBottomLeftRadius: 12, borderBottomRightRadius: 12}}>
            <View style={{ backgroundColor: '#fff', paddingHorizontal: 16, margin: 16, borderRadius: 12, shadowColor: '#000000', shadowOpacity: 0.2, shadowOffset: { height: -1, width: 0 }, shadowRadius: 1, maxHeight: '92%'}}>
                <ScrollView showsVerticalScrollIndicator={false}
                  contentContainerStyle={{paddingBottom: 20}}>
                  {this.props.data.contentType ?
                    (Platform.OS === 'android' && nativeVersion > 10021) || (Platform.OS === 'ios' && nativeVersion > 100016) ?
                      <HTML source={{ html: htmlContent }} contentWidth={Define.constants.widthScreen} imagesMaxWidth={Define.constants.widthScreen * 0.7} baseStyle={{fontSize: 16}}/>
                      :
                      <HTML html={htmlContent} imagesMaxWidth={Define.constants.widthScreen * 0.7} baseFontStyle={{fontSize: 16}}/>
                    :
                    <Include.Text style={{fontSize: 16}}>{this.props.data.body}</Include.Text>
                  }
                </ScrollView>
              <View style={{backgroundColor: 'transparent', paddingVertical: 8, width: Define.constants.widthScreen*0.9-64, flexDirection: 'row'}}>
                {this.props.data.link || this.props.data.actionURL ?
                  <TouchableOpacity
                    style={{flex: 1, paddingRight: 8, justifyContent: 'center', alignItems: 'center'}}
                    onPress={() => {
                      popupActions.popPopup();
                      this.props.setShowHotNews && this.props.setShowHotNews(true);
                      if(this.props.data.link) {
                        globalVariableManager.navigatorManager.handleNavigator(this.props.data.link, this.props.data.extras || {});
                      } else if(this.props.data.actionURL) {
                        globalVariableManager.navigatorManager.handleNavigator('WebviewScreen', {
                          source: this.props.data.actionURL
                        })
                      }
                    }}
                  >
                    <LinearGradient start={{x: 0, y: 0}} end={{x: 1, y: 0}} colors={['#72BCF2', '#0070C1']} style={{height: 50, justifyContent: 'center', alignItems: 'center', flexDirection: 'row', borderRadius: 36, paddingHorizontal: 4, width: (Define.constants.widthScreen*0.9-64)/2 - 8}}>
                      <View style={{width: (Define.constants.widthScreen*0.9-64)/2 - 16, height: 42, borderWidth: 2, borderRadius: 20, borderColor: '#ffff', justifyContent: 'center', alignItems: 'center', flexDirection: 'row'}}>
                        <Include.Text style={{color: '#fff', fontSize: 16, marginRight: 6}}>
                          Chi tiết
                        </Include.Text>
                        <HeyUIcon name={'fi-br-angle-right'} color={'#fff'} size={14}/>
                      </View>
                    </LinearGradient>
                  </TouchableOpacity>
                : null }
                <TouchableOpacity
                  style={{flex: 1, paddingLeft: 8, justifyContent: 'center', alignItems: 'center'}}
                  onPress={() => {
                    popupActions.popPopup();
                    this.props.setShowHotNews && this.props.setShowHotNews(true);
                  }}
                >
                  <LinearGradient start={{x: 0, y: 0}} end={{x: 1, y: 0}} colors={['#666666', '#2E2E2E']} style={{height: 50, justifyContent: 'center', alignItems: 'center', flexDirection: 'row', borderRadius: 36, paddingHorizontal: 4, width: (Define.constants.widthScreen*0.9-64)/2 - 8}}>
                    <View style={{width: (Define.constants.widthScreen*0.9-64)/2 - 16, height: 42, borderWidth: 2, borderRadius: 20, borderColor: '#ffff', justifyContent: 'center', alignItems: 'center'}}>
                      <Include.Text style={{color: '#fff', fontSize: 16}}>
                        Đóng
                      </Include.Text>
                    </View>
                  </LinearGradient>
                </TouchableOpacity>
              </View>
            </View>
          </ImageBackground>
        </View>
      </View>
    );
    return(content)
  }
}

// NOTE : TODO :now can't use connect ( popupManager can't read config)
/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
// function selectActions(state) {
//   return {}
// }

// export default connect(selectActions, undefined, undefined, {withRef: true})(TemplatePopup);

export default HotNewsPopup
