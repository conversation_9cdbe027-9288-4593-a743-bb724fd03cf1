
var _ = require('lodash')
//LIB
import React  from 'react';
import {
  View,
  InteractionManager,
  Linking,
  Platform
} from 'react-native';

var RectButton = require('../elements/RectButton');
// import { connect } from 'react-redux';

//action

//components
var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
var Util = require('../../Util/Util');
var Include = require('../../Include');
import DefaultPopup from './DefaultPopup'
import WebviewCommentPopup from './WebviewCommentPopup'
var ButtonWrap= require('../elements/ButtonWrap');

import Popup from './Popup'

var {popupActions,popupConst} = require('./PopupManager');
var {globalVariableManager}= require('../modules/GlobalVariableManager');

class JoinGroupPopup extends Popup{
  static componentName = 'JoinGroupPopup'
  static config=
  {
    ...Popup.config,
  }
  static containerStyle={
    ...Popup.containerStyle,
  }
  static defaultProps = {
    disableClose:true,
  }
  // static defaultProps = {}
  // static propTypes = {}
  constructor(props){
    super(props);
    this.state = _.merge(this.state,
    {})
  }
  renderContent(){
    var content = null;
    var {feed_id} = this.props;
    let groupID = feed_id.split('_');
    let groupLink =`https://facebook.com/groups/${groupID[0]}`;

    content = (
      <DefaultPopup
        title =  {''}
        buttonTitle2 = {'Gia nhập'}
        buttonTitle = {'Quay lại'}
        onPress2 = {()=>{
          popupActions.popPopup();

          popupActions.setRenderContentAndShow(WebviewCommentPopup, {
            uri: groupLink
          });
        }}
        onPress = {()=>{
          popupActions.popPopup();
        }}>
          <Include.Text style={{fontSize:15, ...Platform.select({
            ios: {
              textAlign: 'justify',
            },
            android: {
              textAlign: 'center'
            },
          })}}>
            Có vẻ như bạn chưa gia nhập Group này. Vì thế bạn không thể nhận đơn hàng nhanh hay tham gia bình luận, vui lòng nhấp vào tham gia để gia nhập group. Xin cảm ơn.
          </Include.Text>
      </DefaultPopup>
    )
    return(content)
  }
}

// NOTE : TODO :now can't use connect ( popupManager can't read config)
/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
// function selectActions(state) {
//   return {}
// }

// export default connect(selectActions, undefined, undefined, {withRef: true})(JoinGroupPopup);

export default JoinGroupPopup
