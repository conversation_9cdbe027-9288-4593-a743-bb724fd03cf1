var _ = require('lodash');
//LIB
import React from 'react';
import {
  View,
  ActivityIndicator,
  Vibration,
  Image,
  TouchableOpacity,
  ScrollView,
  Platform,
  StatusBar,
  SafeAreaView,
} from 'react-native';

var RectButton = require('../elements/RectButton');
import {connect} from 'react-redux';
var {Actions, ActionConst} = require('react-native-router-flux');
import LinearGradient from 'react-native-linear-gradient';
import MapView from 'react-native-maps';
var locationManager = require('../modules/LocationManager');
//action
import BikeActions_MiddleWare from '../../actions/BikeActions_MiddleWare';
import RDActions from '../../actions/RDActions';
import * as Animatable from 'react-native-animatable';

//components
var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
var Util = require('../../Util/Util');
var Include = require('../../Include');
import Video, {VideoRef} from 'react-native-video';
import Popup from './Popup';
import PenguinBoldIcon from '../elements/PenguinBoldIcon/PenguinBoldIcon';
import PenguinLinearIcon from '../elements/PenguinLinearIcon/PenguinLinearIcon';
var {popupActions} = require('./PopupManager');
var {globalVariableManager} = require('../modules/GlobalVariableManager');
import HeyUIcon from '../elements/HeyUIcon/HeyUIcon';
class VideoPopup extends Popup {
  static componentName = 'VideoPopup';
  static config = {
    ...Popup.config,
    tapToExit: false,
    noDetectGes: true,
    movePopupIn: contentObject => {
      var contentRefObject = contentObject.objRef;
      return contentRefObject.fadeIn(300);
    },
    movePopupOut: contentObject => {
      var contentRefObject = contentObject.objRef;
      return contentRefObject.fadeOut(300);
    },
  };
  static containerStyle = {
    ...Popup.containerStyle,
  };
  static defaultProps = {
    disableClose: true,
  };
  // static defaultProps = {}
  // static propTypes = {}
  constructor(props) {
    super(props);
    this.state = _.merge(this.state, {
      loading: true,
    });

    this.intervalRemove = null;
    this.intervalAccept = null;
  }

  renderContent() {
    var content = null;
    const {feedSystem, path, index} = this.props;
    content = (
      <View
        style={{
          flex: 1,
          width: Define.constants.widthScreen,
          backgroundColor: 'rgba(0,0,0,0)',
          alignItems: 'center',
          justifyContent: 'center',
        }}>
        <View
          style={{
            height: Define.constants.heightScreen * 0.7,
            width: Define.constants.widthScreen * 0.92,
            backgroundColor: '#0000005a',
            borderRadius: 12,
          }}>
          <SafeAreaView style={{right: 16, top: 16, position: 'absolute', zIndex: 13, elevation:13}}>
            <TouchableOpacity
              onPress={() => {
                popupActions.popPopup();
              }}
              style={{
                alignItems: 'center',
                justifyContent: 'center',
                width: 32,
                height: 32,
                backgroundColor: '#e74c3c',
                borderRadius: 32,
              }}
            >
              <HeyUIcon name='fi-rr-cross-small' size={20} color='#fff' style={{marginTop: -2}}/>
            </TouchableOpacity>
          </SafeAreaView>
          <Video
            // Can be a URL or a local file.
            source={{
              bufferConfig: {
                minBufferMs: 2500,
                maxBufferMs: 5000,
                bufferForPlaybackMs: 2500,
                bufferForPlaybackAfterRebufferMs: 2500,
              },
              uri: path
            }}
            controls={true}
            style={{flex:1}}
          />
        </View>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginHorizontal: 16,
          }}>
          {this.props.isDeclineFile ? (
            <TouchableOpacity
              style={{
                alignSelf: 'center',
                alignItems: 'center',
                justifyContent: 'center',
                marginTop: 10,
                backgroundColor: '#E93940',
                borderRadius: 8,
                width: 50,
                height: 36,
                marginRight: 8,
              }}
              onPress={() => {
                popupActions.popPopup();
                this.props.declineFile && this.props.declineFile(index, true);
              }}>
              <PenguinLinearIcon name={'trash'} size={16} color={'#fff'} />
            </TouchableOpacity>
          ) : null}
          {/* <TouchableOpacity
            style={{
              alignSelf: 'center',
              alignItems: 'center',
              justifyContent: 'center',
              marginTop: 10,
              backgroundColor: '#2e86de',
              borderRadius: 8,
              width: Define.constants.widthScreen * 0.5,
              height: 36,
            }}
            onPress={() => {
              popupActions.popPopup();
            }}>
            <Include.Text
              style={{
                color: '#fff',
                fontFamily: Define.constants.fontBold500,
                fontSize: 16,
              }}>
              Đóng
            </Include.Text>
          </TouchableOpacity> */}
        </View>
      </View>
    );
    return content;
  }

  componentDidMount() {}

  componentWillUnmount() {}
}
// NOTE : TODO :now can't use connect ( popupManager can't read config)
/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  return {
    navigator: state.Navigator,
    appState: state.AppState,
    feedSystem: state.FeedSystem,
  };
}

export default connect(selectActions, undefined, undefined, {withRef: true})(
  VideoPopup,
);
