
var _ = require('lodash')
//LIB
import React from 'react';
import {
  View,
  TouchableOpacity,
  Image,
  TextInput,
  Text,
  Keyboard,
  Vibration,
  Platform
} from 'react-native';

import { connect } from 'react-redux';
import LinearGradient from 'react-native-linear-gradient';

//action

//components
var Define = require('../../Define');
var Include = require('../../Include');

var { Actions } = require('react-native-router-flux');
import Popup from './Popup'
import RDActions from '../../actions/RDActions'

var { popupActions, popupConst } = require('./PopupManager');
import PenguinBoldIcon from '../elements/PenguinBoldIcon/PenguinBoldIcon';
import {Ionicons as Icon} from '@react-native-vector-icons/ionicons';
import Voice from '@react-native-voice/voice';
import LottieView from 'lottie-react-native';

const STEP = {
  STOP: 0,
  RUNNING: 1,
}
class RecordPopup extends Popup {
  static componentName = 'RecordPopup'
  static config =
    {
      ...Popup.config,
      group: popupConst.PERMISSIONS_GROUP,
      movePopupIn: (contentObject) => {
        var contentRefObject = contentObject.objRef;
        return contentRefObject.fadeInUp(200);
      },
      movePopupOut: (contentObject) => {
        var contentRefObject = contentObject.objRef;
        return contentRefObject.fadeOutDown(300);
      },
      tapToExit: false,
    }
  static containerStyle = {
    ...Popup.containerStyle,
  }
  static defaultProps = {
    disableClose: true,
  }
  // static defaultProps = {}
  // static propTypes = {}
  constructor(props) {
    super(props);
    this.state = _.merge(this.state,
      {
        text: '',
        textDisplay: '',
        step: STEP.STOP,
        bottom: 0,
        focusContent: false,
        isMoving: false
      })
    this.onPressStartRecording = this.onPressStartRecording.bind(this)
    this.onPressStopRecording = this.onPressStopRecording.bind(this)
    Voice.onSpeechPartialResults = this.onSpeechPartialResults.bind(this);
    Voice.onSpeechStart = this.onSpeechStartHandler.bind(this);
    Voice.onSpeechEnd = this.onSpeechEndHandler.bind(this);
    this.prevText = ''
    this.keyboardWillHide = this.keyboardWillHide.bind(this)
    this.keyboardWillShow = this.keyboardWillShow.bind(this)
    this.debounceScrollEnd = _.debounce(this.handleScrollEnd, 300);
  }

  handleScrollEnd = () => {
    this.setState({
      isMoving: false,
    })
  };

  onSpeechPartialResults(e) {

    if(e && e.value && e.value.length) {
      if(e.value[0] === '') {

        if(this.state.text) {
          this.prevText += ` ${this.state.text}`
        }
        this._content.setNativeProps({
          text: this.prevText
        });
        this.setState({text: ''})
      } else {
        if (this.state.step !== STEP.STOP) {
          let data = e.value[0].trim()
          this.setState({text: data})
          this._content.setNativeProps({
            text: `${this.prevText} ${data}`
          });
        }
      }
    }
  }

  async startRecognizing(initial) {
    try {
      if(Platform.OS === 'ios' && !initial) {
        this.prevText = this.state.textDisplay;
      }
      await Voice.start('vi-VN');
      this._content.blur();
      Keyboard.dismiss()
    } catch (error) {
      console.error('Lỗi khi bắt đầu nhận dạng: ', error);
    }
  }
  async stopRecognizing() {
    try {
      await Voice.stop();
      this.prevText += ` ${this.state.text}`
      this._content.setNativeProps({
        text: this.prevText
      });
      this.setState({text: ''})
    } catch (error) {
      console.error('Lỗi khi dừng nhận dạng: ', error);
    }
  }

  onSpeechStartHandler =  async () => {
    console.log('Speech started');
  };

  onSpeechEndHandler =  async () => {
    console.log('Speech ended');
    if (Platform.OS === 'android') {
      this.prevText += ` ${this.state.text}`
      this._content.setNativeProps({
        text: this.prevText
      });
      this.setState({text: ''})
      this.onPressStartRecording(false)
    } else {
      if (this.state.step !== STEP.STOP) {
        this.onPressStartRecording(false)
      }
    }
  };

  renderContent() {
    var content = null;
    content = (
       <View style={{
        backgroundColor: 'rgba(0,0,0,0)',
        justifyContent: 'flex-end',
        alignItems: 'center',
        alignSelf: 'stretch',
        width: Define.constants.widthScreen,
        height: Platform.OS === 'ios' ? Define.constants.heightScreen : '100%',
        position: Platform.OS === 'ios' ? 'absolute' :'relative' ,
        bottom: Platform.OS === 'ios' ? this.state.bottom : 0,
      }}>
        <TouchableOpacity
          onPress={() => {
            this.props.checkSafeAreaView?.()
            popupActions.popPopup()}}
          style={{
            flex: 1,
            width: Define.constants.widthScreen,
            backgroundColor: 'transparent'
          }}
        />
        <View style={{ backgroundColor:'#fff', height: this.state.bottom && Platform.OS === 'ios' ? (Define.constants.heightScreen - this.state.bottom) * 0.9 : Define.constants.heightScreen * 0.65, width:'100%', borderTopRightRadius: 12, borderTopLeftRadius:12 }}>
          <Include.Text style={{color: '#2E3236', fontFamily:Define.constants.fontBold600, fontSize: 18, textAlign: 'center', marginVertical: 16}}>Nhập văn bản bằng giọng nói</Include.Text>
          <TouchableOpacity
          onPress={() => {
            this.props.checkSafeAreaView?.()
            popupActions.popPopup()}}
          style={{position:'absolute', right: 8, top: 18, width: 30, height: 30, zIndex: 11}}>
            <PenguinBoldIcon name={'close-circle'} size={24} color={'#929394'} />
          </TouchableOpacity>
          <View style={{flex: 1, alignItems:'center', width:'100%', zIndex: 10}}>
            <TextInput
              ref={ref => (this._content = ref)}
              style={{
                flex: 1,
                color: 'rgba(46, 50, 54, 1)',
                backgroundColor: '#F1F7FB',
                fontSize: 18,
                fontFamily: Define.constants.fontBold500,
                textAlignVertical: 'top',
                marginHorizontal: 16,
                paddingHorizontal: 8,
                marginBottom: 16,
                borderRadius: 12,
                width:'92%'
              }}
              multiline
              returnKeyType={'done'}
              onChangeText={text => {
                this.setState({textDisplay: text})
                if (Platform.OS === 'android') {
                  this.prevText = text
                }
              }}
              value={this.state.textDisplay}
              onSubmitEditing={() => {
              }}
              onFocus={() => {
                this.setState({focusContent: true});
                if(this.state.step === STEP.RUNNING) {
                  this.setState({
                    step: STEP.STOP
                  })
                  this.stopRecognizing();
                }
              }}
              editable={!this.state.isMoving || this.state.focusContent}
              onBlur={() => {
                if (this.state.focusContent) {
                  this.setState({
                    focusContent: false,
                    isMoving: false
                  });
                }
              }}
              onScroll={() => {
                if (!this.state.isMoving) {
                  this.setState({
                    isMoving: true
                  })
                }
                this.debounceScrollEnd();
              }}
              onTouchEnd={() => {
                this.setState({
                  isMoving: false
                })
              }}
            />
            {!this.state.textDisplay && !this.prevText.trim() && !this.state.text ? <View pointerEvents={'none'} style={{position:'absolute', top: 50, alignItems:'center', paddingHorizontal: 16}}>
              <Text allowFontScaling={false} numberOfLines={2} style={{color:'rgba(0, 124, 254, 0.5)',fontFamily:Define.constants.fontBold600, fontSize: 28, alignSelf: 'center', textAlign:'center'}}>
                {this.state.step === STEP.RUNNING ? 'Hãy bắt đầu nói...' : 'Hãy chạm vào nút ghi âm để bắt đầu nói'}
              </Text>
            </View>:null}
          </View>
          {this.state.step === STEP.STOP ?
          <View style={{flexDirection:'row', justifyContent:'center', marginHorizontal: 16}}>
            {this.prevText && this.prevText.trim() ? <TouchableOpacity
              onPress={this.onRefreshText}
              style={{ alignItems:'center', justifyContent:'center', alignSelf:'center'}}>
                <PenguinBoldIcon name={'rotate-left'} color={'rgba(211, 5, 0, 1)'} size={36} />
            </TouchableOpacity> : null}
            <View style={{flex:1}}>
              <TouchableOpacity
                onPress={this.onPressStartRecording}
                style={{backgroundColor:'#98C2FD47', width:72, height: 72, borderRadius: 36, alignItems:'center', justifyContent:'center', alignSelf:'center'}}>
                <View style={{backgroundColor:'#007CFE', width:60, height: 60, borderRadius: 30, alignItems:'center', justifyContent:'center'}}>
                  <PenguinBoldIcon name={'microphone-2'} color={'#fff'} size={32} />
                </View>
              </TouchableOpacity>
            </View>
            {this.prevText && this.prevText.trim()? <TouchableOpacity
              onPress={() => {
                if(this.props.onRecordComplete) {
                  this.props.checkSafeAreaView?.()
                  this.props.onRecordComplete(Platform.OS === 'android' ? this.prevText : this.state.textDisplay)
                  popupActions.popPopup()
                }
              }}
              style={{alignItems:'center', justifyContent:'center', alignSelf:'center'}}>
                <PenguinBoldIcon name={this.props.iconSend ? this.props.iconSend : 'send-1'} color={'rgba(54, 152, 255, 1)'} size={36} />
            </TouchableOpacity> :null}
          </View>:
          <View style={{ alignItems: 'center', justifyContent: 'center', zIndex: 999, width: '100%' }}>
            <LottieView
              style={{ width: 160, height: 160, position: 'absolute', bottom: -44, alignSelf:'center',}}
              source={require('../../../assets/Animation/rippleEffectRecord')}
              autoPlay
              loop
            />
            <TouchableOpacity
              onPress={this.onPressStopRecording}
              style={{ backgroundColor: '#98C2FD47', width: 72, height: 72, borderRadius: 36, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
              <View style={{ backgroundColor: '#007CFE', width: 60, height: 60, borderRadius: 30, alignItems: 'center', justifyContent: 'center' }}>
                <Icon name={'pause'} color={'#fff'} size={32} />
              </View>
            </TouchableOpacity>
          </View>
          }
          <Include.Text style={{color: '#2E3236', fontFamily:Define.constants.fontBold600, fontSize: 18, textAlign: 'center', marginVertical: 16}}>{this.state.step === STEP.STOP ? 'Chạm để bắt đầu nói' : 'Nói ngay bây giờ... '}</Include.Text>
          {this.state.bottom ?
            <TouchableOpacity
              style={{ position: 'absolute', bottom: 0, right: 0, zIndex: 100, flexDirection: 'row', alignItems: 'center', justifyContent: 'center', borderTopLeftRadius: 5, backgroundColor: '#c9ccd2' }}
              onPress={() => {
                Keyboard.dismiss()
              }}>
              <View style={{ justifyContent: 'center', alignItems: 'center', marginLeft: 10, }}>
                <Image
                  source={{ uri: 'https://media.heyu.asia/uploads/mobiles/2025-02-13-keyboard1.png' }}
                  style={{ width: 24, height: 20 }}
                  tintColor={'#b30000'}
                />
                <PenguinBoldIcon name='arrow-down' style={{ backgroundColor: 'transparent', marginTop: -6 }} color={'#b30000'} size={14} />
              </View>
              <Text allowFontScaling={false} style={{ fontSize: 16, fontFamily: Define.constants.fontBold600, paddingHorizontal: 10, color: '#b30000' }}>Đóng</Text>
            </TouchableOpacity> : null}
        </View>
      </View>
    )
    return (content)
  }

  onRefreshText = ()  => {
    this.prevText = ''
    this.setState({
      text: '',
      textDisplay: ''
    })
    this._content.setNativeProps({
      text: ''
    })
  }

  onPressStartRecording(initial=true) {
    this.setState({ step: STEP.RUNNING },() => {
      if(initial) {
        Vibration.vibrate();
      }
      this.startRecognizing(initial)
    })
  }


  async onPressStopRecording() {
    await this.stopRecognizing()
    Vibration.vibrate();
    this.setState({ step: STEP.STOP })
  }

    keyboardWillShow(e) {
      const heightKeyboard = e.endCoordinates.height;
      if (Platform.OS === 'ios') {
        this.setState({
          bottom: heightKeyboard
        });
      } else {
      }
    }

    keyboardWillHide() {
      this.setState({
        bottom: 0
      });
    }

  componentDidMount() {
    super.componentDidMount()
    if (this.props.content) {
      this.prevText = this.props.content;
      this._content && this._content.setNativeProps({
        text: this.props.content
      })
      if (Platform.OS === 'ios') {
        this.setState({
          textDisplay: this.props.content
        })
      }
    }
    if (Platform.OS === 'android') {
      this.keyboardDidShowSubscription = Keyboard.addListener('keyboardDidShow', this.keyboardWillShow)
      this.keyboardDidHideSubscription = Keyboard.addListener('keyboardDidHide', this.keyboardWillHide)
    } else {
      this.keyboardDidShowSubscription = Keyboard.addListener('keyboardWillShow', this.keyboardWillShow)
      this.keyboardDidHideSubscription = Keyboard.addListener('keyboardWillHide', this.keyboardWillHide)
    }
    this.onPressStartRecording();
  }

  componentWillUnmount() {
    super.componentWillUnmount()
    Voice.destroy().then(Voice.removeAllListeners);
    this.keyboardDidShowSubscription.remove()
    this.keyboardDidHideSubscription.remove()
  }
}

// NOTE : TODO :now can't use connect ( popupManager can't read config)
/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  return {}
}

export default connect(selectActions, undefined, undefined, {withRef: true})(RecordPopup);

// export default RecordPopup