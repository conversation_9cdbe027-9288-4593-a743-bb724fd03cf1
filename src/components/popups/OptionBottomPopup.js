var _ = require('lodash');
//LIB
import React from 'react';
import {
  View,
  InteractionManager,
  Text,
  TouchableOpacity,
  Linking,
  PermissionsAndroid,
  NativeModules,
  Image,
} from 'react-native';

import {connect} from 'react-redux';
//action

//components
var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
var Util = require('../../Util/Util');
var Include = require('../../Include');
var RNIntent = NativeModules.RNIntent;
var ButtonWrap = require('../elements/ButtonWrap');
var CommonUtil = require('../../Util/common');
import BikeActions_MiddleWare from '../../actions/BikeActions_MiddleWare';
import Popup from './Popup';
import DefaultPopup from './DefaultPopup';
import LinearGradient from 'react-native-linear-gradient';
import PenguinBoldIcon from '../elements/PenguinBoldIcon/PenguinBoldIcon';
import PenguinLinearIcon from '../elements/PenguinLinearIcon/PenguinLinearIcon';
var {popupActions, popupConst} = require('./PopupManager');
var {globalVariableManager} = require('../modules/GlobalVariableManager');

class OptionBottomPopup extends Popup {
  static componentName = 'OptionBottomPopup';
  static config = {
    ...Popup.config,
    movePopupIn: contentObject => {
      var contentRefObject = contentObject.objRef;
      return contentRefObject.fadeInUp(200);
    },
    movePopupOut: contentObject => {
      var contentRefObject = contentObject.objRef;
      return contentRefObject.fadeOutDown(200);
    },
  };
  static containerStyle = {
    ...Popup.containerStyle,
  };
  static defaultProps = {
    disableClose: true,
  };
  // static defaultProps = {}
  // static propTypes = {}
  constructor(props) {
    super(props);
    this.state = _.merge(this.state, {
      bottom: 0,
    });
  }
  renderPick = () => {
    return (
      <TouchableOpacity
        onPress={() => {
          popupActions.popPopup();
          this.props.onPressPick && this.props.onPressPick();
        }}>
        <View
          style={{
            backgroundColor: '#007CFE',
            paddingVertical: 12,
            marginHorizontal: 16,
            borderRadius: 16,
            alignItems: 'center',
            marginBottom: 16,
            flexDirection: 'row',
            justifyContent: 'center',
          }}>
          <Text
            style={{
              color: '#fff',
              fontSize: 16,
              textAlign: 'center',
              fontFamily: Define.constants.fontBold500,
            }}>
            {this.props.buttonTitle}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };
  renderChoose = () => {
    return (
      <TouchableOpacity
        onPress={() => {
          popupActions.popPopup();
          this.props.onPressChoose && this.props.onPressChoose();
        }}>
        <View
          style={{
            backgroundColor: '#fff',
            paddingVertical: 12,
            marginHorizontal: 16,
            borderRadius: 16,
            alignItems: 'center',
            flexDirection: 'row',
            justifyContent: 'center',
          }}>
          <Text
            style={{
              color: '#007cfe',
              fontSize: 16,
              textAlign: 'center',
              fontFamily: Define.constants.fontBold500,
            }}>
            {this.props.buttonTitle2}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };
  renderContent() {
    var content = null;
    return (
      <View
        style={{
          width: Define.constants.widthScreen,
          backgroundColor: '#023367',
          borderTopLeftRadius: 16,
          borderTopRightRadius: 16,
          elevation: Define.constants.elevation,
          borderColor: '#000',
          position: 'absolute',
          bottom: this.state.bottom,
          paddingVertical: 16,
        }}>
        {this.props.title ?
        <View style={{borderBottomWidth: 1, borderColor: '#F3F3F3', paddingBottom: 12, marginBottom: 16}}>
          <Text
            style={{
              color: '#fff',
              fontSize: 18,
              textAlign: 'center',
              fontFamily: Define.constants.fontBold500,
            }}>
            {this.props.title}
          </Text>
        </View> : null }
        {this.renderPick()}
        {!this.props.addDocument ? this.renderChoose() : null}
      </View>
    );
  }
}

// NOTE : TODO :now can't use connect ( popupManager can't read config)
/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  return {
    appSetting: state.appSetting,
    user: state.User,
  };
}

export default connect(selectActions, undefined, undefined, {withRef: true})(
  OptionBottomPopup,
);
