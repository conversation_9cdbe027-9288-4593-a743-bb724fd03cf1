
var _ = require('lodash')
//LIB
import React  from 'react';
import {
  View,
  Image
  // InteractionManager
} from 'react-native';

// import { connect } from 'react-redux';

//action

//components
var Define = require('../../Define');
// var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
// var Util = require('../../Util/Util');
var Include = require('../../Include');

var StyleConfig = require('../../Themes/StyleConfig');
var RectButton = require('../elements/RectButton');
var ButtonWrap= require('../elements/ButtonWrap');

import Popup from './Popup'

var {popupActions,popupConst} = require('../popups/PopupManager');
// var {globalVariableManager}= require('../modules/GlobalVariableManager');

class EndSessionPopup extends Popup{
  static componentName = 'EndSessionPopup'
  static config=
  {
    ...Popup.config,
    group:popupConst.POPUP_GROUP,
    tapToExit : true,
    videoMotion:false,
    // movePopupIn:()=>{return new Promise((resolve)=>{resolve()});},
    // movePopupOut:()=>{return new Promise((resolve)=>{resolve()});},
  }
  static containerStyle={
    ...Popup.containerStyle,
    flexDirection:'column',
    justifyContent:'center',
  }
  static defaultProps = {
    disableClose:true,
  }
  // static propTypes = {}
  constructor(props){
    super(props);
    this.state = _.merge(this.state,
    {})
  }
  UNSAFE_componentWillMount(){
    super.UNSAFE_componentWillMount();
    if (this.props.onWillMount) {
      this.props.onWillMount();
    }
  }
  renderPopupContent(){
    var self = this;
    const {title,description,} = self.props;
    var descriptionText = null;
    if(description) {
      descriptionText =(
        <View style={{width:'100%', borderBottomWidth:1, borderBottomColor:'#DCDEE1', padding:10}}>
          <Include.Text style={{left:0,right:0,color:'#000',margin:5,alignSelf:'center',textAlign :'center'}}>{description}</Include.Text>
        </View>
      )
    }
    var titleComponent = null;
    if(title) {
      titleComponent = (
        <View style={[Themes.current.popup.titleWrap]}>
          <Include.Text style={Themes.current.text.popupTitle}>{title}</Include.Text>
        </View>
      );
    }
    var button = null;
      button=(
        <ButtonWrap
          onPress={() => {popupActions.popPopup()}}
        >
          <View style={{backgroundColor:'#fff',flex:1,justifyContent:'center',borderBottomRightRadius: 4, borderBottomLeftRadius:4, height:45}}>
            <Include.Text style={{alignSelf:'center',marginLeft:0,fontSize :14,fontWeight :'bold', color:'#1697B4'}}>OK</Include.Text>
          </View>
        </ButtonWrap>
      )
    return(
      <View style={{backgroundColor:'#fff',margin:15,borderRadius:4,paddingTop:20,...StyleConfig.default.shadownStyle, ...self.props.style}}>
        {titleComponent}
        {descriptionText}
        <View style={{flexDirection:'row', justifyContent: 'space-around', backgroundColor:'#EDEEF0', borderBottomLeftRadius:4, borderBottomRightRadius:4}}>
          {button}
        </View>
      </View>
    )
  }
  componentWillUnmount(){
    super.componentWillUnmount()
    if (this.props.onWillUnmount) {
      this.props.onWillUnmount();
    }
  }
}

/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */

export default EndSessionPopup
