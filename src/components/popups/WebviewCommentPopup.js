
var _ = require('lodash')
//LIB
import React  from 'react';
import {
  View,
  InteractionManager,
  ActivityIndicator,
  Keyboard,
  Platform
} from 'react-native';

import {Ionicons as Icon} from '@react-native-vector-icons/ionicons';
import { connect } from 'react-redux';
import { WebView } from 'react-native-webview';

//action
import UserActions_MiddleWare from '../../actions/UserActions_MiddleWare'

//components
var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
var Util = require('../../Util/Util');
var Include = require('../../Include');
import CookieManager from '@react-native-cookies/cookies';

var ButtonWrap= require('../elements/ButtonWrap');

import Popup from './Popup'

var {popupActions,popupConst} = require('./PopupManager');
var {globalVariableManager}= require('../modules/GlobalVariableManager');

class WebviewCommentPopup extends Popup{
  static componentName = 'WebviewCommentPopup'
  static config=
  {
    ...Popup.config,
    noDetectGes: true
  }
  static containerStyle={
    ...Popup.containerStyle,
  }
  static defaultProps = {
    disableClose:true,
  }
  // static defaultProps = {}
  // static propTypes = {}
  constructor(props){
    super(props);
    this.state = _.merge(this.state,
    {});
  }
  onRefresh(){
    super.onRefresh();
    var {dispatch} = this.props;
  }

  onGetMore(){
    super.onGetMore();
    var {dispatch} = this.props;
  }
  renderContent(){
    var {dispatch} = this.props;
    var content = null;
    content =(
      <View
        ref={(obj)=>{this._commentContainer=obj}}
        style={[styles.container]}>
        <View style={styles.content}>
          <WebView
            javaScriptEnabled ={true}
            source={{uri: this.props.uri || "https://m.facebook.com"}}
            onLoadEnd={() => {
            }}
          />
        </View>

        <ButtonWrap
          onPress={() => {
            popupActions.popPopup();
          }}>
          <View style={styles.exitButton}>
            <Icon name='close-circle' style={styles.exitIcon} />
          </View>
        </ButtonWrap>
      </View>
    )
    return content;
  }
  UNSAFE_componentWillMount(){
  }
  componentWillUnmount() {
    const {dispatch} = this.props;
    const useWebKit = true;

    CookieManager.get('https://m.facebook.com', useWebKit)
      .then((res) => {
        if(res && res.c_user) {
          const keys = Object.keys(res);
          keys.forEach((key) => {
            res[key] = res[key].value;
          })

          dispatch(UserActions_MiddleWare.uploadCookies({cookies: res}))
            .then(() => {
            })
        }
      })
  }
}
let styles = {
  container: {
    width: Define.constants.widthScreen-10,
    height:  Define.constants.heightScreen*9/10
  },
  exitButton: {
    alignSelf: 'center',
    top: 5
  },
  exitIcon: {
    fontSize: 45,
    color: '#000'
  },
  content: {
    backgroundColor: '#fff',
    flex: 1,
    padding: 7,
    borderRadius: 3
  },
}
// NOTE : TODO :now can't use connect ( popupManager can't read config)
/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  return {}
}

export default connect(selectActions, undefined, undefined, {withRef: true})(WebviewCommentPopup);
