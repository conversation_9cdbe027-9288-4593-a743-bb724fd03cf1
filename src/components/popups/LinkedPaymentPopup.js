var _ = require('lodash');
//LIB
import React from 'react';
import {
  View,
  InteractionManager,
  Text,
  TouchableOpacity,
  Linking,
  Image,
  ImageBackground,
  ScrollView,
  StatusBar,
} from 'react-native';

import {connect} from 'react-redux';

//action

//components
var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
var Util = require('../../Util/Util');
var Include = require('../../Include');

var ButtonWrap = require('../elements/ButtonWrap');

import Popup from './Popup';
import HeyUIcon from '../elements/HeyUIcon/HeyUIcon';
import PenguinLinearIcon from '../elements/PenguinLinearIcon/PenguinLinearIcon';

import UserActions_MiddleWare from '../../actions/UserActions_MiddleWare'
import NotificationsActions_MiddleWare from '../../actions/NotificationsActions_MiddleWare';
var {popupActions, popupConst} = require('./PopupManager');
var {globalVariableManager} = require('../modules/GlobalVariableManager');
import { getStatusBarHeight } from 'react-native-status-bar-height';

class LinkedPaymentPopup extends Popup {
  static componentName = 'LinkedPaymentPopup';
  static config = {
    ...Popup.config,
    group: popupConst.POPUP_GROUP,
    tapToExit: true,
    videoMotion: false,
    noDetectGes: true,
    movePopupIn: contentObject => {
      var contentRefObject = contentObject.objRef;
      return contentRefObject.fadeInUp(200);
    },
    movePopupOut: contentObject => {
      var contentRefObject = contentObject.objRef;
      return contentRefObject.fadeOutDown(200);
    },
  };
  static containerStyle = {
    ...Popup.containerStyle,
  };
  static defaultProps = {
    disableClose: true,
  };
  // static defaultProps = {}
  // static propTypes = {}
  constructor(props) {
    super(props);
    this.state = _.merge(this.state, {

      dataNoti: null
    });
  }
  renderNavBar() {
    const statusBarHeight =
      Platform.OS === 'ios'
        ? 0
        : StatusBar.currentHeight;
    return (
      <View
        style={{
          width: Define.constants.widthScreen,
          zIndex: 2,
          backgroundColor: '#fff',
          paddingBottom: 16,
        }}>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <View
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              backgroundColor: '#fff',
              paddingHorizontal: 16,
              paddingTop: statusBarHeight + 8,
            }}>
            <TouchableOpacity
              onPress={() => {
                popupActions.popPopup()
              }}>
              <HeyUIcon name={'fi-sr-arrow-left'} size={24} color={'#012548'} />
            </TouchableOpacity>
          </View>
          <View
            style={{
              backgroundColor: '#fff',
              flexDirection: 'row',
              justifyContent: 'center',
              alignItems: 'center',
              paddingTop: statusBarHeight + 8,
            }}
            pointerEvents={'none'}>
            <Include.Text
              style={{
                fontSize: 18,
                fontFamily: Define.constants.fontBold500,
                color: '#012548',
              }}>
                Chi tiết liên kết
            </Include.Text>
          </View>
        </View>
      </View>
    );
  }

  renderContent() {
    var content = null;
    return (
      <View
        style={{
          width: Define.constants.widthScreen,
          height:'100%',
          backgroundColor: '#f6f6f6',
        }}>
        {this.renderNavBar()}
        <ScrollView showsVerticalScrollIndicator={false} style={{backgroundColor: '#fff', marginTop: 4}}>
          <ImageBackground
            source={Define.assets.Images.cardBanner}
            resizeMode={'stretch'}
            style={{
              overflow: 'hidden',
              shadowColor: '#000',
              shadowOpacity: 0.5,
              paddingHorizontal: 16,
              marginLeft: 16,
              marginTop: 16,
              shadowOffset: { height: 0, width: 0 },
              shadowRadius: 1,
              height: (Define.constants.widthScreen * 396 / 428) * 152 / 396,
              width: Define.constants.widthScreen * 396 / 428,
              borderRadius: 16,
            }}>
            <Image
              style={{width: 64, height: 64, marginTop: 16}}
              source={{uri: this.props.icon}}
              resizeMode={'stretch'}
            />
          </ImageBackground>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              padding: 16,
              borderBottomColor: '#eef0f0',
              borderBottomWidth: 1,
            }}>
            <Text allowFontScaling={false}
              style={{
                flex: 1,
                fontSize: 16,
                fontFamily: Define.constants.fontBold600,
                color: '#012458',
              }}>
              Phương thức thanh toán
            </Text>
            <Text allowFontScaling={false}
              style={{
                fontSize: 16,
                fontFamily: Define.constants.fontBold500,
                color: '#012548',
              }}>
              {this.props.variable && this.props.variable.includes('*') ? 'Thẻ ngân hàng' : this.props.name}
            </Text>
            <Image
              style={{width: 32, height: 32, marginLeft: 8}}
              source={{uri: this.props.icon}}
              resizeMode={'stretch'}
            />
          </View>
          {this.props.variable && this.props.variable.includes('*') ?
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              padding: 16,
              borderBottomColor: '#eef0f0',
              borderBottomWidth: 1,
            }}>
            <Text allowFontScaling={false}
              style={{
                flex: 1,
                fontSize: 16,
                fontFamily: Define.constants.fontBold600,
                color: '#012458',
              }}>
              Số thẻ
            </Text>
            <Text allowFontScaling={false}
              style={{
                fontSize: 16,
                fontFamily: Define.constants.fontBold500,
                color: '#012548',
              }}>
              {'****' + this.props.name.slice(-4)}
            </Text>
            <Image
              style={{width: 32, height: 32, marginLeft: 8}}
              source={{uri: this.props.icon}}
              resizeMode={'stretch'}
            />
          </View>
          : null}
          {this.props.idWithdraw ?
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              padding: 16,
              borderBottomColor: '#eef0f0',
              borderBottomWidth: 1,
            }}>
            <View style={{ flex: 3}}>
              <Text allowFontScaling={false}
                style={{
                  fontSize: 16,
                  fontFamily: Define.constants.fontBold600,
                  color: '#012458',
                  marginBottom: 4,
                }}>
                Hướng dẫn huỷ liên kết
              </Text>
              {/* <Text allowFontScaling={false}
                style={{
                  fontSize: 14,
                  fontFamily: Define.constants.fontBold400,
                  color: '#012548',
                }}>
                Để hủy liên kết dịch vụ, bạn có thể tham khảo thông tin hướng
                dẫn tại đây
              </Text> */}
            </View>
            <TouchableOpacity
              onPress={() => {
                popupActions.popPopup();

                if(this.state.dataNoti){
                globalVariableManager.navigatorManager.handleNavigator(this.state.dataNoti?.link, this.state.dataNoti?.extras)
                } else {
                globalVariableManager.navigatorManager.handleNavigator('DetailNotificationScreen',{_id: this.props.idWithdraw})
                }
              }}
              style={{
                flex: 1,
                borderRadius: 8,
                backgroundColor: '#1589d8',
                alignItems: 'center',
                justifyContent: 'center',
                paddingVertical: 8
                }}>
              <Text allowFontScaling={false}
                style={{
                  fontSize: 14,
                  fontFamily: Define.constants.fontBold400,
                  color: '#fff',
                  textAlign:'center'
                }}>
                Hướng dẫn
              </Text>
            </TouchableOpacity>
          </View>
          : null}
        </ScrollView>
        {this.props.variable && this.props.variable.includes('*') ?
        <View style={{padding: 16, backgroundColor: '#fff'}}>
          <TouchableOpacity
            onPress={() => {
              this.props.onPress && this.props.onPress()
            }}
            style={{ paddingVertical: 16, backgroundColor: '#FFEBEF', borderRadius: 16, alignItems: 'center'}}>
            <Text allowFontScaling={false} style={{color: '#E93940', fontSize: 18, fontFamily: Define.constants.fontBold600}}>Hủy liên kết</Text>
          </TouchableOpacity>
        </View>
        : null}
      </View>
    );
  }

  getDetailNoti = () => {
    this.props.dispatch((NotificationsActions_MiddleWare.getDetailNotification({ _id: this.props.idWithdraw })))
      .then((result) => {
        this.setState({dataNoti: result.res.data?.data});
      })
  }

  componentDidMount() {
    super.componentDidMount();
    this.getDetailNoti()
  }
}

// NOTE : TODO :now can't use connect ( popupManager can't read config)
/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  return {
    user: state.User,
  };
}

export default connect(
  selectActions,
  undefined,
  undefined,
  {withRef: true},
)(LinkedPaymentPopup);
