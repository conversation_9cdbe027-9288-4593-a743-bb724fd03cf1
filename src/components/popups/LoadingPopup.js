
var _ = require('lodash')
//LIB
import React from 'react';
import {
  View,
  TouchableOpacity,
  Animated,
  Easing
} from 'react-native';
import LottieView from 'lottie-react-native';
import { connect } from 'react-redux';

//action
var AppStateActions_MiddleWare = require('../../actions/AppStateActions_MiddleWare');

//components
var Define = require('../../Define');
var Themes = require('../../Themes');
var Include = require('../../Include');

var ButtonWrap = require('../elements/ButtonWrap');

import Popup from './Popup'

var { popupActions, popupConst } = require('./PopupManager');
var StyleConfig = require('../../Themes/StyleConfig');
var CommonUtil = require('../../Util/common');

class LoadingPopup extends Popup {
  static componentName = 'LoadingPopup'
  static config =
    {
      group: popupConst.LOADING_GROUP,
      movePopupIn: (contentObject) => {
        var contentRefObject = contentObject.objRef;
        return contentRefObject.fadeIn(300);
      },
      movePopupOut: (contentObject) => {
        var contentRefObject = contentObject.objRef;
        return contentRefObject.fadeOut(800);
      }
    }
  static containerStyle = {
    // ...Popup.containerStyle,
  }
  static defaultProps = {
    disableClose: true,
  }
  // static defaultProps = {}
  // static propTypes = {}
  constructor(props) {
    super(props);
    this.state = _.merge(this.state, {})
  }

  renderPopupContent() {
    let content = 'đơn hàng'
    if (this.props.isBike) {
      content = 'chuyến đi'
    }
    else if (this.props.isHireDriver) {
      content = 'đơn'
    }
    else if (this.props.isHeyClean) {
      content = 'đơn dọn dẹp'
    }
    else if (this.props.isHeyCare) {
      content = 'đơn chăm sóc'
    }
    return (
      <View style={{ position: 'absolute', alignItems: 'center', justifyContent: 'center', top: 0, left: 0, right: 0, bottom: 0, alignItems: 'center', backgroundColor: '#fff' }}>
        <View style={{ backgroundColor: '#fff', alignItems: 'center', justifyContent: 'center', borderRadius: 6, padding: 16, width: Define.constants.widthScreen * 0.8 }}>
        {this.props.isHeyClean ?
            <LottieView source={require('../../../assets/Animation/animationCreatOrderCleaning')} style={{width: Define.constants.widthScreen * 0.5, height: Define.constants.widthScreen * 0.5}} autoPlay/> :
            this.props.isHeyCare ?
            <LottieView source={require('../../../assets/Animation/animationCreatOrderHeycare')} style={{width: Define.constants.widthScreen * 0.5, height: Define.constants.widthScreen * 0.5}} autoPlay/> :
            <LottieView source={require('../../../assets/Animation/animationSearchingDriver')} style={{width: Define.constants.widthScreen * 0.5, height: Define.constants.widthScreen * 0.5}} autoPlay/>}
          <Include.Text style={{ color: '#161616', fontSize: 24, fontWeight: '500', textAlign: 'center', marginVertical: 16 }}>Đang xử lý {content}</Include.Text>
        </View>
      </View>
    );
  }

  componentDidMount = () => {
    this.props.dispatch(AppStateActions_MiddleWare.setStatusBarColor('#fff'));

    setTimeout(() => {
      popupActions.popPopup(0, true, popupConst.LOADING_GROUP, false, []);
    }, 3050);
  }

  componentWillUnmount() {
    this.props.dispatch(AppStateActions_MiddleWare.setStatusBarColor(popupActions.getPopupStack().length ? '#fff' : 'transparent'));
  }
}

// NOTE : TODO :now can't use connect ( popupManager can't read config)
/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  return { appState: state.AppState }
}

export default connect(selectActions, undefined, undefined, { withRef: true })(LoadingPopup);