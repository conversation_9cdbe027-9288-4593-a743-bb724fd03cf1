
var _ = require('lodash')
//LIB
import React  from 'react';
import {
  View,
  Image,
  TouchableOpacity,
  Text,
  // InteractionManager
} from 'react-native';

// import { connect } from 'react-redux';

//action

//components
var Define = require('../../Define');
// var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
// var Util = require('../../Util/Util');
var Include = require('../../Include');

var StyleConfig = require('../../Themes/StyleConfig');
var RectButton = require('../elements/RectButton');
var ButtonWrap= require('../elements/ButtonWrap');
import PenguinLinearIcon from '../elements/PenguinLinearIcon/PenguinLinearIcon';

import Popup from './Popup'

var {popupActions,popupConst} = require('../popups/PopupManager');
// var {globalVariableManager}= require('../modules/GlobalVariableManager');

class DefaultPopup extends Popup{
  static componentName = 'DefaultPopup'
  static config=
  {
    ...Popup.config,
    group:popupConst.POPUP_GROUP,
    tapToExit : true,
    videoMotion:false,
    // movePopupIn:()=>{return new Promise((resolve)=>{resolve()});},
    // movePopupOut:()=>{return new Promise((resolve)=>{resolve()});},
  }
  static containerStyle={
    ...Popup.containerStyle,
    flexDirection:'column',
    justifyContent:'center',
  }
  static defaultProps = {
    disableClose:true,
  }
  // static propTypes = {}
  constructor(props){
    super(props);
    this.state = _.merge(this.state,
    {})
  }
  UNSAFE_componentWillMount(){
    super.UNSAFE_componentWillMount();
    if (this.props.onWillMount) {
      this.props.onWillMount();
    }
  }
  renderPopupContent(){
    var self = this;
    const {disableClose,title,description,description2,buttonTitle,onPress,buttonTitle2,onPress2,buttonTitle3,onPress3,onPressPopup, imageUrl, rejectJob} = self.props;
    var descriptionText = null;
    if(description) {
      descriptionText =(
        <View style={{width:'100%', paddingTop:10}}>
          <Text allowFontScaling={false} style={{fontSize: 16, fontFamily: Define.constants.fontBold400, color:'#131313'}}>{description}</Text>
        </View>
      )
    }
    var rejectJobText = null;
    if(rejectJob) {
      rejectJobText =(
        <View style={{width:'100%', paddingTop:10, backgroundColor:'#FF9D0A26', borderRadius:16, flexDirection:'row', alignItems:'center',padding:12, marginTop:12}}>
          <PenguinLinearIcon name={'info-circle'} size={24} color={'#1589D8'}/>
          <Text allowFontScaling={false} numberOfLines={3} style={{fontSize: 16, fontFamily: Define.constants.fontBold400, color:'#131313', marginRight:24, marginLeft:6}}>{rejectJob} <Text allowFontScaling={false} numberOfLines={2} style={{fontSize: 16, fontFamily: Define.constants.fontBold600, color:'#1589D8', marginRight:16}}>quản lý ca</Text></Text>
        </View>
      )
    }
    var description2Text = null;
    if (description2) {
      description2Text = (<View style={{ width: '100%' }}>
        <Text allowFontScaling={false} style={{ fontSize: 16, fontFamily: Define.constants.fontBold400, color: '#131313' }}>{description2}</Text>
      </View>)
    }
    var titleComponent = null;
    if(title) {
      titleComponent = (
        <View style={{justifyContent: 'center'}}>
          <Text allowFontScaling={false} style={{fontSize: 20, fontFamily: Define.constants.fontBold600, color: '#131313'}}>{title}</Text>
          {closeButton}
        </View>
      );
    }
    var closeButton = null;
    if (!disableClose) {
      closeButton=(
        <ButtonWrap onPress={()=>{
              popupActions.popPopup(undefined,undefined,undefined,undefined,[popupConst.INFO_GROUP]);
            }}>
          <View style={{position: 'absolute', top: 0, bottom: 0,left:0,right:0, justifyContent: 'flex-end',padding:3}}>
            <Image tintColor={'#000'} style={Themes.current.image.closeIcon} source={Define.assets.Home.close}/>
          </View>
        </ButtonWrap>
      )
    }
    var button = null;
    if (buttonTitle) {
      button=(
        <TouchableOpacity
          activeOpacity={0.6}
          style={{ flex: 1 }}
          onPress={() => {onPress();}}
        >
          <View
            style={{
              flexDirection: 'row',
              backgroundColor:'#1589D8',
              height: 48,
              borderRadius: 16,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <Text allowFontScaling={false} style={{fontSize: 16, fontFamily: Define.constants.fontBold500, color:'#fff'}}>{buttonTitle}</Text>
          </View>
        </TouchableOpacity>
      )
    }
    var button2 = null;
    if (buttonTitle2) {
      button2=(
        <TouchableOpacity
          activeOpacity={0.6}
          style={{ flex: 1 }}
          onPress={() => {onPress2();}}
        >
          <View
            style={{
              backgroundColor:'#fff',
              borderWidth: 1,
              borderColor: '#1589D8',
              borderRadius: 16,
              alignItems: 'center',
              justifyContent:'center',
              height: 48,
              marginRight: 10,
            }}>
            <Text allowFontScaling={false} style={{fontSize: 16, fontFamily: Define.constants.fontBold500, color:'#1589D8'}}>{buttonTitle2}</Text>
          </View>
        </TouchableOpacity>
      )
    }
    var button3 = null;
    if (buttonTitle3) {
      button3=(
        <TouchableOpacity
          activeOpacity={0.6}
          style={{ flex: 1 }}
          onPress={() => {onPress3();}}
        >
          <View
            style={{
              flexDirection: 'row',
              backgroundColor:'#fff',
              height: 48,
              borderRadius: 16,
              alignItems: 'center',
              justifyContent: 'center',
              borderWidth: 1,
              borderColor: '#1589D8',
              marginRight: 10,
            }}>
            <Text allowFontScaling={false} style={{fontSize: 16, fontFamily: Define.constants.fontBold500, color:'#1589d8'}}>{buttonTitle3}</Text>
          </View>
        </TouchableOpacity>
      )
    }
    let image = null;
    if (imageUrl) {
      image = (
        <View style={{height: Define.constants.heightScreen * 0.4, alignSelf: 'center', marginTop : 4}}>
          <Image
            style={{
              flex: 1,
              width: Define.constants.widthScreen * 0.8,
              height: null,
              resizeMode: 'contain'
            }}
            source={{uri: imageUrl}}
          />
        </View>
      )
    }

    if (onPressPopup && !(buttonTitle&&buttonTitle2)) {
      return(
        <ButtonWrap onPress={()=>{
            onPressPopup();
          }}>
          <View style={{backgroundColor:'#fff', padding: 16, width: Define.constants.widthScreen * 0.9, borderRadius: 6,  justifyContent:'center', alignItems:'center'}}>
            {titleComponent}
            {descriptionText}
            {rejectJobText}
            {description2Text}
            {self.props.children}
            {image}
            {button && button2 ?
            <View style={{ flexDirection:'row', alignItems: 'center', justifyContent: 'space-around', marginTop: 16}}>
              {button2}
              {button}
            </View>
            :
            <View style={{ flexDirection: 'row', marginTop: 16 }}>
              {button}
            </View>
            }
          </View>
        </ButtonWrap>
      )
    }
    else{
      return(
        <View style={{ backgroundColor:'#fff', padding: 16, width: Define.constants.widthScreen * 0.9, borderRadius: 6,  justifyContent:'center', alignItems:'center'}}>
          {titleComponent}
          {descriptionText}
          {rejectJobText}
          {description2Text}
          {self.props.children}
          {image}
          {button && button2 ?
            <View style={{ flexDirection:'row', alignItems: 'center', justifyContent: 'space-around', marginTop: 16}}>
              {button3 ? button3 : null}
              {button2}
              {button}
            </View>
            :
            <View style={{ flexDirection: 'row', marginTop: 16, alignItems: 'center', justifyContent: 'center' }}>
              {button}
            </View>
            }
        </View>
      )
    }
  }
  componentWillUnmount(){
    super.componentWillUnmount()
    if (this.props.onWillUnmount) {
      this.props.onWillUnmount();
    }
  }
}

/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
// function selectActions(state) {
//   return {}
// }

// export default connect(selectActions, undefined, undefined, {withRef: true})(DefaultPopup);
export default DefaultPopup