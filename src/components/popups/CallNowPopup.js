
var _ = require('lodash')
//LIB
import React from 'react';
import {
  View,
  TouchableOpacity
} from 'react-native';

import { connect } from 'react-redux';

//action

//components
var Themes = require('../../Themes');
var Include = require('../../Include');

var ButtonWrap = require('../elements/ButtonWrap');

import Popup from './Popup'

var { popupActions, popupConst } = require('./PopupManager');
var StyleConfig = require('../../Themes/StyleConfig');
var CommonUtil = require('../../Util/common');
var Define = require('../../Define');
class CallNowPopup extends Popup {
  static componentName = 'CallNowPopup'
  static config =
    {
      ...Popup.config,
      group: popupConst.POPUP_GROUP,
      tapToExit: true,
    }
  static containerStyle = {
    ...Popup.containerStyle,
  }
  static defaultProps = {
    disableClose: true,
  }
  // static defaultProps = {}
  // static propTypes = {}
  constructor(props) {
    super(props);
    this.state = _.merge(this.state,
      {})
    this.callNow = this.callNow.bind(this);
  }
  callNow() {
    CommonUtil.callNormal(this.props.phone)
    popupActions.popPopup();
  }
  renderPopupContent() {
    var button = null;
    button = (
      <View style={{flexDirection:'row', justifyContent: 'flex-end', marginTop: 16}}>
        <TouchableOpacity
          activeOpacity={0.6}
          onPress={() => { popupActions.popPopup()}}
        >
          <View style={{backgroundColor:'#fff',justifyContent:'center', height:48, marginRight: 10}}>
            <Include.Text style={{fontSize: 16, fontWeight:'500', color:'#1E5D97'}}>Huỷ</Include.Text>
          </View>
        </TouchableOpacity>
        <TouchableOpacity
          activeOpacity={0.6}
          onPress={() => { this.callNow() }}
        >
          <View style={{ backgroundColor:'#1E5D97', height:48, paddingHorizontal: 12, borderRadius: 6, justifyContent: 'center'}}>
            <Include.Text style={{fontSize: 16, fontWeight:'500', color:'#fff'}}>Gọi ngay</Include.Text>
          </View>
        </TouchableOpacity>
      </View>
    )
    return (
      <View style={{backgroundColor:'#fff', padding: 16, width: Define.constants.widthScreen * 0.9, borderRadius: 6}}>
        <View style={{justifyContent: 'center'}}>
          <Include.Text style={{fontSize: 20, fontWeight: '500', color: '#161616'}}>{this.props.title || 'Thông báo'}</Include.Text>
        </View>
        <View style={{width:'100%', paddingTop:10}}>
          <Include.Text style={{fontSize: 15, color:'#696969'}}>{this.props.description}</Include.Text>
        </View>
        {button}
      </View>
    )
  }
}

// NOTE : TODO :now can't use connect ( popupManager can't read config)
/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  return { appState: state.AppState }
}

export default connect(selectActions, undefined, undefined, { withRef: true })(CallNowPopup);
