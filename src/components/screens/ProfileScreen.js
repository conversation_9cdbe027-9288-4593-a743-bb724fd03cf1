
var _ = require('lodash')
const ms = require('ms');
import moment from 'moment';
//LIB
import React from 'react';
import {
  View,
  ScrollView,
  RefreshControl,
  StyleSheet,
  Platform,
  Image,
  TouchableOpacity,
  TextInput,
  Text,
  Keyboard,
  LayoutAnimation,
  SafeAreaView,
  ImageBackground,
  StatusBar
} from 'react-native';

var { Actions } = require('react-native-router-flux');
import { connect } from 'react-redux';
import { Hoshi } from 'react-native-textinput-effects';
//action
var NotifyUtil = require('../../Util/notify')

//components
var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
var Util = require('../../Util/Util');
var UserUtil = require('../../Util/user');
var Include = require('../../Include');

var { popupActions } = require('../popups/PopupManager');
var { globalVariableManager } = require('../modules/GlobalVariableManager');

var ButtonWrap = require('../elements/ButtonWrap');
import HeyUIcon from '../elements/HeyUIcon/HeyUIcon';
import { TabActions, CommonActions } from '@react-navigation/native';
import DatePicker from 'react-native-date-picker';
import RDActions from '../../actions/RDActions';
import SocketOrderManager from '../modules/SocketOrder';


//screens
import Screen from './Screen'

// popups
import DefaultPopup from '../popups/DefaultPopup';
import FadeDownDefaultPopup from '../popups/FadeDownDefaultPopup'

// actions
import UserActions_MiddleWare from '../../actions/UserActions_MiddleWare'
import AppStateActions_MiddleWare from '../../actions/AppStateActions_MiddleWare'
import { Icon, Button, Thumbnail } from 'native-base';
import LinearGradient from 'react-native-linear-gradient';
import * as Animatable from 'react-native-animatable';
import StarRating from 'react-native-star-rating';
import PenguinLinearIcon from '../elements/PenguinLinearIcon/PenguinLinearIcon';
import ChooseBlockAccountPopup from '../popups/ChooseBlockAccountPopup';


//variable


//

class ProfileScreen extends Screen {
  static componentName = 'ProfileScreen'
  static sceneConfig = {
    ...Screen.sceneConfig,
    hideNavBar: true
  }
  // static defaultProps = {}
  // static propTypes = {}
  constructor(props) {
    super(props);
    let { user } = this.props;
    this.state = _.merge(this.state, {
      keyboardShow: false,
      bottom: 0,
      starData: [],
      openDatePicker: false,
      total: 0,
      configChangePhone: {
        isOpen: 0
      },
      heightHeader: null,
      configReferralCode: {
        isOpenProfile: 0
      },
      configShipperAuthenOnline: {
        isOpen: 0
      },
      configRemoveAccount: {
        isOpen: 0,
      }
    })
    this.userInfo = {
      name: _.get(user, 'memberInfo.name', ''),
      phone: _.get(user, 'memberInfo.phones', []).join(', '),
      picture: _.get(user, 'memberInfo.avatar', ''),
      idNumber: _.get(user, 'memberInfo.idNumber', ''),
      level: _.get(user, 'memberInfo.politicalTheoryLevel', ''),
      position: user.memberInfo.positions.length ? user.memberInfo.positions[0].name : '',
      rank: _.get(user, 'memberInfo.rank', ''),
      gender: _.get(user, 'memberInfo.gender', ''),
      eduLevel: _.get(user, 'memberInfo.educationLevel', ''),
      birthday: _.get(user, 'memberInfo.dob', ''),
      email: _.get(user, 'memberInfo.email', ''),
      avatar: _.get(user, 'memberInfo.avatar', ''),
      rankImage: _.get(user, 'memberInfo.rankImage', ''),
      areas :  user.memberInfo.areas.length ? user.memberInfo.areas[0].name : '',
      areaChilds:  user.memberInfo.areas.length ? user.memberInfo.areas[0].areaChilds : [],
      units : user.memberInfo.units.length ? user.memberInfo.units[user.memberInfo.units.length - 1].name : ''
      }
    this.tokenPhone = '';
    this.phoneCode = '';
    this.phoneAuthen = '';
    this.infoFromToken = null
    this.inviteCode = '';

    this.handleUpdateProfile = this.handleUpdateProfile.bind(this);
    this.updateProfile = this.updateProfile.bind(this);
    this.openAccountKit = this.openAccountKit.bind(this);
    this.needUpdate = (!_.get(user, 'memberInfo.member.facebook.name', ''));
    this.keyboardWillShow = this.keyboardWillShow.bind(this);
    this.keyboardWillHide = this.keyboardWillHide.bind(this);
    this.handlePhoneAuthenSucess = this.handlePhoneAuthenSucess.bind(this);
    this.setToken = this.setToken.bind(this);
    this.setPhoneCode = this.setPhoneCode.bind(this);
    this.setPhone = this.setPhone.bind(this);
  }

  // static renderLeftButton(scene){
  //   return (
  //     <View style={Themes.current.screen.leftButtonWrapNavBar}>
  //       <Include.Text>LeftButton</Include.Text>
  //     </View>
  //   )
  // }
  // static renderRightButton(scene){
  //   return (
  //     <View style={Themes.current.screen.rightButtonWrapNavBar}>
  //       <Include.Text>RightButton</Include.Text>
  //     </View>
  //   )
  // }
  static renderTitle(scene) {
    return (
      <View style={Themes.current.screen.titleWrapNavBarCenter}>
        <Include.Text style={Themes.current.text.navBartitle}>THÔNG TIN CÁ NHÂN</Include.Text>
      </View>
    )
  }

  onRefresh() {
    super.onRefresh();
    var { dispatch } = this.props;
  }

  onGetMore() {
    super.onGetMore();
    var { dispatch } = this.props;
  }

  handlePhoneAuthenSucess() {
    this.props.dispatch(UserActions_MiddleWare.changePhoneNumberByNewPhoneAuthen({ newPhone: this.phoneAuthen, token: this.tokenPhone, code: this.phoneCode }))
      .then((result) => {
        this.userInfo.phone = this.phoneAuthen;
        this.forceUpdate();
      })
  }

  setToken(token) {
    this.tokenPhone = token
  }

  setPhoneCode(code) {
    this.phoneCode = code
  }

  setPhone(phone) {
    this.phoneAuthen = phone
  }

  openAccountKit() {
    let { dispatch } = this.props;
    Actions.PhoneAuthenticationScreen({
      handlePhoneAuthenSucess: this.handlePhoneAuthenSucess,
      setToken: this.setToken,
      setPhoneCode: this.setPhoneCode,
      setPhone: this.setPhone,
      changePhone: true
    });
  }

  handleUpdateProfile() {
    let { dispatch, user } = this.props;
    let message = '';
    let name = this.userInfo.name.trim();
    let email = this.userInfo.email.trim();
    let idCard = this.userInfo.idCard.trim();
    let birthday = this.userInfo.birthday.trim();
    let avatar = this.userInfo.avatar
    const objUpdate = {
      name,
      email,
      idCard,
      birthday,
      avatar
    };
    if (this.infoFromToken) {
      objUpdate.access_token = this.infoFromToken.access_token;
    }

    if(birthday && this.birthdayInput && !this.birthdayInput.isValid()) {
      message += 'Định dạng ngày sinh chưa đúng';
    }

    if(name.length < 6) {
      message += 'Họ và tên không được dưới 6 kí tự';
    }

    if ( objUpdate.name.trim() === _.get(user, 'memberInfo.name', '')
      && objUpdate.email.trim() === _.get(user, 'memberInfo.email', '')
      && objUpdate.birthday.trim() === _.get(user, 'memberInfo.birthday', '')
      && objUpdate.idCard.trim() === _.get(user, 'memberInfo.idCard', '')
      && (!this.state.configReferralCode.isOpenProfile || (this.state.configReferralCode.isOpenProfile && this.inviteCode.trim() === ''))
      && ((!objUpdate.avatar && this.userInfo.avatar === _.get(user, 'memberInfo.avatar', '')) || (objUpdate.avatar && objUpdate.avatar === _.get(user, 'memberInfo.avatar', '')))
    ) {
    message = 'Bạn chưa thay đổi thông tin cập nhật'
  }
    if (message) {
      popupActions.setRenderContentAndShow(DefaultPopup, {
        title: 'Thông báo',
        description: message,
        onPress: () => {
          popupActions.popPopup();
        },
        buttonTitle: 'Xong'
      })
    } else {
      this.updateProfile(objUpdate);
    }
  }

  updateProfile(objUpdate) {
    var { dispatch, user, navigation } = this.props;
    dispatch(UserActions_MiddleWare.updateProfile(objUpdate))
      .then((res) => {
        NotifyUtil.pushAlertTopNotify({
          type: 'succes',
          content: 'Cập nhật thông tin thành công',
          timeClose: 3000,
        })
        // navigation.goBack()
        // if (this.inviteCode.trim() !== '') {
        //   dispatch(UserActions_MiddleWare.addPresenterCode({
        //     presenterCode: this.inviteCode.trim()
        //   }))
        // }

        // dispatch(UserActions_MiddleWare.get())
        //   .then(() => {
        //     if (this.needUpdate) {
        //       return (
        //         Actions.SwitchModeScreen({
        //           type: 'reset'
        //         })
        //       )
        //     }
        //   })
      })
      .catch(err => {
        NotifyUtil.pushAlertTopNotify({
          type: 'error',
          content: 'Cập nhật thông tin thất bại',
          timeClose: 3000,
        })
      })
  }

  keyboardWillShow(e) {
    const heightKeyboard = e.endCoordinates.height;
    if (Platform.OS === 'ios') {
      this.setState({
        bottom: heightKeyboard,
        keyboardShow: true
      });
    } else {
      this.setState({
        keyboardShow: true
      })
    }
  }

  keyboardWillHide(e) {
    if (Platform.OS === 'ios') {
      if (this.scrollProfile) {
        this.scrollProfile.scrollTo({ x: 0, y: 0, animated: false })
      }
      this.setState({
        bottom: 0,
        keyboardShow: false
      });
    } else {
      this.setState({
        keyboardShow: false
      })
    }
  }

  renderNavbar() {
    var { dispatch, navigation, user } = this.props;
    return (
      <View
        style={{
          width: Define.constants.widthScreen,
          zIndex: 2,
          shadowColor: Platform.OS === 'ios' ? '#d1d1d1' : 'black',
          shadowOffset: { height: 1, width: 0 },
          shadowOpacity: 0.5,
          shadowRadius: 1,
          elevation: 3,
        }}>
        <View style={{ flexDirection: 'row', justifyContent: 'center', alignItems: 'center' }}>

          <TouchableOpacity
            style={{ position: 'absolute', left: 0,  paddingHorizontal: 16, paddingTop: Platform.OS === 'ios' ? 0 : 10 }}
            onPress={() => {
              navigation.goBack();
            }}>
            <HeyUIcon name={'fi-sr-arrow-left'} size={24} color={'#fff'} />
          </TouchableOpacity>

          <View style={Themes.current.screen.titleWrapNavBarCenter} pointerEvents={'none'}>
            <Include.Text style={{ fontSize: 18, fontFamily: Define.constants.fontBold500, color: '#fff' }}>Thông tin cán bộ</Include.Text>
          </View>
          {user.memberInfo?._id ?
          <TouchableOpacity
          style={{ position: 'absolute', right: 0,  paddingHorizontal: 16, paddingTop: Platform.OS === 'ios' ? 0 : 10 }}
          onPress={() => {
              popupActions.setRenderContentAndShow(DefaultPopup, {
                title: 'Đăng xuất',
                description: 'Bạn có chắc chắn muốn đăng xuất?',
                buttonTitle2: 'Không',
                buttonTitle: 'Đăng xuất',
                onPress2: () => {
                  popupActions.popPopup();
                },
                onPress: () => {
                  // dispatch(UserActions_MiddleWare.trackingAction({type: 1}))
                  // .then(() => {
                    this.props.dispatch(UserActions_MiddleWare.logout({}))
                    .then(() => {
                      SocketOrderManager.destroy();
                      navigation.dispatch(
                        CommonActions.reset({
                          index: 0,
                          routes: [
                            {
                              name: 'LoginAccountScreen',
                            },
                          ],
                        })
                      );
                    })
                    popupActions.popPopup();
                  // })
                }
              })
            }}
          >
            <PenguinLinearIcon name={'login'} size={24} color={'#fff'} />
          </TouchableOpacity>
          : null}
        </View>

      </View>
    )
  }

  renderScreenContent() {
    var { navigation, user } = this.props;
    var content = null;
    let phone =
      content = (
        <View style={[Themes.current.screen.bodyView, this.props.bodyStyle, { position: 'absolute', paddingTop: 0, top: 0, bottom: this.state.bottom }]}>
          {Platform.OS === 'ios' ?
            <StatusBar
              backgroundColor={'#fff'}
              barStyle={'light-content'}
            /> : null}
          <LinearGradient start={{x: 0, y: 0.3}} end={{x: 0, y: 0}} colors={['#003F9E', '#042F71', '#390404ff']} style={{flex: 1}}>
            <SafeAreaView style={{ flex: 1, backgroundColor: 'transparent' }}>
              {this.renderNavbar()}
              <ScrollView
                showsVerticalScrollIndicator={false}
                style={{ flex: 1 }}
                contentContainerStyle={{paddingBottom: 50}}
                ref={(ref) => this.scrollProfile = ref}
              >

                <View
                  onLayout={e => this.setState({ heightProfile: e.nativeEvent.layout.height })}
                  style={{ justifyContent: 'center', padding: 16 }}>
                  <View style={{alignItems:'center'}}>
                    {this.userInfo.picture ?
                      <Image
                        source={{
                          uri: this.userInfo.picture
                        }}
                        style={{
                          width: 72,
                          height: 72,
                          borderRadius: 120,
                        }}
                      /> :
                      <View style={{
                        backgroundColor: '#6fbde8', width: 72, height: 72, borderRadius: 120,
                        alignSelf: 'center', justifyContent: 'center', alignItems: 'center',
                      }}>
                        <Include.Text style={{ backgroundColor: 'transparent', fontSize: 32, color: '#fff' }}>{UserUtil.getNameAsAvatar(this.userInfo.name)}</Include.Text>
                      </View>}
                    {this.userInfo.idNumber ?
                      <Text allowFontScaling={false}
                        style={{
                          fontSize: 16,
                          color: '#fff',
                          fontFamily: Define.constants.fontBold400,
                          marginTop: 12
                        }}>
                        Số hiệu: {this.userInfo.idNumber}
                      </Text> : null}
                  </View>
                  <View>
                    <Include.Text style={{ fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold400, marginBottom: 8 }}>
                      Họ và tên
                    </Include.Text>
                    <View style={{
                      padding: 12,
                      borderRadius: 12,
                      borderWidth: 1,
                      borderColor: '#4A4F55',
                      backgroundColor: '#1C1E21',
                    }}>
                      <Include.Text style={{ fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold500 }}>
                        {this.userInfo.name}
                      </Include.Text>
                    </View>
                  </View>
                  <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginTop: 16, flex: 1 }}>
                    <View style={{ flex: 1, marginRight: 6 }}>
                      <Include.Text style={{ fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold400, marginBottom: 8 }}>
                        Cấp bậc
                      </Include.Text>
                      <View style={{
                        padding: 12,
                        borderRadius: 12,
                        borderWidth: 1,
                        borderColor: '#4A4F55',
                        backgroundColor: '#1C1E21',
                        flexDirection: 'row',
                        alignItems:'center'
                      }}>
                        <Include.Text style={{ fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold500, marginRight: 8}}>
                          {this.userInfo.rank}
                        </Include.Text>
                        {this.userInfo.rankImage ?
                          <View style={{ width: 36, height: 18, justifyContent: 'center', alignItems: 'center' }}>
                            <Image
                              source={{
                                uri: this.userInfo.rankImage,
                              }}
                              resizeMode={'contain'}
                              style={{
                                width: 14,
                                height: 36,
                                transform: [{ rotate: '-90deg' }], // Rotate the image horizontally
                              }}
                            />
                          </View>
                          : null}
                      </View>
                    </View>
                    {this.userInfo.position ?
                    <View style={{ flex: 1, marginLeft: 6 }}>
                      <Include.Text style={{ fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold400, marginBottom: 8 }}>
                        Chức vụ
                      </Include.Text>
                      <View style={{
                        padding: 12,
                        borderRadius: 12,
                        borderWidth: 1,
                        borderColor: '#4A4F55',
                        backgroundColor: '#1C1E21',
                      }}>
                        <Include.Text style={{ fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold500 }}>
                          {this.userInfo.position}
                        </Include.Text>
                      </View>
                    </View> : null }
                  </View>
                  <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginTop: 16, flex: 1 }}>
                    <View style={{ flex: 1, marginRight: 6 }}>
                      <Include.Text style={{ fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold400, marginBottom: 8 }}>
                        Giới tính
                      </Include.Text>
                      <View style={{
                        padding: 12,
                        borderRadius: 12,
                        borderWidth: 1,
                        borderColor: '#4A4F55',
                        backgroundColor: '#1C1E21',
                      }}>
                        <Include.Text style={{ fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold500 }}>
                          {this.userInfo.gender === 'male' ? 'Nam' : 'Nữ'}
                        </Include.Text>
                      </View>
                    </View>
                    <View style={{ flex: 1, marginLeft: 6 }}>
                      <Include.Text style={{ fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold400, marginBottom: 8 }}>
                        Ngày sinh
                      </Include.Text>
                      <View style={{
                        padding: 12,
                        borderRadius: 12,
                        borderWidth: 1,
                        borderColor: '#4A4F55',
                        backgroundColor: '#1C1E21',
                      }}>
                        <Include.Text style={{ fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold500 }}>
                          {this.userInfo.birthday}
                        </Include.Text>
                      </View>
                    </View>
                  </View>
                  <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginTop: 16, flex: 1 }}>
                    <View style={{ flex: 1, marginRight: 6 }}>
                      <Include.Text style={{ fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold400, marginBottom: 8 }}>
                      Lý luận chính trị
                      </Include.Text>
                      <View style={{
                        padding: 12,
                        borderRadius: 12,
                        borderWidth: 1,
                        borderColor: '#4A4F55',
                        backgroundColor: '#1C1E21',
                      }}>
                        <Include.Text style={{ fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold500 }}>
                          {this.userInfo.level}
                        </Include.Text>
                      </View>
                    </View>
                    <View style={{ flex: 1, marginLeft: 6 }}>
                      <Include.Text style={{ fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold400, marginBottom: 8 }}>
                        Học vấn
                      </Include.Text>
                      <View style={{
                        padding: 12,
                        borderRadius: 12,
                        borderWidth: 1,
                        borderColor: '#4A4F55',
                        backgroundColor: '#1C1E21',
                      }}>
                        <Include.Text style={{ fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold500 }}>
                          {this.userInfo.eduLevel}
                        </Include.Text>
                      </View>
                    </View>
                  </View>
                  <View style={{ marginTop: 16 }}>
                    <Include.Text style={{ fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold400, marginBottom: 8 }}>
                      Email
                    </Include.Text>
                    <View style={{
                      padding: 12,
                      borderRadius: 12,
                      borderWidth: 1,
                      borderColor: '#4A4F55',
                      backgroundColor: '#1C1E21',
                    }}>
                      <Include.Text style={{ fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold500 }}>
                        {this.userInfo.email}
                      </Include.Text>
                    </View>
                  </View>
                  <View style={{ marginTop: 16 }}>
                    <Include.Text style={{ fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold400, marginBottom: 8 }}>
                      Số điện thoại
                    </Include.Text>
                  <View style={{
                    padding: 12,
                    borderRadius: 12,
                    borderWidth: 1,
                    borderColor: '#4A4F55',
                    backgroundColor: '#1C1E21',
                  }}>
                    <Include.Text style={{ fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold500 }}>
                      {this.userInfo.phone}
                    </Include.Text>
                  </View>
                  </View>
                  {this.userInfo.units ?
                    <View style={{ marginTop: 16 }}>
                      <Include.Text style={{ fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold400, marginBottom: 8 }}>
                        Đơn vị công tác
                      </Include.Text>
                      <View style={{
                        padding: 12,
                        borderRadius: 12,
                        borderWidth: 1,
                        borderColor: '#4A4F55',
                        backgroundColor: '#1C1E21',
                      }}>
                        <Include.Text style={{ fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold500 }}>
                          {this.userInfo.units}
                        </Include.Text>
                      </View>
                    </View> : null}
                  {this.userInfo.areas ?
                    <View style={{ marginTop: 16 }}>
                      <Include.Text style={{ fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold400, marginBottom: 8 }}>
                        Khu vực
                      </Include.Text>
                      <View style={{
                        padding: 12,
                        borderRadius: 12,
                        borderWidth: 1,
                        borderColor: '#4A4F55',
                        backgroundColor: '#1C1E21',
                      }}>
                        <Include.Text style={{ fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold500 }}>
                          {this.userInfo.areas}
                        </Include.Text>
                      </View>
                    </View> : null}
                  {!!this.userInfo.areaChilds.length ?
                  <View style={{ marginTop: 16 }}>
                    <Include.Text style={{ fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold400, marginBottom: 8 }}>
                      Tổ dân phố
                    </Include.Text>
                    <View style={{
                      padding: 8,
                      borderRadius: 12,
                      borderWidth: 1,
                      borderColor: '#4A4F55',
                      backgroundColor: '#1C1E21',
                      flexDirection:'row',
                      flexWrap: 'wrap',
                    }}>
                      {this.userInfo.areaChilds
                        .filter(c => !!c?.name)
                        .slice(0, 2)
                        .map(child => (
                          <View key={child._id} style={{
                            backgroundColor: '#2E3236',
                            borderRadius: 16,
                            paddingVertical: 4,
                            paddingHorizontal: 8,
                            marginHorizontal: 4,
                            marginVertical: 4,
                          }}>
                            <Include.Text style={{ fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold400 }}>
                              {child.name}
                            </Include.Text>
                          </View>
                        ))}
                    </View>
                  </View> : null }
                </View>
                {this.state.configRemoveAccount && this.state.configRemoveAccount.isOpen ?
                  <TouchableOpacity
                    style={{ marginTop: 32, padding: 16, zIndex:5}}
                    onPress={() => {
                      popupActions.setRenderContentAndShow(ChooseBlockAccountPopup, {
                        actions: this.state.configRemoveAccount
                          && this.state.configRemoveAccount.actions
                          ? this.state.configRemoveAccount.actions
                          : []
                      })
                    }}
                  >
                    <Text allowFontScaling={false}
                      style={{
                        fontSize: 18,
                        color: '#E93940',
                        fontFamily: Define.constants.fontBold400,
                        textAlign: 'center'
                      }}>
                        Xoá tài khoản
                    </Text>
                  </TouchableOpacity>
                :null}
                {/* <View style={{ zIndex: 0 }}>
                  <Image
                    style={{
                      width: 165,
                      height: 175,
                      position: 'absolute',
                      left: 0,
                      bottom: -80,
                      zIndex: 1
                    }}
                    resizeMode={'stretch'}
                    source={{ uri: 'https://media.heyu.asia/uploads/mobiles/2025-01-08-hoaphuongdonavbar.png' }}
                  />
                  <Image
                    style={{
                      width: 165,
                      height: 175,
                      position: 'absolute',
                      right: 0,
                      bottom: -80,
                      zIndex: 1
                    }}
                    resizeMode={'stretch'}
                    source={{ uri: 'https://media.heyu.asia/uploads/mobiles/2025-01-08-hoaphuongdonavbar2.png' }}
                  />
                </View> */}
              </ScrollView>
              {/* <View style={{
                padding: 16,
                backgroundColor: '#021E38',
                shadowColor: Platform.OS === 'ios' ? '#929394' : 'black',
                shadowOffset: { height: 2, width: 0 },
                shadowOpacity: 1,
                shadowRadius: 2,
              }}>
                <TouchableOpacity
                  style={{
                    padding: 12, borderRadius: 8, alignItems: 'center', backgroundColor: this.userInfo.name.trim() === _.get(user, 'memberInfo.name', '')
                      && this.userInfo.email.trim() === _.get(user, 'memberInfo.email', '')
                      && this.userInfo.birthday.trim() === _.get(user, 'memberInfo.birthday', '')
                      && this.userInfo.idCard.trim() === _.get(user, 'memberInfo.idCard', '')
                      && (!this.state.configReferralCode.isOpenProfile || (this.state.configReferralCode.isOpenProfile && this.inviteCode.trim() === ''))
                      && ((!this.userInfo.avatar && this.userInfo.avatar === _.get(user, 'memberInfo.avatar', '')) || (this.userInfo.avatar && this.userInfo.avatar === _.get(user, 'memberInfo.avatar', ''))) ? '#F3F3F3' : '#007CFE', width: Define.constants.widthScreen - 32, justifyContent: 'center', flexDirection: 'row'
                  }}
                  onPress={() => {
                    Keyboard.dismiss();
                    this.handleUpdateProfile();
                    if (Platform.OS === 'ios') {
                      this.scrollProfile.scrollTo({ x: 0, y: 0, animated: false })
                    }
                  }}>
                  <Include.Text style={{
                    backgroundColor: 'transparent', color: this.userInfo.name.trim() === _.get(user, 'memberInfo.name', '')
                      && this.userInfo.email.trim() === _.get(user, 'memberInfo.email', '')
                      && this.userInfo.birthday.trim() === _.get(user, 'memberInfo.birthday', '')
                      && this.userInfo.idCard.trim() === _.get(user, 'memberInfo.idCard', '')
                      && (!this.state.configReferralCode.isOpenProfile || (this.state.configReferralCode.isOpenProfile && this.inviteCode.trim() === ''))
                      && ((!this.userInfo.avatar && this.userInfo.avatar === _.get(user, 'memberInfo.avatar', '')) || (this.userInfo.avatar && this.userInfo.avatar === _.get(user, 'memberInfo.avatar', ''))) ? '#A1A7AE' : '#fff', fontSize: 18, fontFamily: Define.constants.fontBold600, marginLeft: 6
                  }}>Lưu thông tin</Include.Text>
                </TouchableOpacity>
              </View> */}
            </SafeAreaView>
          </LinearGradient>
          <SafeAreaView style={{ flex: 0, backgroundColor: '#023d96' }} />
        </View>
      )

    return content;
  }

  onDateChange = (date) => {
    this.userInfo.birthday = date;
  }
  msToTime(duration) {
    return moment(duration,'DD/MM/YYYY').toDate();
  }

  UNSAFE_componentWillMount() {
    super.UNSAFE_componentWillMount()
    if (Platform.OS === 'android') {
      this.keyboardDidShowSubscription = Keyboard.addListener('keyboardDidShow', this.keyboardWillShow)
      this.keyboardDidHideSubscription = Keyboard.addListener('keyboardDidHide', this.keyboardWillHide)
    } else {
      this.keyboardDidShowSubscription = Keyboard.addListener('keyboardWillShow', this.keyboardWillShow)
      this.keyboardDidHideSubscription = Keyboard.addListener('keyboardWillHide', this.keyboardWillHide)
    }
  }
  getConfigRemoveAccount = () => {
    this.props.dispatch(UserActions_MiddleWare.getConfigRemoveAccount())
    .then((result) => {
      if(result && result.res && result.res.data) {
        this.setState({
          configRemoveAccount: result.res.data
        })
      }
    })
  }
  componentDidMount() {
    super.componentDidMount();
    this.getConfigRemoveAccount()
    this.props.dispatch(UserActions_MiddleWare.get())

    // this.props.dispatch(AppStateActions_MiddleWare.getConfigReferralCode())
    //   .then((result) => {
    //     if (result.res.data) {
    //       this.setState({
    //         configReferralCode: result.res.data
    //       })
    //     }
    //   })

    // this.props.dispatch(AppStateActions_MiddleWare.getConfigAuthenShipperOnline())
    //   .then((result) => {
    //     if (result.res.data) {
    //       this.setState({
    //         configShipperAuthenOnline: result.res.data
    //       })
    //     }
    //   })
  }
  componentDidUpdate() {
    if (Platform.OS === 'ios') {
      LayoutAnimation.easeInEaseOut();
    }
  }
}

/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  return {
    user: state.User,
    navigator: state.Navigator,
    appState: state.AppState,
    appSetting: state.AppSetting
  }
}

export default connect(selectActions, undefined, undefined, { withRef: true })(ProfileScreen);
