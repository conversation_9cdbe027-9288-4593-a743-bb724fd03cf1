var _ = require('lodash');

//LIB
import React from 'react';
import {
  View,
  ScrollView,
  RefreshControl,
  SafeAreaView,
  Text,
  Image,
  TouchableOpacity,
  StatusBar,
  Keyboard,
  InteractionManager,
  Platform,
  AppState,
} from 'react-native';

var {Actions} = require('react-native-router-flux');
import {connect} from 'react-redux';
//action
import {TabActions} from '@react-navigation/native';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import LinearGradient from 'react-native-linear-gradient';
const Tab = createBottomTabNavigator();
//components
var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
var Util = require('../../Util/Util');
var Include = require('../../Include');

var {popupActions} = require('../popups/PopupManager');
var {globalVariableManager} = require('../modules/GlobalVariableManager');

var ButtonWrap = require('../elements/ButtonWrap');
import SocketOrderManager from '../modules/SocketOrder';
//screens
import Screen from './Screen';
import NotifyScreen from './NotifyScreen';
import AccountScreen from './AccountScreen';
import MainScreen from './MainScreen';
// popups
import DefaultPopup from '../popups/DefaultPopup';
import PenguinBoldIcon from '../elements/PenguinBoldIcon/PenguinBoldIcon';
import WebviewScreen from './WebviewScreen';
import RDActions from '../../actions/RDActions';
import PenguinLinearIcon from '../elements/PenguinLinearIcon/PenguinLinearIcon';
const PushNotiManager = require('../modules/PushNotiManager');
import ReactNativeBiometrics from 'react-native-biometrics';

// actions

//variable

// var styles = StyleSheet.create({
//
// })

//

class MainContainer extends Screen {
  static componentName = 'MainContainer';
  static sceneConfig = {
    ...Screen.sceneConfig,
    hideNavBar: true,
  };
  // static defaultProps = {}
  // static propTypes = {}
  constructor(props) {
    super(props);
    this.state = _.merge(this.state, {
      bottom: 0,
      requireFaceIDLogin: false,
      biometricKeysExist: false,
      faceIDChecked: false, // Track if FaceID check has run after cold start
    });
  }

  onRefresh() {
    super.onRefresh();
    var {dispatch} = this.props;
  }

  onGetMore() {
    super.onGetMore();
    var {dispatch} = this.props;
  }

  async checkAndShowFaceID() {
    if (this.state.faceIDChecked) return;
    const { user, navigation } = this.props;
    const rnBiometrics = new ReactNativeBiometrics();
    const { keysExist } = await rnBiometrics.biometricKeysExist();
    if (keysExist && user && user.memberInfo && user.memberInfo._id) {
      this.setState({
        requireFaceIDLogin: true,
        biometricKeysExist: true,
        faceIDChecked: true,
      },()=>{
        const { requireFaceIDLogin, biometricKeysExist } = this.state;
        if (
          requireFaceIDLogin &&
          biometricKeysExist &&
          user &&
          user.memberInfo &&
          user.memberInfo._id
        ) {
          setTimeout(() => {
            navigation.navigate('LoginFaceIDScreen');
            this.setState({ requireFaceIDLogin: false, biometricKeysExist: false, faceIDChecked: false});
          }, 50);
        }
      });
    } else {
      this.setState({
        requireFaceIDLogin: false,
        biometricKeysExist: false,
        faceIDChecked: true,
      });
    }
  }

  handleAppStateChange = (currentAppState) => {
    switch (currentAppState) {
      case 'active': {
        if (this.lastBackgroundTime) {
          const diff = Date.now() - this.lastBackgroundTime;
          if (diff > 60000) {
            this.checkAndShowFaceID();
          }
        }
        this.lastBackgroundTime = null;
        break;
      }
      case 'background': {
        this.lastBackgroundTime = Date.now();
        break;
      }
      default:
    }
  }

  renderScreenContent() {
    var {dispatch, navigation, user, notify} = this.props;
    var content = (
      <View style={{flex: 1, backgroundColor: '#023367'}}>
        <Tab.Navigator
          backBehavior={'history'}
          screenOptions={({route}) => ({
            freezeOnBlur: true,
            tabBarHideOnKeyboard: true,
            tabBarLabel: ({focused, color}) => {
              return (
                <View
                  style={{
                    paddingTop: Platform.OS === 'ios' ? 12 : 16,
                    paddingBottom: 8,
                    borderBottomWidth: focused ? 2 : 0,
                    borderColor: '#fff',
                  }}>
                  <Text
                    allowFontScaling={false}
                    style={{
                      fontSize: 12,
                      fontFamily: focused
                        ? Define.constants.fontBold600
                        : Define.constants.fontBold500,
                      color: focused ? '#fff' : '#CCCFD3',
                      textAlign: 'center',
                      paddingBottom: 12,
                    }}>
                    {route.name}
                  </Text>
                </View>
              );
            },
            tabBarStyle: {
              position: Platform.OS === 'ios' ? 'relative' : 'absolute',
              height: 92,
              borderTopLeftRadius: 12,
              borderTopRightRadius: 12,
              paddingHorizontal: 0,
              paddingTop: 10,
              shadowColor: Platform.OS === 'ios' ? '#d1d1d1' : 'black',
              shadowOffset: {height: -1, width: 0},
              shadowOpacity: 0.5,
              shadowRadius: 2,
              elevation: 3,
              backgroundColor: '#023367',
              borderTopWidth: 0,
            },
            headerShown: false,
            tabBarIcon: ({focused, color, size}) => {
              let iconName;
              let sizeIcon = 24;
              if (route.name === 'Trang chủ') {
                iconName = 'home-2';
              } else if (route.name === 'Nhiệm vụ') {
                iconName = 'menu-board';
              } else if (route.name === 'Báo cáo') {
                iconName = 'graph';
              } else if (route.name === 'Cá nhân') {
                iconName = 'user';
              } else if (route.name === 'Lịch sử') {
                iconName = 'clock-1';
              }
              return (
                <View
                  style={{
                    alignItems: 'center',
                    marginTop: 16,
                    marginBottom: 8,
                  }}>
                  {focused ? (
                    <View
                      style={{
                        width: 67,
                        height: 80,
                        position: 'absolute',
                        alignSelf: 'center',
                        top: 0,
                        backgroundColor: '#007CFE',
                        borderTopLeftRadius: 12,
                        borderTopRightRadius: 12,
                      }}
                    />
                  ) : null}
                  {/* <Image
                    source={iconName}
                    style={{width:sizeIcon, height: sizeIcon}}
                    tintColor={color}
                  /> */}
                  <View
                    style={{
                      width: sizeIcon,
                      height: sizeIcon,
                      marginTop: 16,
                    }}>
                    <PenguinBoldIcon
                      name={iconName}
                      size={sizeIcon}
                      color={focused ? '#fff' : '#CCCFD3'}
                    />
                  </View>
                </View>
              );
            },
          })}
          tabBarOptions={{
            activeTintColor: '#376FFF',
            inactiveTintColor: '#2E3236',
          }}>
          <Tab.Screen name="Trang chủ" component={MainScreen} />
          {/* <Tab.Screen
            name="Điểm danh"
            component={AttendanceListScreen}
            options={{
              tabBarLabel: ({focused, color}) => {
                return (
                  <View
                    style={{
                      marginTop: Platform.OS === 'ios' ? 12 : 16,
                      marginBottom: 8,
                      backgroundColor: focused ? '#007cfe' : 'transparent',
                      borderRadius: 12,
                      paddingHorizontal: focused ? 4 : 0,
                      paddingVertical: focused ? 2 : 0,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Text
                      allowFontScaling={false}
                      style={{
                        fontSize: focused ? 11 : 12,
                        fontFamily: focused
                          ? Define.constants.fontBold600
                          : Define.constants.fontBold500,
                        color: focused ? '#fff' : '#CCCFD3',
                        marginTop: -1,
                      }}>
                      Điểm danh
                    </Text>
                  </View>
                );
              },
              tabBarIcon: ({focused, color, size}) => {
                return (
                  <View
                    style={{
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <View
                      style={{
                        width: 66,
                        height: 66,
                        alignItems: 'center',
                        justifyContent: 'center',
                        marginBottom: 20,
                      }}>
                      <Image
                        source={Define.assets.Images.attendance}
                        style={{width: 66, height: 66}}
                      />
                    </View>
                  </View>
                );
              },
            }}
          /> */}
          <Tab.Screen
            name="Cá nhân"
            component={AccountScreen}
            listeners={({navigation}) => ({
              tabPress: event => {
                if (user.memberInfo._id) {
                  // trigger default event AKA focused to this tab
                } else {
                  event.preventDefault(); //preventing dafault.
                  navigation.navigate('LoginAccountScreen', {
                    checkLogin: false,
                  }); //calling custom
                }
              },
            })}
            options={({route, navigation, theme}) => ({})}
          />
        </Tab.Navigator>
        <SafeAreaView
          style={{
            flex: 0,
            backgroundColor:
              Platform.OS === 'ios' && !this.props.appSetting.openedPopupNoti
                ? '#fff'
                : '#023367',
          }}
        />
      </View>
    );
    return content;
  }
  keyboardWillShow = e => {
    const heightKeyboard = e.endCoordinates.height;
    if (Platform.OS === 'ios') {
      this.setState({
        bottom: heightKeyboard,
        keyboardShow: true,
      });
    } else {
      setTimeout(() => {
        this.setState({
          bottom: heightKeyboard,
          keyboardShow: true,
        });
      }, 100);
    }
  };

  keyboardWillHide = e => {
    if (Platform.OS === 'ios') {
      this.setState({
        bottom: 0,
        keyboardShow: false,
      });
    } else {
      this.setState({
        bottom: 0,
        keyboardShow: false,
      });
    }
  };
  UNSAFE_componentWillMount() {
    super.UNSAFE_componentWillMount();

    if (Platform.OS === 'android') {
      this.keyboardDidShowSubscription = Keyboard.addListener(
        'keyboardDidShow',
        this.keyboardWillShow,
      );
      this.keyboardDidHideSubscription = Keyboard.addListener(
        'keyboardDidHide',
        this.keyboardWillHide,
      );
    } else {
      this.keyboardDidShowSubscription = Keyboard.addListener(
        'keyboardWillShow',
        this.keyboardWillShow,
      );
      this.keyboardDidHideSubscription = Keyboard.addListener(
        'keyboardWillHide',
        this.keyboardWillHide,
      );
    }
    this.appStateSubscription = AppState.addEventListener('change', this.handleAppStateChange)
  }
  componentDidMount() {
    super.componentDidMount();
    this.props.navigation.setOptions({ gestureEnabled: false });
    // if (this.props.route.params?.tabIndex !== undefined) {
    //   const tabNames = ['Trang chủ', 'Nhiệm vụ', 'Điểm danh', 'Báo cáo', 'Cá nhân'];
    //   const tabName = tabNames[this.props.route.params?.tabIndex];
    //   if (tabName && this.props.navigation) {
    //     this.props.navigation.navigate('MainContainer', { screen: tabName });
    //   }
    // }
    if (Platform.OS === 'ios' && !this.props.appSetting.openedPopupNoti) {
      PushNotiManager.handlePopup();
    }
    SocketOrderManager.reCheck();
    InteractionManager.runAfterInteractions(() => {
      globalVariableManager.rootView.preProcessWhenStart();
    });
  }
  componentDidUpdate(prevProps) {
    // if (prevProps.route.params?.tabIndex !== this.props.route.params?.tabIndex) {
    //   const tabNames = ['Trang chủ', 'Nhiệm vụ', 'Điểm danh', 'Báo cáo', 'Cá nhân'];
    //   const tabName = tabNames[this.props.route.params?.tabIndex];
    //   if (tabName && this.props.navigation) {
    //     this.props.navigation.navigate('MainContainer', { screen: tabName });
    //   }
    // }
  }
  componentWillUnmount() {
    const {dispatch} = this.props;
    super.componentWillUnmount();
    this.keyboardDidShowSubscription.remove();
    this.keyboardDidHideSubscription.remove();
  }
}

/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  return {
    navigator: state.Navigator,
    user: state.User,
    notify: state.Notify,
    appSetting: state.AppSetting,
  };
}

export default connect(selectActions, undefined, undefined, {withRef: true})(
  MainContainer,
);
