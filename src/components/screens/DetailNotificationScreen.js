
var _ = require('lodash')

//LIB
import React from 'react';
import {
  View,
  ScrollView,
  RefreshControl,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  SafeAreaView,
  Platform,
} from 'react-native';

var { Actions } = require('react-native-router-flux');
import { connect } from 'react-redux';
import { getStatusBarHeight } from 'react-native-status-bar-height';
//action
import RDActions from '../../actions/RDActions'

//components
var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
var Util = require('../../Util/Util');
var Include = require('../../Include');
import HeyUIcon from '../elements/HeyUIcon/HeyUIcon';

var { popupActions } = require('../popups/PopupManager');
var { globalVariableManager } = require('../modules/GlobalVariableManager');
import { Icon } from 'native-base';
import HTML from 'react-native-render-html';

var ButtonWrap = require('../elements/ButtonWrap');

import LinearGradient from 'react-native-linear-gradient';
import { TransitionPresets } from 'react-navigation-stack';


//screens
import Screen from './Screen'

// popups
import DefaultPopup from '../popups/DefaultPopup';

// actions
import NotificationsActions_MiddleWare from '../../actions/NotificationsActions_MiddleWare';

//variable

// var styles = StyleSheet.create({
//
// })

//

class DetailNotificationScreen extends Screen {
  static componentName = 'DetailNotificationScreen'
  static sceneConfig = {
    ...Screen.sceneConfig,
    hideNavBar: true
  }
  // static defaultProps = {}
  // static propTypes = {}
  constructor(props) {
    super(props);
    this.state = _.merge(this.state,
      {
        notify: null,
        dataNoti: {}
      })
  }

  onRefresh() {
    super.onRefresh();
    var { dispatch } = this.props;
  }

  onGetMore() {
    super.onGetMore();
    var { dispatch } = this.props;
  }

  renderNavBar() {
    const statusBarHeight = Platform.OS === 'ios' ? getStatusBarHeight(true) : 10;
    return (
      <View
        style={{
          height: Platform.OS === 'ios' ? Define.constants.navBarHeight + statusBarHeight : Define.constants.navBarHeight,
          width: '100%',
          backgroundColor: '#021E38',
          flexDirection: 'row',
          paddingTop: statusBarHeight,
          alignItems: 'center',
          elevation: 2,
          shadowColor: '#d1d1d1', shadowOpacity: 0.5, shadowOffset: { height: 1, width: 0 }, shadowRadius: 1, zIndex: 1
        }}>
        <TouchableOpacity
          onPress={() => {
            this.props.navigation.goBack();
          }}
          style={{ marginLeft: 22, width: Define.constants.navBarHeight }}
        >
          <HeyUIcon name={'fi-sr-arrow-left'} color={'#fff'} size={24} />
        </TouchableOpacity>
        <View style={{ marginLeft: -22, flex: 1 }} pointerEvents={'none'}>
          <Include.Text numberOfLines={1} style={{ color: '#fff', fontSize: 20, fontFamily: Define.constants.fontBold500, textAlign: 'center' }}>Thông báo</Include.Text>
        </View>
        <View style={{ width: Define.constants.navBarHeight }} />
      </View>
    )
  }

    renderLoading() {
      var {dispatch, navigation} = this.props;
      return (
        <View style={{ flex: 1, backgroundColor: '#fff' }}>
          <SafeAreaView style={{ backgroundColor: '#007CFE', flex: 0 }} />
          <View
            style={{
              height: Define.constants.navBarHeight + 6,
              width: '100%',
              backgroundColor: '#007CFE',
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'row',
              shadowColor: '#000',
              shadowOpacity: 0.1,
              shadowOffset: { height: 2, width: 0 },
              shadowRadius: 0.3,
              zIndex: 2,
              elevation: 3,
            }}>
            <TouchableOpacity
              style={{
                position: 'absolute',
                left: 0,
                backgroundColor: 'transparent',
                top: Platform.OS === 'android' ? 10 : 0,
                paddingHorizontal: 16,
                height: Define.constants.navBarHeight + 6,
                justifyContent: 'center',
                alignItems: 'center',
              }}
              activeOpacity={0.6}
              onPress={() => {
                navigation.goBack()
              }}>
              <HeyUIcon name={'fi-sr-arrow-left'} size={24} color={'#fff'} />
            </TouchableOpacity>

            <Include.Text
              style={{
                color: '#fff',
                fontSize: 18,
                fontFamily: Define.constants.fontBold500,
                top: Platform.OS === 'android' ? 10 : 0,
              }}>
              Chi tiết thông báo
            </Include.Text>
          </View>
          <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
            <ActivityIndicator size={'large'} />
            <Include.Text>Please wait...</Include.Text>
          </View>
          <SafeAreaView style={{ flex: 0 }} />
        </View>
      );
    }

  renderScreenContent() {
    var { dispatch } = this.props;
    var content = null;
    var {actionButton, content} = this.props.route.params
    let nativeVersion = Number(Define.constants.nativeVersion)
    var {dataNoti} = this.state
    if (_.isEqual(dataNoti, {})) {
      return this.renderLoading()
    }
    content = (
      <View style={[Themes.current.screen.bodyView, this.props.bodyStyle, { backgroundColor: '#fff', paddingTop: 0 }]}>
        {this.renderNavBar()}
        <ScrollView
          onRefresh={this.onRefresh}
          contentContainerStyle={{
            paddingBottom: Define.constants.heightScreen * 0.1 + 10
          }}
          style={{ marginHorizontal: 12, marginTop: 12}}
          showsVerticalScrollIndicator={false}
        >
          {/* 100017
          10021 */}
          {(Platform.OS === 'android' && nativeVersion > 10021) || (Platform.OS === 'ios' && nativeVersion > 100016) ?
            <HTML source={{html: dataNoti.extras.content}} contentWidth={Define.constants.widthScreen} imagesMaxWidth={Define.constants.widthScreen - 20} />
            :
            <HTML source={{html: dataNoti.extras.content}} imagesMaxWidth={Define.constants.widthScreen - 20} />
          }
        </ScrollView>
        {/* {dataNoti.link ?
          <View style={{ bottom: 0, left: 0, right: 0, width: Define.constants.widthScreen, height: Define.constants.heightScreen * 0.1, padding: 10, position: 'absolute', alignItems: 'flex-end' }}>
            <TouchableOpacity
              style={{ flexDirection: 'row', paddingHorizontal: 10, borderRadius: 5, alignItems: 'center', justifyContent: 'center', padding: 10, backgroundColor: '#0473CD' }}
              onPress={() => {
                if (dataNoti.link) {
                  globalVariableManager.navigatorManager.handleNavigator(dataNoti.link, dataNoti.extras)
                } else if (dataNoti.actionUrl) {
                  globalVariableManager.navigatorManager.handleNavigator('WebviewScreen', {
                    source: dataNoti.actionUrl
                  })

                }
              }}>
              <Include.Text style={{ backgroundColor: 'transparent', color: '#fff', fontSize: 18, fontWeight: '500' }}>{actionButton.buttonTitle}</Include.Text>
            </TouchableOpacity>
          </View> : null
        } */}
      </View>
    )
    return content;
  }

    getDetailNoti = () => {
    var {actionButton, content, _id} = this.props.route.params
      this.props.dispatch((NotificationsActions_MiddleWare.getDetailNotification({ _id: _id })))
        .then((result) => {
          this.setState({dataNoti: result.res.data?.data});
        })
    }

  componentDidMount() {
    super.componentDidMount();
    this.getDetailNoti()

  }
}


/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  return {
    navigator: state.Navigator,
  }
}

export default connect(selectActions, undefined, undefined, { withRef: true })(DetailNotificationScreen);
