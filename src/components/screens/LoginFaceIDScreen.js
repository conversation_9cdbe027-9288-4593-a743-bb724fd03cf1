var _ = require('lodash');
const ms = require('ms');
import moment from 'moment';
//LIB
import React from 'react';
import {
  View,
  ScrollView,
  RefreshControl,
  Button,
  Image,
  ActivityIndicator,
  Modal,
  Platform,
  TextInput,
  TouchableOpacity,
  StatusBar,
  Keyboard,
  LayoutAnimation,
  SafeAreaView,
  Text,
  ImageBackground,
} from 'react-native';
const DeviceInfo = require('react-native-device-info');

var {Actions} = require('react-native-router-flux');
import {connect} from 'react-redux';
import * as Animatable from 'react-native-animatable';
import LinearGradient from 'react-native-linear-gradient';
import {Hoshi} from 'react-native-textinput-effects';
import {Fontisto as IconX} from '@react-native-vector-icons/fontisto';
import PenguinLinearIcon from '../elements/PenguinLinearIcon/PenguinLinearIcon';
import PenguinBoldIcon from '../elements/PenguinBoldIcon/PenguinBoldIcon';
import DatePicker from 'react-native-date-picker';
import * as Keychain from 'react-native-keychain';
import ReactNativeBiometrics from 'react-native-biometrics';

//action

//components
var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
var Util = require('../../Util/Util');
var Include = require('../../Include');
var NotifyUtil = require('../../Util/notify');
var {popupActions} = require('../popups/PopupManager');
var {globalVariableManager} = require('../modules/GlobalVariableManager');
import SocketOrderManager from '../modules/SocketOrder';
import OTPInputView from '@twotalltotems/react-native-otp-input';
var ButtonWrap = require('../elements/ButtonWrap');

//screens
import Screen from './Screen';

// popups
import DefaultPopup from '../popups/DefaultPopup';

// actions
import UserActions_MiddleWare from '../../actions/UserActions_MiddleWare';
import AppStateActions_MiddleWare from '../../actions/AppStateActions_MiddleWare';
import IHeyUActions_MiddleWare from '../../actions/IHeyUActions_MiddleWare';
import HeyUIcon from '../elements/HeyUIcon/HeyUIcon';
//variable
import {getStatusBarHeight} from 'react-native-status-bar-height';
import ParallaxScrollView from 'react-native-parallax-scroll-view';
import RDActions from '../../actions/RDActions';

// var styles = StyleSheet.create({
//
// })

//

class LoginFaceIDScreen extends Screen {
  static componentName = 'LoginFaceIDScreen';
  static sceneConfig = {
    ...Screen.sceneConfig,
    hideNavBar: true,
  };
  constructor(props) {
    super(props);
    this.state = _.merge(this.state, {
      userNameValidate: true,
      passwordValidate: false,
      checkPassword: true,
      bottom: 0,
      scanning: true,
      failed: false,
      showLogin: false,
      hideImage: false,
    });
    this.password = '';
    this.userName = this.props.user.memberInfo.username
  }

  async componentDidMount() {
    super.componentDidMount();
    const rnBiometrics = new ReactNativeBiometrics();
    const { keysExist } = await rnBiometrics.biometricKeysExist();
    if (keysExist) {
      rnBiometrics.isSensorAvailable()
        .then(({ available }) => {
          if (available) {
            rnBiometrics.simplePrompt({ promptMessage: 'Xác thực sinh trắc học'})
              .then(resultObject => {
                const { success } = resultObject;
                if (success) {
                  this.setState({ scanning: false, failed: false }, () => {
                    this.handleNavigate();
                  });
                } else {
                  this.setState({ scanning: false, failed: true });
                }
              })
              .catch(() => {
                this.setState({ scanning: false, failed: true });
              });
          } else {
            this.setState({ scanning: false, failed: true });
          }
        })
        .catch(() => {
          this.setState({ scanning: false, failed: true });
        });
    } else {
      this.setState({ scanning: false, failed: true });
    }
  }

  handleRetryFaceID = async () => {
    this.setState({ scanning: true, failed: false });
    const rnBiometrics = new ReactNativeBiometrics();
    rnBiometrics.simplePrompt({promptMessage: 'Xác thực sinh trắc học'})
      .then(resultObject => {
        const { success } = resultObject;
        if (success) {
          this.setState({ scanning: false, failed: false }, () => {
            this.handleNavigate();
          });
        } else {
          this.setState({ scanning: false, failed: true });
        }
      })
      .catch(() => {
        this.setState({ scanning: false, failed: true });
      });
  }

  handleShowLogin = () => {
    this.setState({ showLogin: true, hideImage: true }, () => {
      this._passwordLogin && this._passwordLogin.focus();
    });
  }

  renderScreenContent() {
    var {dispatch, user, tips, appState, navigation} = this.props;
    const statusBarHeight = Platform.OS === 'ios' ? getStatusBarHeight(true) : StatusBar.currentHeight;

    if (this.state.showLogin) {
      return (
        <ImageBackground source={Define.assets.Images.bgrlogin} style={{ width: Define.constants.widthScreen, height: '100%', paddingHorizontal: 16, }}>
          <ScrollView
            ref={ref => (this.scrollViewRef = ref)}
            contentContainerStyle={{flexGrow: 1}}
            keyboardShouldPersistTaps='handle'
            scrollEnabled={!this.state.bottom ? false : true}
            showsVerticalScrollIndicator={true}
          >
            <View
              style={{
                position: 'absolute',
                top: Platform.OS === 'android' ? StatusBar.currentHeight + 8 : statusBarHeight + 8,
                zIndex: 9999,
                justifyContent: 'center', alignItems: 'center',
              }}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({ showLogin: false, hideImage: false, password: '' });
                  }}
                  style={{
                    width: 40,
                    height: 40,
                    justifyContent: 'center',
                    alignItems: 'center',
                    backgroundColor: '#fff',
                    borderRadius: 32,
                    zIndex: 3,
                  }}>
                  <PenguinLinearIcon name={'arrow-left'} color={'#012548'} size={24} />
                </TouchableOpacity>
            </View>
            <View style={{
              top: Platform.OS === 'android' ? StatusBar.currentHeight + 30 : statusBarHeight + 8,
              zIndex: 7,
              justifyContent: 'center', alignItems: 'center',
            }}>
              <View style={{justifyContent:'center',alignItems:'center',marginBottom:16}}>
                <Image
                  source={Define.assets.Images.logocongan}
                  style={{width:160,height:170}}
                />
                <View style={{marginTop:8,justifyContent:'center',alignItems:'center'}}>
                  <Text allowFontScaling={false}
                    style={{
                      color: '#fff',
                      fontSize: 16,
                      fontFamily: Define.constants.fontBold400,
                    }}>
                    Trung Tâm Chỉ Huy Công An
                  </Text>
                  <Text allowFontScaling={false}
                    style={{
                      color: '#FFD042',
                      fontSize: 24,
                      fontFamily: Define.constants.fontBold600,
                    }}>
                    Phường Hồng Bàng
                  </Text>
                </View>
              </View>
              {this.renderLogin()}
            <TouchableOpacity
                onPress={() => {
                    if (this.state.userNameValidate && this.password.length > 6) {
                        this.login();
                    } else if (this.state.userNameValidate && !this.password) {
                        NotifyUtil.pushAlertTopNotify({
                            content: 'Vui lòng nhập mật khẩu.',
                            type: 'warning',
                            timeClose: 3000,
                        });
                    } else {
                        NotifyUtil.pushAlertTopNotify({
                            content:
                                'Vui lòng kiểm tra lại thông tin tên đăng nhập/mật khẩu',
                            type: 'warning',
                            timeClose: 3000,
                        });
                    }
                }}
                style={{
                    marginTop: 24,
                    alignItems: 'center',
                    paddingVertical: 10,
                    width: 222,
                    backgroundColor:
                        this.state.userNameValidate && this.state.passwordValidate
                            ? '#007CFE'
                            : '#b3b5b6',
                    borderRadius: 12,
                }}>
                <Text allowFontScaling={false}
                    style={{
                        fontFamily: Define.constants.fontBold500,
                        color: '#fff',
                        fontSize: 16,
                    }}>
                    Đăng nhập
                </Text>
            </TouchableOpacity>
            </View>
          </ScrollView>
        </ImageBackground>
      );
    }

    return (
      <ImageBackground source={Define.assets.Images.bgrlogin} style={{ width: Define.constants.widthScreen, height: '100%', paddingHorizontal: 16, }}>
        <ScrollView
          ref={ref => (this.scrollViewRef = ref)}
          contentContainerStyle={{flexGrow: 1}}
          keyboardShouldPersistTaps='handle'
          scrollEnabled={!this.state.bottom ? false : true}
          showsVerticalScrollIndicator={true}
        >
          <View style={{
            top: Platform.OS === 'android' ? StatusBar.currentHeight + 30 : statusBarHeight + 8,
            zIndex: 7,
            justifyContent: 'center', alignItems: 'center',
          }}>
            <View style={{justifyContent:'center',alignItems:'center',marginBottom:32}}>
              <Image
                source={Define.assets.Images.logocongan}
                style={{width:160,height:170}}
              />
              <View style={{marginTop:8,justifyContent:'center',alignItems:'center'}}>
                <Text allowFontScaling={false}
                  style={{
                    color: '#fff',
                    fontSize: 16,
                    fontFamily: Define.constants.fontBold400,
                  }}>
                  Trung Tâm Chỉ Huy Công An
                </Text>
                <Text allowFontScaling={false}
                  style={{
                    color: '#FFD042',
                    fontSize: 24,
                    fontFamily: Define.constants.fontBold600,
                  }}>
                  Phường Hồng Bàng
                </Text>
              </View>
            </View>
            {!this.state.hideImage && (
              <>
                <Image
                  source={{ uri: 'https://media-hb-dev.canbo.ai/uploads/mobiles/2025-09-11-FaceID.png' }}
                  style={{
                    width: 62,
                    height: 62,
                    tintColor: '#fff',
                    marginBottom: 12
                  }}
                />
                {this.state.scanning && (
                  <Text allowFontScaling={false}
                    style={{
                      color: '#fff',
                      fontSize: 16,
                      fontFamily: Define.constants.fontBold400,
                      textAlign: 'center',
                      marginHorizontal: 60
                    }}>
                    Đang quét xác thực...
                  </Text>
                )}
                {this.state.failed && (
                  <Text allowFontScaling={false}
                    style={{
                      color: '#FFD042',
                      fontSize: 16,
                      fontFamily: Define.constants.fontBold400,
                      textAlign: 'center',
                      marginHorizontal: 60
                    }}>
                    Không xác nhận sinh trắc học. Cán bộ vui lòng thử lại
                  </Text>
                )}
                {this.state.failed && (
                  <TouchableOpacity
                    style={{ backgroundColor: '#007CFE', borderRadius: 16, width: 222, paddingVertical: 12, marginTop: 16, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}
                    onPress={this.handleRetryFaceID}
                  >
                    <Text allowFontScaling={false}
                      style={{
                        color: '#fff',
                        fontSize: 16,
                        fontFamily: Define.constants.fontBold400,
                        textAlign: 'center',
                      }}>
                      Thử lại
                    </Text>
                  </TouchableOpacity>
                )}
                {!this.state.scanning && (
                  <TouchableOpacity
                    style={{ flexDirection: 'row', alignItems: 'center', marginTop: 16 }}
                    onPress={this.handleShowLogin}
                  >
                    <PenguinLinearIcon name='lock' size={24} color={'#fff'} />
                    <Text allowFontScaling={false}
                      style={{
                        color: '#fff',
                        fontSize: 16,
                        fontFamily: Define.constants.fontBold400,
                        marginLeft: 8
                      }}>
                      Sử dụng mật khẩu
                    </Text>
                  </TouchableOpacity>
                )}
              </>
            )}
          </View>
        </ScrollView>
      </ImageBackground>
    );
  }
  renderLogin() {
    return (
        <View
          style={{
            width: '100%',
            marginTop: 16,
          }}>
        <Text allowFontScaling={false}
          style={{
            color: '#fff',
            fontSize: 16,
            fontFamily: Define.constants.fontBold600,
            marginBottom: 12,
          }}>
          Tài khoản
        </Text>
        <View
          style={{
            flexDirection: 'row',
            backgroundColor: this.state.userNameValidate ? '#E6F8E9' : '#fff',
            alignItems: 'center',
            borderRadius: 16,
            paddingHorizontal: 18,
            height: 54,
            marginBottom: 20,
            borderWidth: 1,
            borderColor: this.state.userNameValidate ? '#fff' : '#ced6e0',
          }}>
          <PenguinLinearIcon name='user' size={24} color={'#656C75'} />
          <Text
            style={{ fontSize: 18, fontFamily: Define.constants.fontBold500, color: '#161616', paddingHorizontal: 12, alignItems: 'center',}}
          >
            {this.userName}
          </Text>
        </View>
        <Text allowFontScaling={false}
          style={{
            color: '#fff',
            fontSize: 16,
            fontFamily: Define.constants.fontBold600,
            marginTop: 8,
          }}>
          Mật khẩu
        </Text>
          <View
            style={{
              flexDirection: 'row',
              backgroundColor: '#fff',
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: 16,
              paddingHorizontal: 16,
              flex:1,
              marginTop: 8,
              borderWidth: 1,
              borderColor: '#ced6e0',
            }}>
            <PenguinLinearIcon name='lock' size={24} color={'#656C75'} />
            <TextInput
              ref={ref => (this._passwordLogin = ref)}
              placeholder={'Nhập mật khẩu'}
              textContentType={'password'}
              placeholderTextColor={'#929394'}
              secureTextEntry={this.state.checkPassword}
              returnKeyType="done"
              defaultValue={this.password}
              underlineColorAndroid="transparent"
              style={{
                flex: 1,
                fontSize: 18,
                fontFamily: Define.constants.fontBold500,
                height: 54,
                color: '#161616',
                justifyContent: 'center',
                paddingHorizontal: 12,
              }}
              onChangeText={text => {
                this.password = text;
                clearTimeout(this.timeoutPassword);
                this.timeoutPassword = setTimeout(() => {
                  if (this.password.length > 5) {
                    this.setState({passwordValidate: true});
                  } else {
                    this.setState({passwordValidate: false});
                  }
                }, 500);
              }}
              onFocus={() => {
                if (!this.state.hideImage) {
                  this.setState({
                    hideImage: true
                  })
                }
              }}
              onBlur={() => {
                if (this.state.hideImage) {
                  this.setState({
                    hideImage: false
                  })
                }
              }}
              onSubmitEditing={() => {
                Keyboard.dismiss();
                if (this.state.userNameValidate && this.password.length > 6) {
                  this.login();
                } else if (this.state.userNameValidate && !this.password) {
                  NotifyUtil.pushAlertTopNotify({
                    content: 'Vui lòng nhập mật khẩu.',
                    type: 'warning',
                    timeClose: 3000,
                  });
                } else {
                  NotifyUtil.pushAlertTopNotify({
                    content:
                      'Vui lòng kiểm tra lại thông tin mật khẩu',
                    type: 'warning',
                    timeClose: 3000,
                  });
                }
              }}
            />
            {this.password ?
              <TouchableOpacity
                onPress={() => {
                  this.password = ''
                  this.forceUpdate()
                }}
                style={{ alignItems: 'flex-end', marginRight: 16 }}>
                <PenguinBoldIcon name='close-circle' size={20} color={'#656C75'} />
              </TouchableOpacity> : null}
            <TouchableOpacity
              onPress={() => {
                this.setState({
                  checkPassword: !this.state.checkPassword
                })
              }}
              style={{ alignItems: 'flex-end' }}>
              {!this.state.checkPassword ?
                <PenguinBoldIcon name='eye' size={20} color={'#656C75'} />
                : <PenguinBoldIcon name='eye-slash' size={20} color={'#656C75'} />}
            </TouchableOpacity>
          </View>
          {!this.state.passwordValidate && this.password.length ? (
            <Text allowFontScaling={false}
              style={{
                fontFamily: Define.constants.fontBold500,
                color: '#fff',
                fontSize: 14,
              }}>
              Vui lòng điền mật khẩu ít nhất 6 kí tự
            </Text>
          ) : null}
        </View>
    );
  }

    handleNavigate = () => {
        const { dispatch, navigation, route, navigator } = this.props;
        navigation.reset({
            index: 0,
            routes: [{ name: 'MainContainer' }],
        })
    }

  async login() {
    const {dispatch, navigation, route} = this.props;
    dispatch(
      UserActions_MiddleWare.login({
        username: this.userName,
        password: this.password,
      }),
    ).then(async res => {
      let memberInfo = res.res.data;
      if (memberInfo._id) {

        SocketOrderManager.destroy();
        SocketOrderManager.reCheck();

        this.handleNavigate()
        NotifyUtil.pushAlertTopNotify({
          content: 'Đăng nhập thành công.',
          type: 'success',
          timeClose: 3000,
        });

      }
    });
  }

  keyboardWillShow = (e) => {
    const heightKeyboard = e.endCoordinates.height;
    if (Platform.OS === 'ios') {
      this.setState({
        bottom: 100,
        keyboardShow: true
      });
    } else {
      this.setState({
        keyboardShow: true
      })
    }
  }

  keyboardWillHide = (e) => {
    if (Platform.OS === 'ios') {
      // if (this.scrollViewRef) {
      //   this.scrollViewRef.scrollTo({ x: 0, y: 0, animated: false })
      // }
      this.setState({
        bottom: 0,
        keyboardShow: false
      });
    } else {
      this.setState({
        keyboardShow: false
      })
    }
  }


  UNSAFE_componentWillMount() {
    super.UNSAFE_componentWillMount();

    if (Platform.OS === 'android') {
      this.keyboardDidShowSubscription = Keyboard.addListener('keyboardDidShow', this.keyboardWillShow)
      this.keyboardDidHideSubscription = Keyboard.addListener('keyboardDidHide', this.keyboardWillHide)
    } else {
      this.keyboardDidShowSubscription = Keyboard.addListener('keyboardWillShow', this.keyboardWillShow)
      this.keyboardDidHideSubscription = Keyboard.addListener('keyboardWillHide', this.keyboardWillHide)
    }
  }

  componentDidUpdate() {
    if (Platform.OS === 'ios') {
      LayoutAnimation.easeInEaseOut();
    } else {
      // LayoutAnimation.configureNext(
      //   LayoutAnimation.create(
      //     200,
      //     LayoutAnimation.Types.linear,
      //     LayoutAnimation.Properties.opacity,
      //   ),
      // );
    }
  }
  componentWillUnmount() {
    super.componentWillUnmount();
  }
}

/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  // console.log(state)
  return {
    navigator: state.Navigator,
    user: state.User,
    tips: state.Tips,
    appState: state.AppState,
    appSetting: state.AppSetting,
  };
}

export default connect(selectActions, undefined, undefined, {withRef: true})(
  LoginFaceIDScreen,
);
