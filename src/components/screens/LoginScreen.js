
var _ = require('lodash')
//LIB
import React  from 'react';
import {
  View,
  ScrollView,
  RefreshControl,
  Button,
  Image,
  ActivityIndicator ,
  Modal,
  Platform,
  PermissionsAndroid,
  Alert,
  TouchableOpacity,
  StatusBar,
  AppState
} from 'react-native';

var {Actions} = require('react-native-router-flux');
import { connect } from 'react-redux';

import * as Animatable from 'react-native-animatable';
import LinearGradient from 'react-native-linear-gradient';
import LottieView from 'lottie-react-native';
import NetInfo from "@react-native-community/netinfo";

//action

//components
var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
var Util = require('../../Util/Util');
var NotifyUtil = require('../../Util/notify');
var Include = require('../../Include');

var {popupActions} = require('../popups/PopupManager');
var {globalVariableManager}= require('../modules/GlobalVariableManager');
import SocketOrderManager from '../modules/SocketOrder';
var locationManager = require('../modules/LocationManager');

var ButtonWrap = require('../elements/ButtonWrap');
var RDActions = require( '../../actions/RDActions');
//screens
import Screen from './Screen'

// popups
import DefaultPopup from '../popups/DefaultPopup'
import NotifyPopup from '../popups/NotifyPopup';

// actions
import UserActions_MiddleWare from '../../actions/UserActions_MiddleWare'
import AppStateActions_MiddleWare from '../../actions/AppStateActions_MiddleWare'
import { TransitionPresets } from 'react-navigation-stack';

//variable

// var styles = StyleSheet.create({
//
// })

//

class LoginScreen extends Screen{
  static componentName = 'LoginScreen'
  static sceneConfig ={
    ...Screen.sceneConfig,
    hideNavBar: true,
    ...TransitionPresets.DefaultTransition
  }
  // static defaultProps = {}
  // static propTypes = {}
  constructor(props){
    super(props);
    this.state = _.merge(this.state,
    {})
    this.checkAndRequestPermission = this.checkAndRequestPermission.bind(this);
    this.handleAfterLogin = this.handleAfterLogin.bind(this);
    this.handleNavigateScreen = this.handleNavigateScreen.bind(this);

    this.countLogin = 0;
  }
  // static renderRightButton(scene){
  //   return (
  //     <View style={Themes.current.screen.rightButtonWrapNavBar}>
  //       <Include.Text>RightButton</Include.Text>
  //     </View>
  //   )
  // }
  // static renderLeftButton(scene){
  //   return (
  //     <View style={Themes.current.screen.leftButtonWrapNavBar}>
  //       <Include.Text>LeftButton</Include.Text>
  //     </View>
  //   )
  // }
  // static renderTitle(scene){
  //   return(
  //     <View style={Themes.current.screen.titleWrapNavBarCenter}>
  //       <Include.Text style={Themes.current.text.navBartitle}>title</Include.Text>
  //     </View>
  //   )
  // }

  onRefresh(){
    super.onRefresh();
    var {dispatch} = this.props;
  }

  onGetMore(){
    super.onGetMore();
    var {dispatch} = this.props;
  }

  checkAndRequestPermission() {
    return new Promise((resolve,reject)=>{
      if (Platform.OS === 'android') {
        PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION)
        .then((ret)=>{
          if (ret) {
            return Promise.resolve(true);
          }else{
            Alert.alert(
              '',
              'Hãy cho phép HeyU sử dụng các quyền sau để việc tạo, nhận đơn trở lên dễ dàng ',
              [
                {text: 'Từ chối', onPress: () => {}, style: 'cancel'},
                {text: 'Cho Phép',
                  onPress: () => {
                    return PermissionsAndroid.requestMultiple(
                      [PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
                      PermissionsAndroid.PERMISSIONS.RECORD_AUDIO]
                    )}
                },
              ],
              { cancelable: false }
            )

          }
        })
        .then((granted)=>{
          if (granted) {
            resolve()
          } else {
            reject();
          }
        })
      }else{
        resolve()
      }
    })
  }

  setToken = (token) => {
    this.tokenPhone = token;
  }

  setPhoneCode = (code) => {
    this.phoneCode = code;
  }

  setPhone = (phone) => {
    this.phone = phone;
  }

  handleLoginWithoutPassword = () => {
    const {dispatch} = this.props;

    dispatch(UserActions_MiddleWare.loginWithoutPassword({phone: this.phone, token: this.tokenPhone, code: this.phoneCode}))
      .then(this.handleAfterLogin)
      .catch(err => {
        dispatch(AppStateActions_MiddleWare.getConfig())
        Actions.pop();
      });
  }

  getRegion = () => {
    return new Promise((resolve, reject) => {
      const {dispatch, appSetting, user} = this.props;
      const memberToken = _.get(user, 'memberInfo.member.memberToken', '');
      if(!appSetting.mode || !appSetting.regionNew || !memberToken || Date.now() - appSetting.lastTimeGetRegion <= 300000) {
        return resolve();
      }

      locationManager
        .getCurrentLocation()
        .then((location) => {
          return dispatch(UserActions_MiddleWare.getRegionByLatLng({location}))
        })
        .then((res) => {
          resolve();
        })
        .catch((err) => {
          dispatch(AppStateActions_MiddleWare.getConfig())
          if(user && user.addressPicked && user.addressPicked.location) {
            dispatch(UserActions_MiddleWare.getRegionByLatLng({location: user.addressPicked.location}))
              .then(() => {})
              .catch(err => {
                dispatch(AppStateActions_MiddleWare.getConfig())
              })
          }
          resolve();
        })
    })
  }

  handleLimitReLogin = () => {
    const {dispatch, user} = this.props;
    const notiObj = {
      _id: Date.now(),
      notifiedAt: Date.now(),
      description: 'Phiên làm việc hết hạn. Bạn vui lòng đăng nhập lại. Xin cảm ơn!',
      link: '',
      extras: {}
    }

    Actions.PhoneAuthenticationScreen({
      phone: user.phonePrediction || '',
      handleLoginWithoutPassword: this.handleLoginWithoutPassword,
      setToken: this.setToken,
      setPhoneCode: this.setPhoneCode,
      setPhone: this.setPhone
    });

    // popupActions.setRenderContentAndShow(NotifyPopup, {notiObj});
    NotifyUtil.pushNotify(notiObj)
    dispatch(UserActions_MiddleWare.logout());
  }

  checkNetworkLogin = () =>{
    var {user,appSetting, dispatch} = this.props;
    NetInfo.fetch().then(connectionInfo => {
      if((!connectionInfo.isInternetReachable && Platform.OS === 'android') || (!connectionInfo.isConnected && Platform.OS === 'ios')){
        this.countLogin++;

        if (this.countLogin > 3) {
          this.handleLimitReLogin();

          return;
        }

        NotifyUtil.pushAlertTopNotify({
          type: 'warning',
          content: 'Lỗi kết nối mạng, vui lòng kiểm tra lại. Xin cảm ơn',
          timeClose: 3000,
        })

        setTimeout(() => {
          this.checkNetworkLogin();
        }, 3000);
      } else{
        if(user?.memberInfo?.member?.memberToken){
          this.setState({reLogin: true});

          this.getRegion()
          dispatch(AppStateActions_MiddleWare.getConfig())
            .then(() => {
              dispatch(AppStateActions_MiddleWare.getConfigReview())
              this.handleReLogin();
            })
        } else{
          Actions.PhoneAuthenticationScreen({
            phone: user.phonePrediction || '',
            handleLoginWithoutPassword: this.handleLoginWithoutPassword,
            setToken: this.setToken,
            setPhoneCode: this.setPhoneCode,
            setPhone: this.setPhone
          });
        }
      }
    })
  }

  renderAutoLogin() {
    let content = null;

    if(this.state.reLogin) {
      content = (
        <View style={{position: 'absolute', alignItems:'center', justifyContent:'center', top: 0, left: 0, right: 0, bottom: 0, alignItems: 'center', justifyContent: 'center', backgroundColor:'rgba(0,0,0,0.4)'}}>
          <View style={{backgroundColor:'#fff', width:70, height:70, borderRadius:35, alignItems:'center', justifyContent:'center', elevation:2}}>
            <LottieView
              source={require('../../../assets/Animation/animationLoading')} autoPlay loop
              style={{
                width: 70,
                height: 70,
              }}/>
            <Image
              source={Define.assets.Images.logoLogin}
              resizeMode={'stretch'}
              style={{width:30, height:30, position:'absolute'}}
            />
          </View>

          <View
            style={{marginTop: 10}}>
            <Include.Text style={{fontSize: 18, color: '#1e1e1e', fontStyle: 'italic'}}>Đang đăng nhập lại...</Include.Text>
          </View>
        </View>
      )
    }

    return content;
  }
  renderScreenContent(){
    var {dispatch, user,tips, appState} = this.props;
    var content = null;

    content = (
      <View style={{flex:1, backgroundColor:'#fff', paddingTop:Platform.OS === 'ios'?0:StatusBar.currentHeight}}>
        <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
          <View style={{position: 'absolute', top: Define.constants.heightScreen / 16}}>
            <Image source={Define.assets.Images.logoHeyUNew} style={{height: 168, width: 168}}/>
          </View>
          <LottieView source={require('../../../assets/Animation/animationLoginScreen')} autoPlay style={{width: Define.constants.widthScreen, height: Define.constants.widthScreen}}/>
        </View>
      </View>
    );
    return content;
  }

  checkActivityRecognitionPermission(){
    const {appSetting} = this.props;
    return new Promise((resolve,reject)=>{
      if (Platform.OS === 'android' && appSetting.mode && appSetting.mode === 'shipper' && Platform.Version >= 29) {
        PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.ACTIVITY_RECOGNITION)
        .then((ret)=>{
          if (ret) {
            return resolve();
          }else{
            return reject();
          }
        })
      }else{
        resolve()
      }
    })
  }

  handleAfterLogin(info) {
    this.handleNavigateScreen(info, false);
  }

  handleNavigateScreen(info,needTurnOnLocation) {
    const {appSetting, dispatch, user} = this.props;
    if (!info || !info?.res) {
      const inf = {
        res: info || user?.memberInfo
      };
      info = inf;
    }
    let screenName = 'SwitchModeScreen';
    let optionsScene = {
      type: 'reset'
    };

    if(!info?.res?.member?.facebook?.name) {
      screenName = 'RegisterProfileScreen';
    } else if(appSetting.mode === 'shipper' && appSetting.regionNew && !needTurnOnLocation) {
      screenName = 'FeedsScreenContainer'
    } else if(appSetting.mode === 'shop' && appSetting.regionNew && !needTurnOnLocation) {
      dispatch(AppStateActions_MiddleWare.getConfig());

      screenName = 'ServiceAvailableListContainer'
    }
    dispatch(RDActions['AppState']['showLoadingOnRequest']({show:false}))
    Actions[screenName](optionsScene);
  }

  handleAppStateChange = (newState) => {
    switch (newState) {
      case 'active':
        break;
      case 'background':
        break;
      default:
        break;
    }
  }

  componentDidMount(){
    super.componentDidMount();
    StatusBar.setBarStyle('dark-content');

    this.checkNetworkLogin();
  }

  handleReLogin = (forceResolve = true) => {
    return new Promise((resolve, reject) => {
      const fnc = (resolve, reject) => {
        const {dispatch, navigator, user} = this.props;
        const memberToken = _.get(user, 'memberInfo.member.memberToken', '');
        dispatch(UserActions_MiddleWare.get())
          .then(this.handleAfterLogin)
          .catch(err => {
            this.countLogin++;

            const currentScreen = navigator.currentScreen.name;
            if(!forceResolve || this._isUnMount || err?.errObj?.data?.code === 1993 || currentScreen !== 'LoginScreen' || !memberToken || this.countLogin > 3) {
              if (this.countLogin > 3) {
                this.handleLimitReLogin();
              }

              return reject(err);
            }

            NotifyUtil.pushAlertTopNotify({
              type: 'warning',
              content: 'Lỗi kết nối mạng, vui lòng kiểm tra lại. Xin cảm ơn',
              timeClose: 3000,
            })

            setTimeout(() => {
              fnc(resolve, reject);
            }, 3000);
          });
      }
      fnc(resolve, reject);
    })
  }

  UNSAFE_componentWillMount() {
    super.UNSAFE_componentWillMount();
    // if(Platform.OS === 'android'){
    //   StatusBarAndroid.setHexColor('#0037f4');
    // }
    SocketOrderManager.destroy();

    this.appStateSubscription = AppState.addEventListener('change', this.handleAppStateChange);
  }
  componentWillUnmount(){
    super.componentWillUnmount();
    // StatusBar.setBarStyle('light-content');

    this.appStateSubscription.remove();
  }
}


/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  // console.log(state)
  return {
    navigator: state.Navigator,
    user: state.User,
    tips:state.Tips,
    appState: state.AppState,
    appSetting: state.AppSetting
  }
}

export default connect(selectActions, undefined, undefined, {withRef: true})(LoginScreen);