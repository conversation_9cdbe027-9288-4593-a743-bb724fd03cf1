
var _ = require('lodash')
//LIB
import React  from 'react';
import {
  View,
  ScrollView,
  RefreshControl,
  Button,
  Image,
  ActivityIndicator ,
  Modal,
  Platform,
  TextInput,
  TouchableOpacity,
  StatusBar,
  Keyboard,
  LayoutAnimation,
  SafeAreaView
} from 'react-native';

var {Actions} = require('react-native-router-flux');
import { connect } from 'react-redux';
import {Ionicons as Icon} from '@react-native-vector-icons/ionicons';
import * as Animatable from 'react-native-animatable';
import LinearGradient from 'react-native-linear-gradient';
import { Hoshi } from 'react-native-textinput-effects';

//action

//components
var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
var Util = require('../../Util/Util');
var Include = require('../../Include');
var {popupActions} = require('../popups/PopupManager');
var {globalVariableManager}= require('../modules/GlobalVariableManager');
import SocketOrderManager from '../modules/SocketOrder';

var ButtonWrap = require('../elements/ButtonWrap');

//screens
import Screen from './Screen'

// popups
import DefaultPopup from '../popups/DefaultPopup'
import DestroyTokenPopup from '../popups/DestroyTokenPopup'

// actions
import UserActions_MiddleWare from '../../actions/UserActions_MiddleWare'
import AppStateActions_MiddleWare from '../../actions/AppStateActions_MiddleWare'

//variable

// var styles = StyleSheet.create({
//
// })

//
const STEP = {
  CHECK_PHONE: 1,
  TYPE_PASS: 2,
  RESET_PASS: 3
}
class LoginByPhoneScreen extends Screen{
  static componentName = 'LoginByPhoneScreen'
  static sceneConfig ={
    ...Screen.sceneConfig,
    hideNavBar: true
  }
  // static defaultProps = {}
  // static propTypes = {}
  constructor(props){
    super(props);
    this.state = _.merge(this.state,
    {
      step: STEP.CHECK_PHONE,
      keyboardShow:false,
      bottom: 0,
      actionType:''
    })

    this.phone = this.props.user.phonePrediction || '';
    this.password = '';
    this.reTypePassword = '';
    this.token = '';
    this.tokenPhone = '';
    this.phoneCode = '';
    this.handleCheckPhone = this.handleCheckPhone.bind(this);
    this.openAccountKit = this.openAccountKit.bind(this);
    this.handleResetPassword = this.handleResetPassword.bind(this);
    this.handleLoginByPhone = this.handleLoginByPhone.bind(this);
    this.keyboardWillShow = this.keyboardWillShow.bind(this);
    this.keyboardWillHide = this.keyboardWillHide.bind(this);
    this.handlePhoneAuthenSucess = this.handlePhoneAuthenSucess.bind(this);
    this.setToken = this.setToken.bind(this);
    this.setPhoneCode = this.setPhoneCode.bind(this);
    this.setPhone = this.setPhone.bind(this);
  }
  // static renderRightButton(scene){
  //   return (
  //     <View style={Themes.current.screen.rightButtonWrapNavBar}>
  //       <Include.Text>RightButton</Include.Text>
  //     </View>
  //   )
  // }
  // static renderLeftButton(scene){
  //   return (
  //     <View style={Themes.current.screen.leftButtonWrapNavBar}>
  //       <Include.Text>LeftButton</Include.Text>
  //     </View>
  //   )
  // }
  // static renderTitle(scene){
  //   return(
  //     <View style={Themes.current.screen.titleWrapNavBarCenter}>
  //       <Include.Text style={Themes.current.text.navBartitle}>title</Include.Text>
  //     </View>
  //   )
  // }
  keyboardWillShow (e) {
    const heightKeyboard = e.endCoordinates.height;
    if (Platform.OS === 'ios') {
      this.setState({
        bottom: heightKeyboard
      });
    }
    this.setState({
      keyboardShow: true
    });
  }

  keyboardWillHide (e) {
    if (Platform.OS === 'ios') {
      this.setState({
        bottom: 0
      });
    }
    this.setState({
      keyboardShow: false
    });
  }
  onRefresh(){
    super.onRefresh();
    var {dispatch} = this.props;
  }

  onGetMore(){
    super.onGetMore();
    var {dispatch} = this.props;
  }

  handleCheckPhone() {
    const {dispatch} = this.props;
    const phone = this.phone.trim();
    if(phone) {
      dispatch(UserActions_MiddleWare.checkPhoneExist({phone}))
        .then((result) => {
          if(result.res.data.count === 1) {
            this.setState({
              actionType:'resetPassword'
            })
            if(result.res.data.hasPassword === 0) {
              this.openAccountKit();
            } else {
              this.setState({
                step: STEP.TYPE_PASS
              })
            }
          } else if (result.res.data.count === 0) {
            this.setState({
              actionType:'register'
            })
            this.openAccountKit();
          }
        })
    } else {
      popupActions.setRenderContentAndShow(DefaultPopup, {
        title: 'Thông báo',
        description: 'Vui lòng nhập số điện thoại',
        onPress: () => {
          popupActions.popPopup()
        },
        buttonTitle: 'Đồng ý'
      });
    }
  }

  handleLoginByPhone() {
    const {dispatch, appSetting} = this.props;
    if(this.password.length < 6) {
      return popupActions.setRenderContentAndShow(DefaultPopup, {
        title: 'Thông báo',
        description: 'Mật khẩu phải từ 6 ký tự trở lên',
        onPress: () => {
          popupActions.popPopup()
        },
        buttonTitle: 'Đồng ý'
      });
    }

    if(!this.phone) {
      return popupActions.setRenderContentAndShow(DefaultPopup, {
        title: 'Thông báo',
        description: 'Số điện thoại chưa hợp lệ',
        onPress: () => {
          popupActions.popPopup()
        },
        buttonTitle: 'Xong'
      });
    }

    dispatch(UserActions_MiddleWare.loginByPhone({phone: this.phone, password: this.password}))
      .then(this.props.handleAfterLogin)
  }

  handleResetPassword() {
    const {dispatch, appSetting} = this.props;

    if(!this.phone) {
      return popupActions.setRenderContentAndShow(DefaultPopup, {
        title: 'Thông báo',
        description: 'Số điện thoại chưa hợp lệ',
        onPress: () => {
          popupActions.popPopup()
        },
        buttonTitle: 'Xong'
      });
    }

    if(this.password !== this.reTypePassword) {
      return popupActions.setRenderContentAndShow(DefaultPopup, {
        title: 'Thông báo',
        description: 'Mật khẩu không trùng nhau',
        onPress: () => {
          popupActions.popPopup()
        },
        buttonTitle: 'Xong'
      });
    }

    if(this.password.length < 6) {
      return popupActions.setRenderContentAndShow(DefaultPopup, {
        title: 'Thông báo',
        description: 'Mật khẩu phải từ 6 ký tự trở lên',
        onPress: () => {
          popupActions.popPopup()
        },
        buttonTitle: 'Đồng ý'
      });
    }

      dispatch(UserActions_MiddleWare.resetPasswordByNewPhoneAuthen({phone: this.phone, newPassword: this.password, token: this.tokenPhone, code: this.phoneCode}))
        .then(this.props.handleAfterLogin)
  }

  openAccountKit() {
    let {dispatch} = this.props;

      Actions.PhoneAuthenticationScreen({
        phone: this.phone,
        handlePhoneAuthenSucess: this.handlePhoneAuthenSucess,
        setToken: this.setToken,
        setPhoneCode: this.setPhoneCode,
        setPhone: this.setPhone
      });
  }

  handlePhoneAuthenSucess() {
    if(this.state.step === STEP.CHECK_PHONE) {
      this.setState({
        step: STEP.RESET_PASS
      })
    } else if(this.state.step === STEP.TYPE_PASS) {
      this.setState({
        step: STEP.RESET_PASS
      })
    }
  }

  setToken(token) {
    this.tokenPhone = token
  }

  setPhoneCode(code) {
    this.phoneCode = code
  }

  setPhone(phone) {
    this.phone = phone;
    if(this.valueCheckPhone) {
      this.valueCheckPhone.input.current.setNativeProps({
        text: phone
      })
    }
    if(this.valuePhoneResetPass) {
      this.valuePhoneResetPass.input.current.setNativeProps({
        text: phone
      })
    }
  }

  renderScreenContent() {
    var {dispatch, user,tips, appState} = this.props;
    var content = null;
    let textButton = '';
    switch(this.state.step) {
      case STEP.CHECK_PHONE: textButton = 'TIẾP TỤC'
                             break;
      case STEP.TYPE_PASS: textButton = 'ĐĂNG NHẬP'
                             break;
      case STEP.RESET_PASS: textButton = 'CẬP NHẬT MẬT KHẨU'
                             break;
    }

    content = (
      <View style={{backgroundColor:'#fff', flex:1, paddingTop:Platform.OS === 'ios' ? 0 : StatusBar.currentHeight}}>
        <SafeAreaView style={{flex: 0}} />
        <View style={{flex: 1}}>
          <ButtonWrap
            onPress={() => {
              if(this.state.step === STEP.CHECK_PHONE) {
                Actions.pop();
                Keyboard.dismiss();
              } else {
                this.setState({
                  step: STEP.CHECK_PHONE
                })
              }
            }}>
            <View style={{ flexDirection: 'row', backgroundColor: 'transparent', alignItems: 'center', padding: 10 }}>
              <View style={{ justifyContent: 'center', alignItems: 'center', width: 20, height: 20, borderRadius: 14, borderWidth: 1, borderColor: '#707070' }}>
                <Icon name='arrow-back' style={{ color: '#707070', fontSize: 20, paddingBottom: 20 }} />
              </View>
              <Include.Text style={{color: '#707070', fontSize: 16, paddingLeft: 5}}>
                Quay lại
              </Include.Text>
            </View>
          </ButtonWrap>
          {this.state.step === STEP.CHECK_PHONE ?
            <View style={{flex:this.state.keyboardShow && Platform.OS === 'ios' ? 0.3 : 0.5, alignItems:'center', justifyContent:'center'}}>
              <Include.Text style={{fontSize: 45, color:'#1697B4'}}>Xin Chào.</Include.Text>
              <Include.Text style={{fontSize: 20, color:'#707070'}}>Hãy Đăng nhập để bắt đầu</Include.Text>
            </View>:null
          }
          <View style={{flex:1, zIndex:1, elevation:1}}>
            <ScrollView contentContainerStyle={{alignItems: 'center', flex:1,}}>
              {this.state.step === STEP.RESET_PASS && !this.state.keyboardShow ?
                <View style={{flex:1}}>
                  <Include.Text style={{color: '#828282', fontSize: 17, marginBottom: 10, backgroundColor: 'transparent', textAlign: 'center', padding: 10}}>
                      Vui lòng tạo mật khẩu để truy cập vào hệ thống, mật khẩu sẽ được sử dụng để đăng nhập lại vào hệ thống sau này
                  </Include.Text>
                </View>
              : null}
              {this.state.step === STEP.CHECK_PHONE ?
                <Hoshi
                  underlineColorAndroid ='transparent'
                  defaultValue={this.phone}
                  ref = {(ref) => this.valueCheckPhone = ref}
                  autoFocus={true}
                  borderColor={'#828282'}
                  labelStyle={{color: '#828282', fontSize:16}}
                  inputStyle = {{fontSize: 20, color: '#393939', fontWeight:'100', left: 0, padding: 1, zIndex: 1}}
                  label= {'Số điện thoại'}
                  onChangeText={(text) => this.phone = text}
                  keyboardType={'numeric'}
                  onSubmitEditing={() => {
                    this.handleCheckPhone();
                  }}
                  style={{height: 40, borderBottomColor:'#828282', borderBottomWidth:1, backgroundColor: '#fff', padding: 5, width:Define.constants.widthScreen-40}}
                  />
              : <View style={{flex:this.state.keyboardShow && Platform.OS === 'ios' ? 0.3 : 0.5, justifyContent:'center', alignItems:'center'}}>
                <Hoshi
                  underlineColorAndroid ='transparent'
                  ref = {(ref) => this.valuePhoneResetPass = ref}
                  defaultValue={this.phone}
                  borderColor={'#828282'}
                  labelStyle={{color: '#828282', fontSize:16}}
                  inputStyle = {{fontSize: 20,color:'#1697B4', fontWeight:'100', left: 0, padding: 1, zIndex: 1}}
                  label= {'Số điện thoại'}
                  editable={false}
                  keyboardType={'numeric'}
                  style={{height: 40,marginTop:10, borderBottomColor:'#828282', borderBottomWidth:1, backgroundColor: '#fff', padding: 5, width:Define.constants.widthScreen-40}}
                  />
              </View>}

              {this.state.step === STEP.TYPE_PASS?
                <Hoshi
                  underlineColorAndroid ='transparent'
                  defaultValue={''}
                  borderColor={'#828282'}
                  labelStyle={{color: '#828282', fontSize:16}}
                  inputStyle = {{fontSize: 20, fontWeight:'100', left: 0, padding: 1, zIndex: 1}}
                  label= {'Mật Khẩu'}
                  onChangeText={(text) => this.password = text}
                  secureTextEntry={true}
                  returnKeyType={'done'}
                  autoCapitalize = {'none'}
                  autoFocus={true}
                  onSubmitEditing={this.handleLoginByPhone}
                  style={{height: 40, borderBottomColor:'#828282', borderBottomWidth:1, backgroundColor: '#fff', padding: 5, width:Define.constants.widthScreen-40}}
                  />
              : null}

              {this.state.step === STEP.RESET_PASS?
                <View style={{alignSelf: 'stretch', alignItems: 'center', flex:2}}>
                  <Hoshi
                    underlineColorAndroid ='transparent'
                    borderColor={'#828282'}
                    labelStyle={{color: '#828282', fontSize:16}}
                    inputStyle = {{fontSize: 20, fontWeight:'100', left: 0, padding: 1, zIndex: 1}}
                    label= {'Mật Khẩu'}
                    defaultValue={''}
                    autoFocus={true}
                    onChangeText={(text) => this.password = text}
                    secureTextEntry={true}
                    returnKeyType={'next'}
                    autoCapitalize = {'none'}
                    blurOnSubmit = {false}
                    onSubmitEditing={() => {
                      this._textInputRetypePass.focus();
                    }}
                    style={{height: 40, borderBottomColor:'#828282', borderBottomWidth:1, backgroundColor: '#fff', padding: 5, width:Define.constants.widthScreen-40, marginBottom: 10, marginTop: 10}}
                    />

                    <Hoshi
                      ref = {ref => this._textInputRetypePass = ref}
                      underlineColorAndroid ='transparent'
                      borderColor={'#828282'}
                      labelStyle={{color: '#828282', fontSize:16}}
                      inputStyle = {{fontSize: 20, fontWeight:'100', left: 0, padding: 1, zIndex: 1}}
                      label= {'Nhập lại mật khẩu'}
                      autoCapitalize = {'none'}
                      defaultValue={''}
                      onChangeText={(text) => this.reTypePassword = text}
                      secureTextEntry={true}
                      returnKeyType={'done'}
                      onSubmitEditing={this.handleResetPassword}
                      style={{height: 40, borderBottomColor:'#828282', borderBottomWidth:1, backgroundColor: '#fff', padding: 5, width:Define.constants.widthScreen-40}}
                      />
                </View>
              : null}
              {this.state.step === STEP.TYPE_PASS ?
                <ButtonWrap
                  onPress={() => {
                    this.openAccountKit();
                  }}>
                    <View style={{paddingTop: this.state.keyboardShow && Platform.OS === 'ios' ? 15 : 40, flex:1}}>
                      <Include.Text style={{color: '#707070', fontWeight: 'bold',padding: 5, backgroundColor: 'transparent'}}>
                      Quên mật khẩu?
                      </Include.Text>
                      <Include.Text style={{color: '#707070', fontWeight: 'bold',padding: 5, backgroundColor: 'transparent'}}>
                      Hoặc chưa có mật khẩu?
                      </Include.Text>
                    </View>
                </ButtonWrap>
              : null}
            </ScrollView>
            <LinearGradient
              {...Themes.current.linearConfig}
              style={{position:'absolute',bottom:this.state.bottom,elevation:10, zIndex:10, flex:1, width: Define.constants.widthScreen, height: Define.constants.heightScreen/10,}}>
              <TouchableOpacity
                style={{flex:1,  backgroundColor:'transparent', width: Define.constants.widthScreen, height: Define.constants.heightScreen/10,alignItems: 'center', justifyContent: 'center'}}
                onPress={() => {
                  if(this.state.step === STEP.CHECK_PHONE) {
                    this.handleCheckPhone();
                  } else if(this.state.step === STEP.TYPE_PASS){
                    this.handleLoginByPhone();
                  } else {
                    this.handleResetPassword();
                  }
                }}>
                <Include.Text style={{color: '#fff', fontWeight: 'bold', backgroundColor:'transparent'}}>
                  {textButton}
                </Include.Text>
              </TouchableOpacity>
            </LinearGradient>
          </View>
        </View>
        {!this.state.bottom ? <SafeAreaView style={{flex: 0}} /> : null}
      </View>
    );
    return content;
  }
  componentDidMount(){
    super.componentDidMount();
  }
  UNSAFE_componentWillMount() {
    super.UNSAFE_componentWillMount()

    if (Platform.OS === 'android') {
      this.keyboardDidShowSubscription = Keyboard.addListener('keyboardDidShow', this.keyboardWillShow)
      this.keyboardDidHideSubscription = Keyboard.addListener('keyboardDidHide', this.keyboardWillHide)
    }else{
      this.keyboardDidShowSubscription = Keyboard.addListener('keyboardWillShow', this.keyboardWillShow)
      this.keyboardDidHideSubscription = Keyboard.addListener('keyboardWillHide', this.keyboardWillHide)
    }
  }
  componentDidUpdate() {
    if(Platform.OS === 'ios') {
      LayoutAnimation.easeInEaseOut();
    } else {
      LayoutAnimation.configureNext({
        duration: 300,
        update: {
          type: LayoutAnimation.Types.linear
        }
      });
    }
  }
  componentWillUnmount() {
    super.componentWillUnmount()

    this.keyboardDidShowSubscription.remove()
    this.keyboardDidHideSubscription.remove()
  }
}


/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  // console.log(state)
  return {
    navigator: state.Navigator,
    user: state.User,
    tips:state.Tips,
    appState: state.AppState,
    appSetting: state.AppSetting
  }
}

export default connect(selectActions, undefined, undefined, {withRef: true})(LoginByPhoneScreen);
