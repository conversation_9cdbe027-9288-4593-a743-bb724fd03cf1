var _ = require('lodash');
const ms = require('ms');
import moment from 'moment';
//LIB
import React from 'react';
import {
  View,
  ScrollView,
  RefreshControl,
  Button,
  Image,
  ActivityIndicator,
  Modal,
  Platform,
  TextInput,
  TouchableOpacity,
  StatusBar,
  Keyboard,
  LayoutAnimation,
  SafeAreaView,
  Text,
  ImageBackground,
} from 'react-native';
const DeviceInfo = require('react-native-device-info');

var {Actions} = require('react-native-router-flux');
import {connect} from 'react-redux';
import * as Animatable from 'react-native-animatable';
import LinearGradient from 'react-native-linear-gradient';
import {Hoshi} from 'react-native-textinput-effects';
import {Fontisto as IconX} from '@react-native-vector-icons/fontisto';
import PenguinLinearIcon from '../elements/PenguinLinearIcon/PenguinLinearIcon';
import PenguinBoldIcon from '../elements/PenguinBoldIcon/PenguinBoldIcon';
import DatePicker from 'react-native-date-picker';
import * as Keychain from 'react-native-keychain';
import ReactNativeBiometrics from 'react-native-biometrics';

//action

//components
var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
var Util = require('../../Util/Util');
var Include = require('../../Include');
var NotifyUtil = require('../../Util/notify');
var {popupActions} = require('../popups/PopupManager');
var {globalVariableManager} = require('../modules/GlobalVariableManager');
import SocketOrderManager from '../modules/SocketOrder';
import OTPInputView from '@twotalltotems/react-native-otp-input';
var ButtonWrap = require('../elements/ButtonWrap');

//screens
import Screen from './Screen';

// popups
import DefaultPopup from '../popups/DefaultPopup';

// actions
import UserActions_MiddleWare from '../../actions/UserActions_MiddleWare';
import AppStateActions_MiddleWare from '../../actions/AppStateActions_MiddleWare';
import IHeyUActions_MiddleWare from '../../actions/IHeyUActions_MiddleWare';
import HeyUIcon from '../elements/HeyUIcon/HeyUIcon';
//variable
import {getStatusBarHeight} from 'react-native-status-bar-height';
import ParallaxScrollView from 'react-native-parallax-scroll-view';
import RDActions from '../../actions/RDActions';

// var styles = StyleSheet.create({
//
// })

//
const STEP = {
  LOGIN: 1,
  FORGOTPW: 3,
  RESET_PASS: 4,
  GET_OTP: 6,
  GET_PASSWORD: 0
};
class LoginAccountScreen extends Screen {
  static componentName = 'LoginAccountScreen';
  static sceneConfig = {
    ...Screen.sceneConfig,
    hideNavBar: true,
  };
  // static defaultProps = {}
  // static propTypes = {}
  constructor(props) {
    super(props);
    this.state = _.merge(this.state, {
      saveLoginInfo: false,
      step: this.props.route.params?.FORGOTPW ? STEP.FORGOTPW : STEP.LOGIN,
      phone: '',
      userNameValidate: false,
      passwordValidate: false,
      newPasswordValidate: false,
      confirmPasswordValidate: false,
      timeToResend: 0,
      type: 0,
      token: '',
      sent: false,
      name:'',
      email:'',
      birthday:'',
      openDatePicker: false,
      cccd:'',
      hideImage: false,
      checkPassword: true,
      checkPasswordConfirm: true,
      checkNewPassword: true,
      bottom: 0,
      confirmPhone: true,
      confirmEmail: false,
    });
    this.password = '';
    this.userName = '';
    this.oldPassword = '';
    this.newPassword = '';
    this.confirmPassword = '';
    this.timeResend = ms('3m');
    this.intervalResend = null;
  }
  startIntervalResend = () => {
    clearInterval(this.intervalResend);
    this.setState({
      timeToResend: Date.now() + this.timeResend,
    });
    this.intervalResend = setInterval(() => {
      if (this.state.timeToResend > 0) {
        this.setState({
          timeToResend: this.state.timeToResend - 1,
        });
      } else {
        if (this.intervalResend) {
          clearInterval(this.intervalResend);
        }
      }
    }, 1000);
  };
  onRefresh() {
    super.onRefresh();
    var {dispatch} = this.props;
  }

  onGetMore() {
    super.onGetMore();
    var {dispatch} = this.props;
  }

  renderScreenContent() {
    var {dispatch, user, tips, appState, navigation} = this.props;
    var content = null;
    // if(this.state.step === STEP.LOGIN){
    const statusBarHeight = Platform.OS === 'ios' ? getStatusBarHeight(true) : StatusBar.currentHeight;
      content = (
        <ImageBackground source={Define.assets.Images.bgrlogin} style={{ width: Define.constants.widthScreen, height: '100%', paddingHorizontal: 16, }}>
          <ScrollView
            ref={ref => (this.scrollViewRef = ref)}
            contentContainerStyle={{flexGrow: 1}}
            keyboardShouldPersistTaps='handle'
            scrollEnabled={!this.state.bottom ? false : true}
            showsVerticalScrollIndicator={true}
          >
          <View
            style={{
              position: 'absolute',
              top: Platform.OS === 'android' ? StatusBar.currentHeight + 8 : statusBarHeight + 8,
              zIndex: 7,
              justifyContent: 'center', alignItems: 'center',
            }}>
            {this.state.step !== STEP.LOGIN && this.state.step !== STEP.FORGOTPW?
            <TouchableOpacity
              onPress={() => {
                if (
                  this.state.step === STEP.FORGOTPW ||
                  (this.state.step === STEP.GET_OTP &&
                    this.state.type === STEP.LOGIN)
                ) {
                  this.setState({
                    step: STEP.LOGIN,
                  });
                } else if (
                  this.state.step === STEP.GET_OTP
                ) {
                  this.setState({
                    step: STEP.LOGIN,
                  });
                } else if (
                  this.state.step === STEP.GET_OTP &&
                  this.state.type === STEP.FORGOTPW
                ) {
                  this.setState({
                    step: STEP.FORGOTPW,
                  });
                }
              }}
              style={{
                width: 40,
                height: 40,
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: '#fff',
                borderRadius: 32,
                zIndex: 3,
              }}>
              <PenguinLinearIcon name={'arrow-left'} color={'#012548'} size={24} />
            </TouchableOpacity> : null }
          </View>
        <View style={{
            position: 'absolute',
            top: Platform.OS === 'android' ? StatusBar.currentHeight + 30 : statusBarHeight + 8,
            zIndex: 7,
            justifyContent: 'center', alignItems: 'center',
        }}>
        <View
          style={{
            justifyContent:'center',
            alignItems:'center',
            marginBottom: 16
          }}>
          <Image
            source={Define.assets.Images.logocongan}
            style={{
              width: 61,
              height: 61,
            }}
          />
          <View style={{marginTop: 8, justifyContent: 'center', alignItems: 'center'}}>
            <Text allowFontScaling={false}
              style={{
                color: '#fff',
                fontSize: 16,
                fontFamily: Define.constants.fontBold400,
              }}>
              Trung Tâm Chỉ Huy Công An
            </Text>
            <Text allowFontScaling={false}
              style={{
                color: '#FFD042',
                fontSize: 24,
                fontFamily: Define.constants.fontBold600,
              }}>
              Phường Hồng Bàng
            </Text>
          </View>
          </View>
          {this.state.step === STEP.LOGIN ? this.renderLogin() : null}
          {this.state.step === STEP.GET_OTP ? this.renderOTP() : null}
          {this.state.step === STEP.FORGOTPW ? this.renderForgotPassword() : null}
          {this.state.step === STEP.RESET_PASS
            ? this.renderResetPassword()
            : null}
        </View>
        </ScrollView>
        </ImageBackground>
      );
    return content;
  }
  renderLogin() {
    return (
      <View
      keyboardShouldPersistTaps='handle'
      style={{
        backgroundColor: '#FFFFFF',
        paddingHorizontal: 16,
        borderRadius: 16,
        zIndex: 5,
        paddingVertical: 16,
        bottom: this.state.bottom,
        }}>
        <View
          style={{
            backgroundColor: '#fff',
            // justifyContent:'center',
            // alignItems:'center',
          }}>
           <Text allowFontScaling={false}
            style={{
              color: '#012548',
              fontSize: 20,
              fontFamily: Define.constants.fontBold600,
              marginBottom:8,
              textAlign:'center'
            }}>
            Đăng nhập
          </Text>
          <Text allowFontScaling={false}
            style={{
              color: '#012548',
              fontSize: 16,
              fontFamily: Define.constants.fontBold600,
              marginBottom:8,
            }}>
            Tài khoản <Include.Text style={{color: '#E93940'}}>*</Include.Text>
          </Text>
          <View
            style={{
              flexDirection: 'row',
              backgroundColor: this.state.userNameValidate ? '#E6F8E9' : '#fff',
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: 16,
              paddingHorizontal: 18,
              marginBottom: 16,
              borderWidth: 1,
              borderColor: this.state.userNameValidate ? '#fff' : '#ced6e0',
            }}>
            <PenguinLinearIcon name='user' size={24} color={'#656C75'} />
            <TextInput
              ref={ref => (this.phoneRegister = ref)}
              placeholder={'Tên đăng nhập'}
              placeholderTextColor={'#929394'}
              underlineColorAndroid="transparent"
              defaultValue={this.userName}
              autoCapitalize="none"
              returnKeyType="next"
              style={{
                flex: 1,
                fontSize: 18,
                fontFamily: Define.constants.fontBold400,
                height: 56,
                paddingHorizontal: 12,
                color: this.state.userNameValidate ? '#008000' : '#161616',
                justifyContent: 'center',
              }}
              onChangeText={text => {
                this.userName = text.toLowerCase();
                if (text.length >= 5) {
                  this.setState({userNameValidate: true});
                } else {
                  this.setState({userNameValidate: false});
                }
              }}
              onSubmitEditing={() => {
                this._passwordLogin && this._passwordLogin.focus();
              }}
              onFocus={() => {
                if (!this.state.hideImage) {
                  this.setState({
                    hideImage: true
                  })
                }
              }}
              onBlur={() => {
                if (this.state.hideImage) {
                  this.setState({
                    hideImage: false
                  })
                }
              }}
            />
            {this.userName ? (
              this.state.userNameValidate ? (
                <View
                  style={{
                    width: 20,
                    height: 20,
                    borderRadius: 10,
                    backgroundColor: '#44AB49',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}>
                  <IconX name={'check'} size={8} color={'#fff'} />
                </View>
              ) : (
                <TouchableOpacity
                  style={{
                    width: 20,
                    height: 20,
                    borderRadius: 10,
                    backgroundColor: '#f3f3f3',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                  onPress={() => {
                    this.setState({ phone: '' });
                  }}>
                  <IconX name={'close-a'} size={8} color={'#708090'} />
                </TouchableOpacity>
              )
            ) : null}
          </View>
          <Text allowFontScaling={false}
            style={{
              color: '#012548',
              fontSize: 16,
              fontFamily: Define.constants.fontBold600,
            }}>
            Mật khẩu <Include.Text style={{color: '#E93940'}}>*</Include.Text>
          </Text>
          <View
            style={{
              flexDirection: 'row',
              backgroundColor: '#fff',
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: 16,
              paddingHorizontal: 16,
              marginTop: 8,
              borderWidth: 1,
              borderColor: '#ced6e0',
            }}>
            <PenguinLinearIcon name='lock' size={24} color={'#656C75'} />
            <TextInput
              ref={ref => (this._passwordLogin = ref)}
              placeholder={'Nhập mật khẩu'}
              textContentType={'password'}
              placeholderTextColor={'#929394'}
              secureTextEntry={this.state.checkPassword}
              returnKeyType="done"
              defaultValue={this.password}
              underlineColorAndroid="transparent"
              style={{
                flex: 1,
                fontSize: 18,
                fontFamily: Define.constants.fontBold500,
                height: 54,
                color: '#161616',
                justifyContent: 'center',
                paddingHorizontal: 12,
              }}
              onChangeText={text => {
                this.password = text;
                clearTimeout(this.timeoutPassword);
                this.timeoutPassword = setTimeout(() => {
                  if (this.password.length > 5) {
                    this.setState({passwordValidate: true});
                  } else {
                    this.setState({passwordValidate: false});
                  }
                }, 500);
              }}
              onFocus={() => {
                if (!this.state.hideImage) {
                  this.setState({
                    hideImage: true
                  })
                }
              }}
              onBlur={() => {
                if (this.state.hideImage) {
                  this.setState({
                    hideImage: false
                  })
                }
              }}
              onSubmitEditing={() => {
                Keyboard.dismiss();
                if (this.state.userNameValidate && this.password.length > 5) {
                  this.login();
                } else if (this.state.userNameValidate && !this.password) {
                  NotifyUtil.pushAlertTopNotify({
                    content: 'Vui lòng nhập mật khẩu.',
                    type: 'warning',
                    timeClose: 3000,
                  });
                } else {
                  NotifyUtil.pushAlertTopNotify({
                    content:
                      'Vui lòng kiểm tra lại thông tin tên đăng nhập/mật khẩu',
                    type: 'warning',
                    timeClose: 3000,
                  });
                }
              }}
            />
            {this.password ?
              <TouchableOpacity
                onPress={() => {
                  this.password = ''
                  this.forceUpdate()
                }}
                style={{ alignItems: 'flex-end', marginRight: 16 }}>
                <PenguinBoldIcon name='close-circle' size={20} color={'#656C75'} />
              </TouchableOpacity> : null}
            <TouchableOpacity
              onPress={() => {
                this.setState({
                  checkPassword: !this.state.checkPassword
                })
              }}
              style={{ alignItems: 'flex-end' }}>
              {!this.state.checkPassword ?
                <PenguinBoldIcon name='eye' size={20} color={'#656C75'} />
                : <PenguinBoldIcon name='eye-slash' size={20} color={'#656C75'} />}
            </TouchableOpacity>
          </View>
          {!this.state.passwordValidate && this.password.length ? (
            <Text allowFontScaling={false}
              style={{
                fontFamily: Define.constants.fontBold500,
                color: '#D30500',
                fontSize: 14,
              }}>
              Vui lòng điền mật khẩu ít nhất 6 kí tự
            </Text>
          ) : null}
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginTop: 16,
            }}>
            {/* <TouchableOpacity
              onPress={() => {
                this.setState({saveLoginInfo: !this.state.saveLoginInfo});
              }}
              style={{flexDirection: 'row', alignItems: 'center'}}>
              {this.state.saveLoginInfo ? (
                <PenguinBoldIcon
                  name={'tick-square'}
                  size={24}
                  color={'#1589D8'}
                />
              ) : (
                <View
                  style={{
                    width: 24,
                    height: 24,
                    borderWidth: 1,
                    borderRadius: 6,
                    borderColor: '#929394',
                    alignItems: 'center',
                  }}
                />
              )}
              <Text allowFontScaling={false}
                style={{
                  fontFamily: Define.constants.fontBold500,
                  color: '#2E3236',
                  marginLeft: 12,
                  fontSize: 16,
                }}>
                Ghi nhớ đăng nhập
              </Text>
            </TouchableOpacity> */}
            <View style={{flex: 1, alignItems: 'flex-end'}}>
              <TouchableOpacity
                onPress={() => {
                  this.setState({step: STEP.FORGOTPW, sent: false},()=>{
                    setTimeout(() => {
                      this.phoneForgotPassword && this.phoneForgotPassword.focus()
                    }, 100)
                  });
                }}>
                <Text allowFontScaling={false}
                  style={{
                    fontFamily: Define.constants.fontBold600,
                    color: '#007CFE',
                    fontSize: 15,
                  }}>
                  Quên mật khẩu?
                </Text>
              </TouchableOpacity>
            </View>
          </View>
          <TouchableOpacity
            onPress={() => {
              if (this.state.userNameValidate && this.password.length > 5) {
                this.login();
              } else if (this.state.userNameValidate && !this.password) {
                NotifyUtil.pushAlertTopNotify({
                  content: 'Vui lòng nhập mật khẩu.',
                  type: 'warning',
                  timeClose: 3000,
                });
              } else {
                NotifyUtil.pushAlertTopNotify({
                  content:
                    'Vui lòng kiểm tra lại thông tin tên đăng nhập/mật khẩu',
                  type: 'warning',
                  timeClose: 3000,
                });
              }
            }}
            style={{
              marginTop: 16,
              alignItems: 'center',
              paddingVertical: 10,
              width: Define.constants.widthScreen - 64,
              backgroundColor:
                this.state.userNameValidate && this.state.passwordValidate
                  ? '#007CFE'
                  : '#b3b5b6',
              borderRadius: 12,
            }}>
            <Text allowFontScaling={false}
              style={{
                fontFamily: Define.constants.fontBold500,
                color: '#fff',
                fontSize: 16,
              }}>
              Đăng nhập
            </Text>
          </TouchableOpacity>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginVertical: 12,
          }}>
            <View style={{
              flex: 1,
              height: 1,
              backgroundColor: '#E0E0E0',
            }} />
            <Text allowFontScaling={false} style={{
              marginHorizontal: 8,
              color: '#656C75',
              fontSize: 14,
              fontFamily: Define.constants.fontBold400,
            }}>Hoặc</Text>
            <View style={{
              flex: 1,
              height: 1,
              backgroundColor: '#E0E0E0',
            }} />
          </View>
          <TouchableOpacity
            onPress={() => {
              popupActions.setRenderContentAndShow(DefaultPopup, {
                title: 'Thông báo',
                description: 'Tính năng xác thực bằng tài khoản định danh điện tử sẽ được triển khai ở các phiên bản tiếp theo.',
                buttonTitle: 'Đóng',
                onPress: () => {
                  popupActions.popPopup();
                },
              })
            }}
            style={{
              alignItems: 'center',
              justifyContent: 'center',
              paddingVertical: 10,
              width: Define.constants.widthScreen - 64,
              backgroundColor: '#fff',
              borderRadius: 12,
              borderWidth: 1,
              borderColor: '#1589d8',
              flexDirection: 'row'
            }}>
            <Image source={{ uri: 'https://media.heyu.asia/uploads/mobiles/2025-01-08-iconvneid.png' }} style={{ width: 25, height: 25, marginRight: 12 }}></Image>
            <Text allowFontScaling={false}
              style={{
                fontFamily: Define.constants.fontBold500,
                color: '#021E38',
                fontSize: 16,
              }}>
              Đăng nhập bằng tài khoản VNeID
            </Text>
          </TouchableOpacity>
          {/* <Text allowFontScaling={false}
            style={{
              color: '#929394',
              fontSize: 14,
              fontFamily: Define.constants.fontBold400,
              paddingTop: 24
            }}>
              {`Phiên bản thử nghiệm ${Platform.OS === 'ios' ? 'iOS' : 'Android'} v${DeviceInfo.getVersion()}`}
          </Text> */}
        </View>
      </View>
    );
  }

  renderResetPassword() {
    return (
      <View
      keyboardShouldPersistTaps='handle'
      style={{
        backgroundColor: '#FFFFFF',
        paddingHorizontal: 16,
        borderRadius: 16,
        zIndex: 5,
        paddingVertical: 16,
        width: Define.constants.widthScreen - 32,
        }}>
        <View
          style={{
            backgroundColor: '#fff',
            // justifyContent:'center',
            // alignItems:'center',
          }}>
          <Text allowFontScaling={false}
            style={{
              color: '#143250',
              fontSize: 20,
              fontFamily: Define.constants.fontBold600,
              textAlign: 'center',
              marginBottom: 16,
            }}>
            Quên mật khẩu
          </Text>
        <Text allowFontScaling={false}
          style={{
            color: '#143250',
            fontSize: 16,
            fontFamily: Define.constants.fontBold600,
            marginTop: 8,
          }}>
          Mật khẩu mới <Include.Text style={{color: '#E93940'}}>*</Include.Text>
        </Text>
        <View
          style={{
            flexDirection: 'row',
            backgroundColor: '#fff',
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: 16,
            paddingHorizontal: 18,
            marginTop: 16,
            borderWidth: 1,
            borderColor: '#ced6e0',
          }}>
          <TextInput
            ref={ref => (this._password = ref)}
            placeholder={'Mật khẩu mới'}
            textContentType={'password'}
            placeholderTextColor={'#929394'}
            secureTextEntry={this.state.checkNewPassword}
            defaultValue={this.newPassword}
            underlineColorAndroid="transparent"
            style={{
              flex: 1,
              fontSize: 18,
              fontFamily: Define.constants.fontBold500,
              height: 56,
              color: '#161616',
              justifyContent: 'center',
            }}
            onFocus={() => {

            }}
            onBlur={() => {

            }}
            onSubmitEditing={() => {
              this._confirmPassword &&
                this._confirmPassword.focus();
            }}
            onChangeText={text => {
              this.newPassword = text;
              clearTimeout(this.timeoutNewPassword);
              this.timeoutNewPassword = setTimeout(() => {
                if (this.newPassword.length > 5) {
                  this.setState({newPasswordValidate: true});
                } else {
                  this.setState({newPasswordValidate: false});
                }
              }, 500);
            }}
          />
          {this.newPassword ?
            <TouchableOpacity
              onPress={() => {
                this.newPassword = ''
                this.forceUpdate()
              }}
              style={{ alignItems: 'flex-end', marginRight: 16 }}>
              <PenguinBoldIcon name='close-circle' size={20} color={'#656C75'} />
            </TouchableOpacity> : null}
          <TouchableOpacity
            onPress={() => {
              this.setState({
                checkNewPassword: !this.state.checkNewPassword
              })
            }}
            style={{ alignItems: 'flex-end' }}>
            {!this.state.checkNewPassword ?
              <PenguinBoldIcon name='eye' size={20} color={'#656C75'} />
              : <PenguinBoldIcon name='eye-slash' size={20} color={'#656C75'} />}
          </TouchableOpacity>
        </View>
        {!this.state.newPasswordValidate && this.newPassword.length ? (
          <Text allowFontScaling={false}
            style={{
              fontFamily: Define.constants.fontBold500,
              color: '#D30500',
              fontSize: 14,
              marginTop: 8,
            }}>
            Vui lòng điền mật khẩu ít nhất 6 kí tự
          </Text>
        ) : null}
        <Text allowFontScaling={false}
          style={{
            color: '#143250',
            fontSize: 16,
            fontFamily: Define.constants.fontBold600,
            marginTop: 16,
          }}>
          Nhập lại mật khẩu <Include.Text style={{color: '#E93940'}}>*</Include.Text>
        </Text>
        <View
          style={{
            flexDirection: 'row',
            backgroundColor: '#fff',
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: 16,
            paddingHorizontal: 18,
            borderWidth: 1,
            borderColor: '#ced6e0',
            marginTop: 16,
          }}>
          <TextInput
            ref={ref => (this._confirmPassword = ref)}
            placeholder={'Nhập lại mật khẩu'}
            textContentType={'password'}
            placeholderTextColor={'#929394'}
            secureTextEntry={this.state.checkPasswordConfirm}
            defaultValue={this.confirmPassword}
            underlineColorAndroid="transparent"
            style={{
              flex: 1,
              fontSize: 18,
              fontFamily: Define.constants.fontBold500,
              height: 56,
              color: '#161616',
              justifyContent: 'center',
            }}
            onFocus={() => {
            }}
            onBlur={() => {
            }}
            onSubmitEditing={() => {
              Keyboard.dismiss()
            }}
            onChangeText={text => {
              this.confirmPassword = text;
              clearTimeout(this.timeoutConfirmPassword);
              this.timeoutConfirmPassword = setTimeout(() => {
                if (this.confirmPassword.length > 5) {
                  this.setState({confirmPasswordValidate: true});
                } else {
                  this.setState({confirmPasswordValidate: false});
                }
              }, 500);
            }}
          />
          {this.confirmPassword?
            <TouchableOpacity
              onPress={() => {
                this.confirmPassword = ''
                this.forceUpdate()
              }}
              style={{ alignItems: 'flex-end', marginRight: 16 }}>
              <PenguinBoldIcon name='close-circle' size={20} color={'#656C75'} />
            </TouchableOpacity> : null}
          <TouchableOpacity
            onPress={() => {
              this.setState({
                checkPasswordConfirm: !this.state.checkPasswordConfirm
              })
            }}
            style={{ alignItems: 'flex-end' }}>
            {!this.state.checkPasswordConfirm ?
              <PenguinBoldIcon name='eye' size={20} color={'#656C75'} />
              : <PenguinBoldIcon name='eye-slash' size={20} color={'#656C75'} />}
          </TouchableOpacity>
        </View>
        {!this.state.confirmPasswordValidate && this.confirmPassword.length ? (
          <Text allowFontScaling={false}
            style={{
              fontFamily: Define.constants.fontBold500,
              color: '#D30500',
              fontSize: 14,
              marginTop: 8,
            }}>
            Vui lòng điền mật khẩu ít nhất 6 kí tự
          </Text>
        ) : null}
        {!_.isEqual(this.newPassword, this.confirmPassword) &&
        this.confirmPassword.length > 5 ? (
          <Text allowFontScaling={false}
            style={{
              fontFamily: Define.constants.fontBold500,
              color: '#D30500',
              fontSize: 14,
              marginTop: 8,
            }}>
            Nhập lại mật khẩu chưa trùng khớp
          </Text>
        ) : null}
        <TouchableOpacity
          onPress={() => {
            if (
              this.newPassword.length > 5 &&
              this.confirmPassword.length > 5 &&
              _.isEqual(this.newPassword, this.confirmPassword)
            ) {
              this.resetPassword();
            } else {
              NotifyUtil.pushAlertTopNotify({
                content: 'Mật khẩu chưa trùng khớp, vui lòng nhập lại.',
                type: 'warning',
                timeClose: 3000,
              });
            }
          }}
          style={{
            marginTop: 24,
            alignItems: 'center',
            paddingVertical: 12,
            backgroundColor:
              this.newPassword.length > 5 &&
              this.confirmPassword.length > 5 &&
              _.isEqual(this.newPassword, this.confirmPassword)
                ? '#007CFE'
                : '#b3b5b6',
            borderRadius: 12,
          }}>
          <Text allowFontScaling={false}
            style={{
              fontFamily: Define.constants.fontBold500,
              color: '#fff',
              fontSize: 16,
            }}>
            Xác nhận
          </Text>
        </TouchableOpacity>
      </View>
      </View>
    );
  }
  renderForgotPassword() {
    return (
      <View
      keyboardShouldPersistTaps='handle'
      style={{
        backgroundColor: '#FFFFFF',
        paddingHorizontal: 16,
        borderRadius: 16,
        zIndex: 5,
        paddingVertical: 16,
        width: Define.constants.widthScreen - 32,
        }}>
        <View
          style={{
            backgroundColor: '#fff',
            // justifyContent:'center',
            // alignItems:'center',
          }}>
          <Text allowFontScaling={false}
            style={{
              color: '#143250',
              fontSize: 20,
              fontFamily: Define.constants.fontBold600,
              textAlign: 'center',
              marginBottom: 16,
            }}>
            Quên mật khẩu
          </Text>
          <Text allowFontScaling={false}
            style={{
              color: '#012548',
              fontSize: 16,
              fontFamily: Define.constants.fontBold600,
              marginBottom:12,
            }}>
            Tài khoản <Include.Text style={{color: '#E93940'}}>*</Include.Text>
          </Text>
          <View
            style={{
              flexDirection: 'row',
              backgroundColor: this.state.userNameValidate ? '#E6F8E9' : '#fff',
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: 16,
              paddingHorizontal: 18,
              marginBottom: 20,
              borderWidth: 1,
              borderColor: this.state.userNameValidate ? '#fff' : '#ced6e0',
            }}>
            <PenguinLinearIcon name='user' size={24} color={'#656C75'} />
            <TextInput
              ref={ref => (this.phoneForgotPassword = ref)}
              placeholder={'Tên đăng nhập'}
              autoCapitalize="none"
              placeholderTextColor={'#929394'}
              underlineColorAndroid="transparent"
              defaultValue={this.userName}
              style={{
                flex: 1,
                fontSize: 18,
                fontFamily: Define.constants.fontBold400,
                height: 56,
                paddingHorizontal: 12,
                color: this.state.userNameValidate ? '#008000' : '#161616',
                justifyContent: 'center',
              }}
              onFocus={() => {
                if (!this.state.hideImage) {
                  this.setState({
                    hideImage: true
                  })
                }
              }}
              onBlur={() => {
                if (this.state.hideImage) {
                  this.setState({
                    hideImage: false
                  })
                }
              }}
              onChangeText={text => {
                this.userName = text.toLowerCase();
                if (text.length >= 5) {
                  this.setState({userNameValidate: true});
                } else {
                  this.setState({userNameValidate: false});
                }
              }}
            />
            {this.userName ? (
              this.state.userNameValidate ? (
                <View
                  style={{
                    width: 20,
                    height: 20,
                    borderRadius: 10,
                    backgroundColor: '#44AB49',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}>
                  <IconX name={'check'} size={8} color={'#fff'} />
                </View>
              ) : (
                <TouchableOpacity
                  style={{
                    width: 20,
                    height: 20,
                    borderRadius: 10,
                    backgroundColor: '#f3f3f3',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                  onPress={() => {
                    this.userName = '';
                  }}>
                  <IconX name={'close-a'} size={8} color={'#708090'} />
                </TouchableOpacity>
              )
            ) : null}
          </View>
          <Text allowFontScaling={false}
            style={{
              color: '#143250',
              fontSize: 16,
              fontFamily: Define.constants.fontBold600,
            }}>
            Hình thức nhận mã xác nhận
          </Text>
          <TouchableOpacity
            onPress={() => {
              this.setState({ confirmPhone: !this.state.confirmPhone, confirmEmail: false, });
            }}
            style={{ flexDirection: 'row', marginTop: 12, alignItems: 'center', borderColor: this.state.confirmPhone ? '#1589d8' : '#CCCFD3', borderWidth: 1, borderRadius: 16, backgroundColor: '#fff', padding: 12, justifyContent: 'space-between' }}>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Image
                source={{ uri: 'https://media.icongdanso.vn/uploads/mobiles/2025-08-15-sodienthoai.png' }}
                style={{
                  width: 24,
                  height: 24,
                  marginRight: 8,
                }}
              />
              <View>
                <Text allowFontScaling={false}
                  style={{
                    color: '#143250',
                    fontSize: 16,
                    fontFamily: Define.constants.fontBold600,
                  }}>
                  Nhận qua số điện thoại
                </Text>
                <Text allowFontScaling={false}
                  style={{
                    color: '#656C75',
                    fontSize: 14,
                    fontFamily: Define.constants.fontBold400,
                  }}>
                  Nhận mã xác nhận qua SDT
                </Text>
              </View>
            </View>
            <View style={{ height: 20, width: 20, borderWidth: 2, borderColor: this.state.confirmPhone ? '#1589d8' : '#2E3236', borderRadius: 20, alignItems: 'center', justifyContent: 'center' }}>
              <View style={{ height: 10, width: 10, backgroundColor: this.state.confirmPhone ? '#1589d8' : '#fff', borderRadius: 20, alignItems: 'center' }}>
              </View>
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              this.setState({ confirmEmail: !this.state.confirmEmail, confirmPhone: false, });
            }}
            style={{ flexDirection: 'row', marginTop: 16, alignItems: 'center', borderColor: this.state.confirmEmail ? '#1589d8' : '#CCCFD3', borderWidth: 1, borderRadius: 16, backgroundColor: '#fff', padding: 12, justifyContent: 'space-between' }}>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Image
                source={{ uri: 'https://media.icongdanso.vn/uploads/mobiles/2025-08-15-email.png' }}
                style={{
                  width: 24,
                  height: 24,
                  marginRight: 8,
                }}
              />
              <View>
                <Text allowFontScaling={false}
                  style={{
                    color: '#143250',
                    fontSize: 16,
                    fontFamily: Define.constants.fontBold600,
                  }}>
                  Nhận qua email
                </Text>
                <Text allowFontScaling={false}
                  style={{
                    color: '#656C75',
                    fontSize: 14,
                    fontFamily: Define.constants.fontBold400,
                  }}>
                  Nhận mã xác nhận qua địa chỉ email
                </Text>
              </View>
            </View>
            <View style={{ height: 20, width: 20, borderWidth: 2, borderColor: this.state.confirmEmail ? '#1589d8' : '#2E3236', borderRadius: 20, alignItems: 'center', justifyContent: 'center' }}>
              <View style={{ height: 10, width: 10, backgroundColor: this.state.confirmEmail ? '#1589d8' : '#fff', borderRadius: 20, alignItems: 'center' }}>
              </View>
            </View>
          </TouchableOpacity>
          <View style={{
            marginTop: 24,
            backgroundColor: '#fff',
            flexDirection: 'row',
            justifyContent: 'center',
            alignItems: 'center',
          }}>
            <TouchableOpacity
              onPress={() => {
                if (
                  this.state.step === STEP.FORGOTPW ||
                  (this.state.step === STEP.GET_OTP &&
                    this.state.type === STEP.LOGIN)
                ) {
                  this.setState({
                    step: STEP.LOGIN,
                  });
                } else if (
                  this.state.step === STEP.GET_OTP
                ) {
                  this.setState({
                    step: STEP.LOGIN,
                  });
                } else if (
                  this.state.step === STEP.GET_OTP &&
                  this.state.type === STEP.FORGOTPW
                ) {
                  this.setState({
                    step: STEP.FORGOTPW,
                  });
                }
              }}
              style={{
                alignItems: 'center',
                paddingVertical: 12,
                backgroundColor: '#656C75',
                borderRadius: 12,
                width: (Define.constants.widthScreen - 32) / 3.5,
                marginRight: 12,
              }}>
              <Text allowFontScaling={false}
                style={{
                  fontFamily: Define.constants.fontBold500,
                  color: '#fff',
                  fontSize: 16,
                }}>
                Quay lại
              </Text>
            </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              if (!this.state.userNameValidate && this.userName.length) {
                NotifyUtil.pushAlertTopNotify({
                  content: 'Vui lòng điền đúng tên đăng nhập',
                  type: 'warning',
                  timeClose: 3000,
                });
              } else if (!this.userName.length) {
                NotifyUtil.pushAlertTopNotify({
                  content: 'Vui lòng điền tên đăng nhập',
                  type: 'warning',
                  timeClose: 3000,
                });
              } else {
                this.sendOTP(3);
              }
            }}
            style={{
              alignItems: 'center',
              paddingVertical: 12,
              backgroundColor: '#007CFE',
              flex:1,
              borderRadius: 12,
            }}>
            <Text allowFontScaling={false}
              style={{
                fontFamily: Define.constants.fontBold500,
                color: '#fff',
                fontSize: 16,
              }}>
              Tiếp tục
            </Text>
          </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  }
  renderOTP() {
    return (
      <View
      keyboardShouldPersistTaps='handle'
      style={{
        backgroundColor: '#FFFFFF',
        paddingHorizontal: 16,
        borderRadius: 16,
        zIndex: 5,
        paddingVertical: 16,
        width: Define.constants.widthScreen - 32,
        }}>
        <View
          style={{
            backgroundColor: '#fff',
            // justifyContent:'center',
            // alignItems:'center',
          }}>
          <Text allowFontScaling={false}
            style={{
              color: '#143250',
              fontSize: 20,
              fontFamily: Define.constants.fontBold600,
              textAlign: 'center',
              marginBottom: 16,
            }}>
            Nhập OTP
          </Text>
          <Text allowFontScaling={false}
            style={{
              color: '#143250',
              fontSize: 17,
              fontFamily: Define.constants.fontBold600,
              textAlign: 'left',
              marginBottom: 8,
            }}>
            Mã xác thực
          </Text>
          <Text allowFontScaling={false}
            style={{
              color: '#514844',
              fontSize: 17,
              fontFamily: Define.constants.fontBold400,
            }}>
            Nhập mã xác thực vừa gửi đến số{' '}
            <Text allowFontScaling={false}
              style={{
                color: '#514844',
                fontSize: 17,
                fontFamily: Define.constants.fontBold500,
              }}>
              {this.state.phone}
            </Text>
          </Text>
        </View>
        <View
          style={{
            marginTop: 16,
            backgroundColor: '#fff',
          }}>
          <View style={{}}>
            <OTPInputView
              style={{height: 56}}
              autoFocusOnLoad
              onCodeFilled={code => {
                this.setState(
                  {
                    code,
                    step: STEP.RESET_PASS,
                  });
              }}
              codeInputFieldStyle={{
                width: 56,
                height: 56,
                borderRadius: 8,
                color: '#012548',
                backgroundColor: '#fff',
                fontSize: 22,
                fontFamily: Define.constants.fontBold500,
              }}
              codeInputHighlightStyle={{
                backgroundColor: '#fff',
              }}
              placeholderTextColor={'#012548'}
              pinCount={4}
            />
          </View>
        </View>
        <View
          style={{
            backgroundColor: '#fff',
            marginTop: 48,
          }}>
          <Text allowFontScaling={false}
            style={{
              color: '#012548',
              fontFamily: Define.constants.fontBold600,
              fontSize: 17,
              marginBottom: 6,
              fontStyle:'italic'
            }}>
            Bạn cần mã xác thực mới?
          </Text>
          {this.state.timeToResend - Date.now() < 0 ? (
            <Text allowFontScaling={false}
              style={{
                color: '#1589D8',
                fontSize: 16,
                fontFamily: Define.constants.fontBold400,
                fontStyle:'italic'
              }}
              onPress={() => {
                popupActions.setRenderContentAndShow(DefaultPopup, {
                  title: 'Gửi lại mã xác thực',
                  description: `Hãy kiểm tra lại số di động bạn khai báo và chắc chắn rằng điện thoại của bạn vẫn đang nhận được tin nhắn.`,
                  buttonTitle2: 'Bỏ qua',
                  onPress2: () => {
                    popupActions.popPopup();
                  },
                  buttonTitle: 'Gửi lại',
                  onPress: () => {
                    this.setState({
                      sent: false,
                    }, () => {
                      this.sendOTP(this.state.type);
                    })
                    popupActions.popPopup();
                  },
                });
              }}>
              Gửi mã mới
            </Text>
          ) : (
            <Text allowFontScaling={false}
              style={{
                color: '#7f8182',
                fontFamily: Define.constants.fontBold400,
                fontSize: 16,
                fontStyle:'italic'
              }}>
              Gửi lại OTP sau{' '}
              <Text allowFontScaling={false}
                style={{
                  color: '#007CFE',
                  fontFamily: Define.constants.fontBold600,
                  fontSize: 18,
                  fontStyle:'italic'
                }}>
                {Math.round((this.state.timeToResend - Date.now()) / 1000)}{' '}
              </Text>
              giây
            </Text>
          )}
        </View>
      </View>
    );
  }

  sendOTP = type => {
    const {dispatch} = this.props;
    if (!this.state.sent) {
      dispatch(
        UserActions_MiddleWare.sendOTP({
          username: this.userName,
          otpMethod: this.state.confirmEmail ? 'email' : 'sms',
          isChangePassword: type === STEP.FORGOTPW,
        }),
      )
        .then(res => {
          let token = res.res.data?.token
          let phone = res.res.userInf?.phone
          if (phone) {
            this.setState({
              phone: phone
            });
          }
          if (token) {
            this.timeResend = res.res.data.timeResend || ms('3m');
            this.startIntervalResend();
            this.setState({
              token,
              step: STEP.GET_OTP,
              type,
              sent: true,
            });
          }
        })
    } else if (
      (this.state.sent && this.state.timeToResend !== 0)
    ) {
      this.setState({
        step: STEP.GET_OTP,
      });
    }
  };

  resetPassword = () => {
    const { dispatch, navigation, route } = this.props;
    let obj = {
      username: this.userName,
      code: this.state.code,
      token: this.state.token,
      newPassword: this.newPassword,
      rePassword: this.confirmPassword
    };
    dispatch(UserActions_MiddleWare.resetPassword(obj)).then(res => {
      if (this.state.step === STEP.RESET_PASS) {
        this.setState({ step: STEP.LOGIN }, () => {
          setTimeout(() => {
            this.phoneRegister && this.phoneRegister.focus()
          }, 100)
        });
      }
    });
  };

  handleNavigate = () => {
    const { dispatch, navigation, route, navigator } = this.props;
    navigation.reset({
      index: 0,
      routes: [{ name: 'MainContainer' }],
    })
  }


  async login() {
    const {dispatch, navigation, route} = this.props;
    dispatch(
      UserActions_MiddleWare.login({
        username: this.userName,
        password: this.password,
      }),
    ).then(async res => {
      let memberInfo = res.res.data;
      if (memberInfo._id) {

        SocketOrderManager.destroy();
        SocketOrderManager.reCheck();

        this.handleNavigate()
        NotifyUtil.pushAlertTopNotify({
          content: 'Đăng nhập thành công.',
          type: 'success',
          timeClose: 3000,
        });

        const rnBiometrics = new ReactNativeBiometrics();
        const { keysExist } = await rnBiometrics.biometricKeysExist();
        if (!keysExist) {
          popupActions.setRenderContentAndShow(DefaultPopup, {
            title: 'Đăng nhập xác thực sinh trắc học',
            description: 'Bạn có muốn bật đăng nhập xác thực sinh trắc học (bằng vân tay hoặc khuôn mặt) để đăng nhập nhanh hơn trong những lần sau không?',
            buttonTitle2: 'Không',
            onPress2: () => {
              popupActions.popPopup();
            },
            buttonTitle: 'Bật',
            onPress: async () => {
              await rnBiometrics.createKeys();
              popupActions.popPopup();
              NotifyUtil.pushAlertTopNotify({
                content: 'Bạn đã bật đăng nhập xác thực sinh trắc học thành công.',
                type: 'success',
                timeClose: 3000,
              });
            },
          });
        }
      }
    });
  }

  async componentDidMount() {
    super.componentDidMount();
    try {
      const rnBiometrics = new ReactNativeBiometrics();
      await rnBiometrics.deleteKeys();
    } catch (e) { }
  }

  keyboardWillShow = (e) => {
    const heightKeyboard = e.endCoordinates.height;
    if (Platform.OS === 'ios') {
      this.setState({
        bottom: 100,
        keyboardShow: true
      });
    } else {
      this.setState({
        keyboardShow: true
      })
    }
  }

  keyboardWillHide = (e) => {
    if (Platform.OS === 'ios') {
      // if (this.scrollViewRef) {
      //   this.scrollViewRef.scrollTo({ x: 0, y: 0, animated: false })
      // }
      this.setState({
        bottom: 0,
        keyboardShow: false
      });
    } else {
      this.setState({
        keyboardShow: false
      })
    }
  }


  UNSAFE_componentWillMount() {
    super.UNSAFE_componentWillMount();

    if (Platform.OS === 'android') {
      this.keyboardDidShowSubscription = Keyboard.addListener('keyboardDidShow', this.keyboardWillShow)
      this.keyboardDidHideSubscription = Keyboard.addListener('keyboardDidHide', this.keyboardWillHide)
    } else {
      this.keyboardDidShowSubscription = Keyboard.addListener('keyboardWillShow', this.keyboardWillShow)
      this.keyboardDidHideSubscription = Keyboard.addListener('keyboardWillHide', this.keyboardWillHide)
    }
  }

  componentDidUpdate() {
    if (Platform.OS === 'ios') {
      LayoutAnimation.easeInEaseOut();
    } else {
      // LayoutAnimation.configureNext(
      //   LayoutAnimation.create(
      //     200,
      //     LayoutAnimation.Types.linear,
      //     LayoutAnimation.Properties.opacity,
      //   ),
      // );
    }
  }
  componentWillUnmount() {
    super.componentWillUnmount();
  }
}

/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  // console.log(state)
  return {
    navigator: state.Navigator,
    user: state.User,
    tips: state.Tips,
    appState: state.AppState,
    appSetting: state.AppSetting,
  };
}

export default connect(selectActions, undefined, undefined, {withRef: true})(
  LoginAccountScreen,
);
