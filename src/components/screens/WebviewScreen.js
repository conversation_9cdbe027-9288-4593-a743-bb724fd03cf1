
var _ = require('lodash')

//LIB
import React  from 'react';
import {
  View,
  ScrollView,
  RefreshControl,
  ActivityIndicator,
  Platform,
  StatusBar,
  SafeAreaView,
  TouchableOpacity,
  Text,
  BackHandler
} from 'react-native';
import Pdf from 'react-native-pdf';

var {Actions} = require('react-native-router-flux');
import { connect } from 'react-redux';
import { WebView } from 'react-native-webview';
import Share from 'react-native-share';
//action

//components
var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
var Util = require('../../Util/Util');
var Include = require('../../Include');
var NotifyUtil = require('../../Util/notify');

var {popupActions} = require('../popups/PopupManager');
var {globalVariableManager}= require('../modules/GlobalVariableManager');
import { getStatusBarHeight } from 'react-native-status-bar-height';
import Clipboard from '@react-native-community/clipboard';

var ButtonWrap = require('../elements/ButtonWrap');

//screens
import Screen from './Screen'

// popups
import DefaultPopup from '../popups/DefaultPopup';
import HeyUIcon from '../elements/HeyUIcon/HeyUIcon';
import PenguinLinearIcon from '../elements/PenguinLinearIcon/PenguinLinearIcon';
import PenguinBoldIcon from '../elements/PenguinBoldIcon/PenguinBoldIcon';
// actions

//variable

// var styles = StyleSheet.create({
//
// })

//

class WebviewScreen extends Screen{
  static componentName = 'WebviewScreen'
  static sceneConfig ={
    ...Screen.sceneConfig,
    hideNavBar: false,
  }
  // static defaultProps = {}
  // static propTypes = {}
  constructor(props){
    super(props);
    this.state = _.merge(this.state,
    {
      isLoadDone: false,
      canGoBack: false,
      canGoForward: false,
      currentUrl: ''
    })
  }
  renderNavBar() {
    var {navigation} = this.props;
    let title = `${Define.constants.displayName}`;
    const statusBarHeight =
      Platform.OS === 'ios' ? getStatusBarHeight(true) : 8;

    return (
      <View
        style={{
          backgroundColor: '#007CFE',
        }}>
        <SafeAreaView
          style={{
            flex: 0,
            backgroundColor: '#007CFE',
            width: '100%',
            height: StatusBar.currentHeight,
          }}
        />
        <View
          style={{
            backgroundColor: '#007CFE',
            flexDirection: 'row',
            width: '100%',
            height: Platform.OS === 'ios' ? 44 : 54,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <TouchableOpacity
            onPress={() => {
                navigation.goBack();
            }}
            style={{
              position: 'absolute',
              left: 16,
              top: 0,
              bottom: 0,
              justifyContent: 'center',
              zIndex: 2,
              paddingRight: 16,
            }}>
            <PenguinLinearIcon name={'arrow-left'} color={'#fff'} size={24} />
          </TouchableOpacity>
          <View>
            <Include.Text
              numberOfLines={1}
              style={{
                color: '#fff',
                fontSize: 18,
                fontFamily: Define.constants.fontBold500,
              }}>
              {title}
            </Include.Text>
          </View>
          {/* <TouchableOpacity
            onPress={() => {
              this.webViewRef && this.webViewRef.reload();
            }}
            style={{
              position: 'absolute',
              right: 16,
              top: 0,
              bottom: 0,
              justifyContent: 'center',
              zIndex: 2,
              paddingLeft: 16,
            }}>
            <PenguinLinearIcon name={'refresh'} color={'#fff'} size={24} />
          </TouchableOpacity> */}
        </View>
      </View>
    );
  }
  onRefresh(){
    super.onRefresh();
    var {dispatch} = this.props;
  }

  onGetMore(){
    super.onGetMore();
    var {dispatch} = this.props;
  }

  handleNavigationChange = (navState) => {
    this.setState({
      canGoForward: navState.canGoForward,
      currentUrl: navState.url,
    });
  };

  renderScreenContent(){
    var {dispatch} = this.props;
    var content = null;
    let lowerSource = this.props.route.params.source && this.props.route.params.source.toLowerCase()
    if (_.includes(lowerSource, '.pdf') && Platform.OS === 'android') {
      content = (
        <View
          style={{ flex: 1 }}>
          {this.renderNavBar()}
          <Pdf
            trustAllCerts={Platform.OS == 'android' ? false : true}
            source={{ uri: this.props.route.params.source }}
            onLoadComplete={(numberOfPages, filePath) => {
              this.setState({
                isLoadDone: true
              })
            }}
            fitPolicy={0}
            style={{ flex: 1, width: Define.constants.widthScreen, height: Define.constants.heightScreen }} />
        </View>
      )
    } else {
      content = (
        <View
          style={{ flex: 1 }}>
          {this.renderNavBar()}
          {this.props.route.params.body && this.props.route.params.method ?
            <WebView
              ref={ref => this.webViewRef = ref}
              javaScriptEnabled={true}
              source={{
                uri: this.props.route.params.source,
                method: this.props.route.params.method,
                body: this.props.route.params.body
              }}
              // useWebKit={false}
              onLoadEnd={() => {
                this.setState({
                  isLoadDone: true
                })
              }}
              onNavigationStateChange={this.handleNavigationChange}
              onLoadProgress={event => {
                this.setState({canGoback: event.nativeEvent.canGoBack});
              }}
            />
            :
            this.props.route.params.youtube ?
              <View style={{ flex: 1, marginTop: 10 }}>
                <Include.Text style={{ textAlign: 'center', fontSize: 16, fontWeight: '500', color: '#7CADBA', marginBottom: 10 }}>{this.props.route.params.title}</Include.Text>
                <WebView
                  mediaPlaybackRequiresUserAction={((Platform.OS !== 'android') || (Platform.Version >= 17)) ? false : undefined}
                  userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36"
                  javaScriptEnabled={true}
                  source={{ html: `<iframe width="100%" height="${Define.constants.heightScreen}" src="${this.props.route.params.youtube}?&autoplay=1" title="HeyU HDSD" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>` }}
                  onLoadEnd={() => {
                    this.setState({
                      isLoadDone: true
                    })
                  }}
                  ref={ref => this.webViewRef = ref}
                  onNavigationStateChange={this.handleNavigationChange}
                  onLoadProgress={event => {
                    this.setState({canGoback: event.nativeEvent.canGoBack});
                  }}
                />
              </View>
              :
              <WebView
                javaScriptEnabled={true}
                ref={ref => this.webViewRef = ref}
                source={{ uri: this.props.route.params.source }}
                onLoadEnd={() => {
                  this.setState({
                    isLoadDone: true
                  })
                }}
                onNavigationStateChange={this.handleNavigationChange}
                onLoadProgress={event => {
                  this.setState({canGoBack: event.nativeEvent.canGoBack});
                }}
              />
          }
          {!this.state.isLoadDone ?
            <View
              style={{ position: 'absolute', top: 0, left: 0, bottom: 0, right: 0, alignItems: 'center', justifyContent: 'center', pointerEvents: 'none' }}>
              <ActivityIndicator size={'large'} />
              <Include.Text style={{ backgroundColor: 'transparent' }}>Loading...</Include.Text>
            </View>
            : null}
          {this.props.route.params.navbarButton ?
            <View style={{
              backgroundColor: '#fff',
              padding: 16,
              flexDirection: 'row',
              justifyContent: 'space-between',
              shadowColor: Platform.OS === 'ios' ? '#929394' : 'black',
              shadowOffset: { height: 2, width: 0 },
              shadowOpacity: 1,
              shadowRadius: 2,
              elevation: 2
            }}>
              <TouchableOpacity
                onPress={() => {
                  if (this.state.canGoBack) {
                    this.webViewRef && this.webViewRef.goBack();
                  }
                }}
                style={{
                  justifyContent: 'center',
                  zIndex: 2,
                  paddingHorizontal: 6
                }}>
                <PenguinLinearIcon name={'arrow-left-1'} color={this.state.canGoBack ? '#007CFE' : '#808080'} size={24} />
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  if (this.state.canGoForward) {
                    this.webViewRef && this.webViewRef.goForward();
                  }
                }}
                style={{
                  justifyContent: 'center',
                  zIndex: 2,
                  paddingHorizontal: 6
                }}>
                <PenguinLinearIcon name={'arrow-right-1'} color={this.state.canGoForward ? '#007CFE' : '#808080'} size={24} />
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  this.webViewRef && this.webViewRef.reload();
                }}
                style={{
                  justifyContent: 'center',
                  zIndex: 2,
                  paddingHorizontal: 6
                }}>
                <PenguinLinearIcon name={'refresh'} color={'#007CFE'} size={24} />
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  this.shareRouter(this.props.route.params)
                }}
                style={{
                  top: 0,
                  justifyContent: 'center',
                  zIndex: 2,
                  paddingHorizontal: 6
                }}>
                <PenguinLinearIcon name={'export'} color={'#007CFE'} size={24} />
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  this.copyRouter(this.props.route.params)
                }}
                style={{
                  justifyContent: 'center',
                  zIndex: 2,
                  paddingHorizontal: 6
                }}>
                <PenguinLinearIcon name={'copy'} color={'#007CFE'} size={24} />
              </TouchableOpacity>
            </View> : null}
          <SafeAreaView style={{ flex: 0, backgroundColor: '#fff' }} />
        </View>
      )
    }
    return content;
  }
  copyRouter(data) {
    let link = ''
    if (data.youtube){
      link = data.youtube
    } else {
      link = this.state.currentUrl
    }
    Clipboard.setString(link)
    NotifyUtil.pushAlertTopNotify({
      type: 'success',
      content: 'Sao chép link thành công',
      timeClose: 2000
    })
  }

  shareRouter = async (data) => {
    let link = ''
    if (data.youtube){
      link = data.youtube
    } else {
      link = this.state.currentUrl
    }
    if (_.isEqual(link, '')) {
      return;
    }
    const shareOptions = {
      message: link,
    };

    try {
      const ShareResponse = await Share.open(shareOptions);
    } catch (err) {
      console.log(err);
    }
  };

  componentDidMount(){
    super.componentDidMount();
    this.props.navigation.setOptions({ gestureEnabled: false });
    if(Platform.OS === 'android') {
      this.back = BackHandler.addEventListener('hardwareBackPress', this.backHandler);
    }
  }
  componentWillUnmount() {
    if (Platform.OS === 'android') {
      if (this.back) {
        this.back.remove()
      }
    }
  }

  backHandler = () => {
    if (this.state.canGoBack){
      this.webViewRef && this.webViewRef?.goBack()
      return true
    }
    return false
  }
  UNSAFE_componentWillReceiveProps(nextProps) {
    if(nextProps.source !== this.props.route.params.source) {
      this.setState({
        isLoadDone: false
      });
    }
  }
}


/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  return {
    navigator: state.Navigator,
  }
}

export default connect(selectActions, undefined, undefined, {withRef: true})(WebviewScreen);
