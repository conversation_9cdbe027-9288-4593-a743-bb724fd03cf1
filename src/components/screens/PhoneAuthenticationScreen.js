var _ = require('lodash')

//LIB
import React from 'react';
import {
  View,
  ScrollView,
  RefreshControl,
  Platform,
  StatusBar,
  Image,
  TextInput,
  Keyboard,
  LayoutAnimation,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Linking,
  BackHandler,
  AppState,
  Text,
} from 'react-native';
import OTPInputView from '@twotalltotems/react-native-otp-input'

var { Actions } = require('react-native-router-flux');
import { connect } from 'react-redux';
//action

//components
var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
var Util = require('../../Util/Util');
var Include = require('../../Include');
var NotifyUtil = require('../../Util/notify')
var locationManager = require('../modules/LocationManager');
import LinearGradient from 'react-native-linear-gradient';
import LottieView from 'lottie-react-native';
import {Fontisto as IconX} from '@react-native-vector-icons/fontisto';
import validator from 'validator';
import { TransitionPresets } from 'react-navigation-stack';

var { popupActions } = require('../popups/PopupManager');
var { globalVariableManager } = require('../modules/GlobalVariableManager');
const ms = require('ms')

var ButtonWrap = require('../elements/ButtonWrap');
import HeyUIcon from '../elements/HeyUIcon/HeyUIcon';

//screens
import Screen from './Screen'

// popups
import DefaultPopup from '../popups/DefaultPopup';
// actions
import UserActions_MiddleWare from '../../actions/UserActions_MiddleWare'
import AppStateActions_MiddleWare from '../../actions/AppStateActions_MiddleWare'
//variable
import NetInfo from "@react-native-community/netinfo";
import { SafeAreaView } from 'react-native-safe-area-context';
// var styles = StyleSheet.create({
//
// })

//
import {startSmsHandling} from '@eabdullazyanov/react-native-sms-user-consent';


const MODE = {
  CONFIRM_MODE: 0,
  VERRIFY_MODE: 1,
  DONE_MODE: 2
}


class PhoneAuthenticationScreen extends Screen {
  static componentName = 'PhoneAuthenticationScreen'
  static sceneConfig = {
    ...Screen.sceneConfig,
    hideNavBar: true,
    gesturesEnabled: false,
    ...TransitionPresets.ScaleFromCenterAndroid
  }
  // static defaultProps = {}
  // static propTypes = {}
  constructor(props) {
    super(props);
    this.state = _.merge(this.state,
      {
        bottom: 0,
        mode: MODE.CONFIRM_MODE,
        phone: this.props.phone || '',
        token: "",
        timeToResend: 0,
        code: "",
        phoneValidate: false
      })
    this.oldPhone = null
    this.intervalResend = null;
    this.timeResend = ms('3m')
    this.keyboardWillShow = this.keyboardWillShow.bind(this);
    this.keyboardWillHide = this.keyboardWillHide.bind(this);
    this.startIntervalResend = this.startIntervalResend.bind(this);
    this.renderConfirmMode = this.renderConfirmMode.bind(this);
    this.renderVerifyMode = this.renderVerifyMode.bind(this);
    this.onPressConfirm = this.onPressConfirm.bind(this);
  }

  onRefresh() {
    super.onRefresh();
    var { dispatch } = this.props;
  }

  onGetMore() {
    super.onGetMore();
    var { dispatch } = this.props;
  }

  keyboardWillShow(e) {
    const heightKeyboard = e.endCoordinates.height;
    if (Platform.OS === 'ios') {
      this.setState({
        bottom: heightKeyboard
      });
    }
    this.setState({
      keyboardShow: true
    });
  }

  keyboardWillHide(e) {
    this.setState({
      bottom: 0
    });
  }

  renderVerifyMode() {
    const {dispatch} = this.props;
    if(this.state.mode !== MODE.VERRIFY_MODE) {
      return null
    }
    return (
      <View style={{ flex: 1, marginTop: 10, backgroundColor: '#fff' }}>
        <View style={{backgroundColor: '#fff', height: 40, width: Define.constants.widthScreen, flexDirection: 'row', alignItems: 'center'}}>
          <TouchableOpacity
            onPress={() => {
              if (this.state.mode === MODE.CONFIRM_MODE) {
                Actions.pop();
              } else {
                if(this.state.timeToResend === 0) {
                  clearInterval(this.intervalResend);
                }
                this.setState({mode: MODE.CONFIRM_MODE});
                if(this.phone) {
                  this.phone.focus();
                }
              }
            }}
            style={{backgroundColor: '#fff', height: 40, width: 60, justifyContent: 'center', paddingLeft: 20}}
          >
            <HeyUIcon name={'fi-sr-arrow-left'} size={24} color={'#012548'}/>
          </TouchableOpacity>
          <TouchableOpacity
            style={{backgroundColor: '#fff', height: 44, justifyContent: 'center', alignItems: 'center', borderRadius: 6, position: 'absolute', right: 16}}
            onPress={this.onPressConfirm}
            disabled={this.state.code ? false : true}
          >
            <Text allowFontScaling={false}
              style={{
                color: this.state.code ? '#161616' : '#afafaf',
                backgroundColor: 'transparent',
                fontSize: 18,
                fontFamily: Define.constants.fontBold500
              }}>
                Xác thực
            </Text>
          </TouchableOpacity>
        </View>
        <ScrollView style={{ flex: 1 }}>
          <View style={{paddingHorizontal: 16, marginTop: 19, backgroundColor: '#fff'}}>
            <Text allowFontScaling={false}
              style={{
                color: '#514844',
                fontSize: 22,
                fontFamily: Define.constants.fontBold400
              }}>
                HeyU vừa gửi tin
                nhắn chứa mã xác
                thực tới số điện
                thoại <Text allowFontScaling={false}
                        style={{
                          color: '#514844',
                          fontSize: 22,
                          fontFamily: Define.constants.fontBold500
                        }}>
                          {this.state.phone}
                      </Text>
            </Text>
          </View>

          <View style={{paddingHorizontal: 16, marginTop: 15, backgroundColor: '#fff'}}>
            <Text allowFontScaling={false} style={{color: '#7f8182', fontFamily: Define.constants.fontBold400, fontSize: 18}}>Nhập 4 số xác thực trong tin nhắn</Text>
            <View style={{flex: 1, marginVertical: 14}}>
              <OTPInputView
                style={{flex: 1, height: 60}}
                autoFocusOnLoad
                onCodeFilled = {(code => {
                  this.setState({
                    code
                  }, () => {
                    this.onPressConfirm();
                  })
                })}
                codeInputFieldStyle = {{
                  width: 60,
                  height: 60,
                  borderRadius: 16,
                  color: '#012548',
                  backgroundColor: "#fff",
                  fontSize: 22,
                  fontFamily: Define.constants.fontBold500
                }}
                codeInputHighlightStyle = {{
                  backgroundColor: "#fff",
                }}
                placeholderTextColor = {'#1e1e1e'}
                pinCount={4} />
            </View>
          </View>
        </ScrollView>
        <View style={{position: 'absolute', bottom: this.state.bottom, width: Define.constants.widthScreen, backgroundColor: '#fff'}}>
          <View style={{backgroundColor: '#fff', paddingHorizontal: 16, flexDirection: 'row', marginBottom: 10, flex: 1}}>
            <Text allowFontScaling={false} style={{color: '#7f8182', fontFamily: Define.constants.fontBold400, fontSize: 18}}>
              Bạn cần mã xác thực mới?
            </Text>
            {this.state.timeToResend - Date.now() < 0 ?
              <Text allowFontScaling={false}
                style={{
                  color: '#1589D8',
                  fontSize: 18,
                  fontFamily: Define.constants.fontBold400,
                  position: 'absolute',
                  right: 24
                }}
                onPress={() => {
                  popupActions.setRenderContentAndShow(
                    DefaultPopup,
                    {
                      title: 'Gửi lại mã xác thực',
                      description: `Hãy kiểm tra lại số di động bạn khai báo và chắc chắn rằng điện thoại của bạn vẫn đang nhận được tin nhắn.`,
                      buttonTitle2: 'BỎ QUA',
                      onPress2: () => { popupActions.popPopup() },
                      buttonTitle: 'GỬI LẠI',
                      onPress: () => {
                        this.requestPhoneCode()
                        popupActions.popPopup()
                      }
                    }
                  );
                }}
              >
                Gửi mã mới
              </Text>
              :
              <Text allowFontScaling={false} style={{color: '#7f8182', fontFamily: Define.constants.fontBold400, fontSize: 18, position: 'absolute', right: 16}}>
                Chờ {Math.round((this.state.timeToResend - Date.now())/1000)} giây
              </Text>
            }
          </View>
        </View>
      </View>
    )
  }

  onPressConfirm() {
    const {dispatch} = this.props;

    const code = this.state.code;
    dispatch(UserActions_MiddleWare.checkPhoneCode({phone:this.state.phone, code, token: this.state.token}))
      .then((result) => {
        if(this.props.setPhoneCode) {
          this.props.setPhoneCode(code);
        }
        if(this.props.handlePhoneAuthenSucess) {
          this.props.handlePhoneAuthenSucess();
        }

        this.setState({
          mode: MODE.DONE_MODE
        }, () => {
          setTimeout(() => {
            if (this.props.handleLoginWithoutPassword) {
              this.props.handleLoginWithoutPassword();
            } else {
              Actions.pop();
            }
          }, 2000)
        })
      })
  }

  requestPhoneCode() {
    const {dispatch} = this.props;
    let phone = this.state.phone

    if(phone.length === 9 && !phone.startsWith("0")) {
      phone = "0" + phone
    }

    if(phone.length === 11 && phone.startsWith("84")) {
      phone = phone.replace("84", "0")
    }

    if(phone.length === 12 && phone.startsWith("+84")) {
      phone = phone.replace("+84", "0")
    }
    dispatch(UserActions_MiddleWare.requestPhoneCode({phone}))
      .then((result) => {
        if(result.res.data && result.res.data.token) {
          this.oldPhone = phone
          this.timeResend = result.res.data.timeResend || ms('3m')
          this.startIntervalResend();
          this.setState({
            token: result.res.data.token,
            mode: result.res.data.code ? MODE.CONFIRM_MODE : MODE.VERRIFY_MODE,
            code: result.res.data.code || '',
            phone
          }, () => {
            if (result.res.data.code) {
              this.onPressConfirm();
            }
          });
          this.props.setToken(result.res.data.token);
          this.props.setPhone(phone);
        }
      })
      .catch(err => {
        dispatch(AppStateActions_MiddleWare.getConfig())
      })
  }
  checkNetworkLogin = () => {
    NetInfo.fetch().then(state => {
      if(state.isConnected){
        if(this.state.timeToResend === 0 || (this.oldPhone && this.oldPhone!== this.state.phone) || (this.state.timeToResend - Date.now() < 0)) {
          this.requestPhoneCode()
        } else {
          this.props.setPhone(this.state.phone);
          this.setState({
            mode: MODE.VERRIFY_MODE
          })
        }
      } else {
        NotifyUtil.pushAlertTopNotify({
          content: 'Lỗi kết nối mạng, vui lòng kiểm tra lại. Xin cảm ơn',
          type: 'warning',
          timeClose: 3000,
        })
      }
    })
  }
  renderConfirmMode() {
    const {changePhone} = this.props

    if(this.state.mode !== MODE.CONFIRM_MODE) {
      return null
    }
    return (
      <View style={{ flex: 1 , backgroundColor: '#fff', marginTop: 10}}>
        {changePhone ?
          <View style={{backgroundColor: 'transparent'}}>
            <TouchableOpacity
              style={{backgroundColor: 'transparent', padding: 16, flexDirection: 'row', alignItems: 'center'}}
              onPress={() => {Actions.pop()}}
            >
              <HeyUIcon name={'fi-sr-arrow-left'} color={'#012548'} size={24}/>
              <Include.Text style={{fontSize: 16, color: '#161616', marginLeft: 8}}>Trở lại</Include.Text>
            </TouchableOpacity>
          </View>
        : null}
        {!changePhone ?
          <View style={{backgroundColor: '#fff', paddingHorizontal: 16}}>
            <Include.Text style={{ fontSize: 28, marginVertical: 16, color: '#012548', fontFamily: Define.constants.fontBold500}}>Đăng nhập HeyU</Include.Text>
          </View> : null
        }

        <View style={{backgroundColor: '#fff', paddingHorizontal: 16}}>
          <Text allowFontScaling={false}
            style={{
              color: '#7F8182',
              fontSize: 18,
              fontFamily: Define.constants.fontBold400,
              marginTop: 16,
              marginBottom: 24
            }}>
              Bạn vui lòng nhập số điện thoại di động
          </Text>
          <View
           style={{
            flexDirection: 'row',
            backgroundColor:this.state.phoneValidate ? '#E6F8E9' : '#fff',
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: 16,
            paddingHorizontal: 18,
            marginBottom: 10,
            borderWidth: 1,
            borderColor: this.state.phoneValidate ? '#fff' : '#b3b5b6' ,
          }}>
            <TouchableOpacity style={{height: 56, alignItems: 'center', flexDirection: 'row', justifyContent: 'center'}}>
              <Image source={Define.assets.Images.flag} style={{width: 24, height: 15, marginHorizontal: 5, borderRadius: 2}} resizeMode={'stretch'}/>
              <Text allowFontScaling={false} style={{color:this.state.phoneValidate ? '#008000' : '#161616', fontSize: 18, fontFamily: Define.constants.fontBold500}}>+84</Text>
            </TouchableOpacity>
            <TextInput
              ref={ref => this.phone = ref}
              placeholder= {'987654321'}
              textContentType={'telephoneNumber'}
              placeholderTextColor={'#708090'}
              underlineColorAndroid ='transparent'
              keyboardType={'numeric'}
              value={this.state.phone}
              autoFocus={true}
              style={{ flex: 1, fontSize: 18, fontFamily: Define.constants.fontBold500, height: 56, paddingHorizontal: 16, color: this.state.phoneValidate ? '#008000' : '#161616', justifyContent: 'center'}}
              onChangeText={(text) => {
                text = text.replace(/[^+0-9]/g, '')
                this.setState({ phone: text });
                if(text.length >= 9) {
                  this.setState({phoneValidate: true})
                } else {
                  this.setState({phoneValidate: false})
                }
              }}
              maxLength={16}
            />
            {
              this.state.phone ?
                this.state.phoneValidate ?
                  <View
                    style={{width: 20, height: 20, borderRadius: 10, backgroundColor: '#44AB49', justifyContent: 'center', alignItems: 'center'}}
                  >
                    <IconX name={'check'} size={8} color={'#fff'}/>
                  </View>
                  :
                  <TouchableOpacity
                    style={{width: 20, height: 20, borderRadius: 10, backgroundColor: '#d2d2d2', justifyContent: 'center', alignItems: 'center'}}
                    onPress={() => {this.setState({ phone: '' })}}
                  >
                    <IconX name={'close-a'} size={8} color={'#708090'}/>
                  </TouchableOpacity>
              :
                null
            }
          </View>
          <Text allowFontScaling={false}
            style={{
              color: '#7F8182',
              fontSize: 18,
              fontFamily: Define.constants.fontBold400,
              marginTop: 16,
              marginBottom: 24
            }}>
              Sau đó ấn nút "Gửi mã xác thực" bên dưới
          </Text>
        </View>

        <View style={{position: 'absolute', bottom: this.state.bottom, width: Define.constants.widthScreen, backgroundColor: '#fff', paddingHorizontal: 16, paddingBottom: 10}}>
          <View style={{marginBottom: 24}}>
            <TouchableWithoutFeedback
              style={{flexWrap: 'wrap'}}
              onPress={() => {}}
            >
              <Text allowFontScaling={false} style={{color: '#012548', fontSize: 16, fontFamily: Define.constants.fontBold400}}>
                Tôi đồng ý với <Text allowFontScaling={false} style={{color: '#1871B9', fontSize: 16, fontFamily: Define.constants.fontBold400}} onPress={() => {Linking.openURL(Define.constants.urlTerms)}}>{`Điều khoản dịch vụ & quyền riêng tư`}</Text> của HeyU
              </Text>
            </TouchableWithoutFeedback>
          </View>
          <TouchableOpacity
            style={{
              backgroundColor: this.state.phone && this.state.phoneValidate ? '#1589D8' : '#B3B5B6',
              height: 56,
              justifyContent: 'center',
              alignItems: 'center',
              borderRadius: 16
            }}
            onPress={() => {
              this.checkNetworkLogin()
            }}
            disabled={this.state.phone && this.state.phoneValidate ? false : true}
          >
            <Text allowFontScaling={false}
              style={{
                color: this.state.phone && this.state.phoneValidate ? '#fff' : '#708090',
                backgroundColor: 'transparent',
                fontSize: 18,
                fontFamily: Define.constants.fontBold500
                }}>
                  Gửi mã xác thực
            </Text>
          </TouchableOpacity>
        </View>

      </View>
    )
  }

  renderDoneMode() {
    if(this.state.mode !== MODE.DONE_MODE) {
      return null
    }
    return(
      <View
        style={{marginTop: Define.constants.heightScreen/3, alignSelf:'center', width: Define.constants.widthScreen/3, height:  Define.constants.widthScreen/3}}
      >
        <LottieView
          style={{width: Define.constants.widthScreen/3, height:  Define.constants.widthScreen/3}}
          source={require('../../../assets/Animation/animationDone')}
          autoPlay={true}
          resizeMode= {'cover'}
          loop={false}
          // onAnimationFinish={() => {
          //   if (this.props.handleLoginWithoutPassword) {
          //     this.props.handleLoginWithoutPassword();
          //   } else {
          //     Actions.pop();
          //   }
          // }}
        />
      </View>
    )
  }

  renderScreenContent() {
    var { dispatch } = this.props;
    var content = null;

    if (this.state.requireLocation) {
      return this.renderDescriptionRequireLocation();
    }

    if (this.state.regionNotSupport) {
      return this.renderRegionNotSupport();
    }

    content = (
      <SafeAreaView style={{flex: 1, backgroundColor: '#fff', paddingTop: Platform.OS === 'ios' ? 0 : StatusBar.currentHeight}}>
        <View style={{flex: 1}}>
          {this.renderConfirmMode()}
          {this.renderVerifyMode()}
          {this.renderDoneMode()}
        </View>
      </SafeAreaView>
    )
    return content;
  }

  startIntervalResend() {
    clearInterval(this.intervalResend);
    this.setState({
      timeToResend: Date.now() + this.timeResend
    })
    this.intervalResend = setInterval(() => {
      if(this.state.timeToResend > 0) {
        this.setState({
          timeToResend: this.state.timeToResend - 1
        })
      } else {
        if(this.intervalResend) {
          clearInterval(this.intervalResend);
        }
      }
    },1000)
  }

  UNSAFE_componentWillMount() {
    super.UNSAFE_componentWillMount();

    if (Platform.OS === 'android') {
      this.keyboardDidShowSubscription = Keyboard.addListener('keyboardDidShow', this.keyboardWillShow)
      this.keyboardDidHideSubscription = Keyboard.addListener('keyboardDidHide', this.keyboardWillHide)
      this.back = BackHandler.addEventListener('hardwareBackPress', this.backHandler);
    } else {
      this.keyboardDidShowSubscription = Keyboard.addListener('keyboardWillShow', this.keyboardWillShow)
      this.keyboardDidHideSubscription = Keyboard.addListener('keyboardWillHide', this.keyboardWillHide)
    }
  }

  componentDidMount() {
    super.componentDidMount();
    StatusBar.setBarStyle('dark-content');
    this.appStateSubscription = AppState.addEventListener('change', this.handleAppStateChange);

    if(Platform.OS === 'android') {
      this.handleSMSListener = startSmsHandling((data) => {
        if(data && data.sms) {
          const otp = data.sms.slice(data.sms.length-4);
          this.setState({
            code: otp
          }, () => {
            this.onPressConfirm();
          })
        }
      })
    }

    if(validator.isMobilePhone(this.state.phone, ['vi-VN'])) {
      this.setState({phoneValidate: true})
    } else {
      this.setState({phoneValidate: false})
    }

    this.getConfigRegion();
  }

  handleAppStateChange = (currentAppState) => {
    switch (currentAppState) {
      case 'active': {
        this.getConfigRegion(true);

        break;
      }
      case 'background': {
        break;
      }
      case 'inactive': {
        break;
      }
      default:
    }
  }

  requestPermission = () => {
    const { dispatch } = this.props;

    locationManager
      .getCurrentLocation()
      .then((location) => {
        if (this.state.requireLocation) {
          this.setState({ requireLocation: false });
        }

        return dispatch(AppStateActions_MiddleWare.getRegionByLatLng({ location }));
      })
      .then((result) => {
        if (!this.state.configRegion?.listRegion.includes(result.res.data)) {
          this.setState({
            regionNotSupport: true,
            requireLocation: false
          });
        } else {
          if (this.state.regionNotSupport) {
            this.setState({ regionNotSupport: false });
          }
        }
      })
      .catch(err => {
        if (this.state.configRegion?.force) {
          this.setState({
            requireLocation: true,
            regionNotSupport: false
          });
        } else {
          if (this.state.requireLocation) {
            this.setState({ requireLocation: false });
          }
        }
      })
  }

  renderDescriptionRequireLocation = () => {
    const { user } = this.props;

    let title = `Cho phép truy cập vị trí rất quan trọng`
    let message = `Bằng việc "Cho phép truy cập vị trí" sẽ giúp tài xế đến lấy hàng, giao hàng nhanh chóng, chính xác. Hỗ trợ quý khách tốt hơn trong quá trình sử dụng.`
    if (_.get(user, 'memberInfo.member.phone', '') === '0987654321') {
      message = `Bằng việc "Tiếp tục" bạn sẽ cho phép truy cập vị trí để tài xế đến lấy hàng, giao hàng nhanh chóng, chính xác. Hỗ trợ bạn tốt hơn trong quá trình sử dụng.`;
    }

    return (
      <View style={{ flex: 1, margin: 16, justifyContent: 'space-between', paddingVertical: 40 }}>
        <View>
          <Include.Text style={{ fontSize: 24, color: '#514844', textAlign: 'center', fontWeight: '500' }}>{title}</Include.Text>
          <Include.Text style={{ fontSize: 16, color: '#708090', textAlign: 'center', marginTop: 22 }}>{message}</Include.Text>
        </View>
        <View style={{ alignItems: 'center', justifyContent: 'center' }}>
          <LottieView source={require('../../../assets/Animation/animationRequireLocation')} autoPlay style={{ width: '100%', height: Define.constants.widthScreen * 0.5 }} />
        </View>
        <TouchableOpacity
          style={{ backgroundColor: '#0473CD', height: 60, justifyContent: 'center', alignItems: 'center', borderRadius: 6 }}
          onPress={() => {
            Linking.openSettings()
          }}>
          <Include.Text style={{ color: '#fff', fontSize: 18, backgroundColor: 'transparent' }}>Cho phép truy cập vị trí</Include.Text>
        </TouchableOpacity>
      </View>
    )
  }

  renderRegionNotSupport = () => {
    return (
      <SafeAreaView>
        <View style={{ padding: 16, justifyContent: 'center', alignItems: 'center', height: '100%' }}>
          <Image source={{ uri: 'https://media.heyu.asia/uploads/mobiles/2022-09-06-notactive.png' }} style={{ width: Define.constants.widthScreen - 48, height: (Define.constants.widthScreen - 48) * 871 / 1280 }} />
          <Include.Text style={{ fontSize: 18, fontWeight: '500', fontFamily: Define.constants.fontBold500, textAlign: 'center', color: '#012548', marginTop: 32 }}>Xin lỗi, Hiện tại HeyU chưa triển khai ở khu vực của bạn. Bạn vui lòng quay lại sau. Xin cảm ơn.</Include.Text>
        </View>
      </SafeAreaView>
    )
  }

  getConfigRegion = (appStateChange) => {
    this.props.dispatch(AppStateActions_MiddleWare.getConfigRegion())
      .then(result => {
        if (result.res.data?.isOpen) {
          this.setState({ configRegion: result.res.data || [] }, () => {
            if (appStateChange) {
                if (result.res.data?.force) {
                  this.setState({
                    requireLocation: true,
                    regionNotSupport: false
                  });
                } else {
                  if (this.state.requireLocation) {
                    this.setState({ requireLocation: false });
                  }
                }
            } else {
              this.requestPermission();
            }
          });
        } else {
          if (this.state.requireLocation || this.state.regionNotSupport) {
            this.setState({
              requireLocation: false,
              regionNotSupport: false
            })
          }
        }
      })
      .catch(err => {
        if (this.state.requireLocation || this.state.regionNotSupport) {
          this.setState({
            requireLocation: false,
            regionNotSupport: false
          })
        }
      })
  }

  UNSAFE_componentWillUpdate(){
    super.UNSAFE_componentWillUpdate();
    if(Platform.OS === 'ios') {
      LayoutAnimation.easeInEaseOut();
    }
  }

  componentWillUnmount() {
    this.appStateSubscription.remove();

    clearInterval(this.intervalResend);
    if (Platform.OS === 'android') {
      this.handleSMSListener && this.handleSMSListener();
      if (this.back) {
        this.back.remove()
      }
    }
    this.keyboardDidShowSubscription.remove()
    this.keyboardDidHideSubscription.remove()
  }

  backHandler = () => {
      return true;
  }
}


/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  return {
    navigator: state.Navigator,
  }
}

export default connect(selectActions, undefined, undefined, { withRef: true })(PhoneAuthenticationScreen);
