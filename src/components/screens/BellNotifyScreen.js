var _ = require('lodash')

import React from 'react';
import {
  View,
  ScrollView,
  AppState,
  Image,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ImageBackground
} from 'react-native';

import { connect } from 'react-redux';
import LinearGradient from 'react-native-linear-gradient';
import { Icon } from 'native-base';
import { getStatusBarHeight } from 'react-native-status-bar-height';

import RDActions from '../../actions/RDActions';
import Screen from './Screen';
import DefaultPopup from '../popups/DefaultPopup';
import NotificationsActions_MiddleWare from '../../actions/NotificationsActions_MiddleWare';

var { Actions } = require('react-native-router-flux');
var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
var Util = require('../../Util/Util');
var Include = require('../../Include');
var ButtonWrap = require('../elements/ButtonWrap');
var { popupActions } = require('../popups/PopupManager');
var { globalVariableManager } = require('../modules/GlobalVariableManager');
import HeyUIcon from '../elements/HeyUIcon/HeyUIcon';
const PushNotiManager = require('../modules/PushNotiManager');

class BellNotifyScreen extends Screen {
  static componentName = 'BellNotifyScreen'
  static sceneConfig = {
    ...Screen.sceneConfig,
    hideNavBar: true
  }

  constructor(props) {
    super(props)
    this.state = _.merge(this.state, {
      start: 0,
      end: 10,
      notifyArray: []
    })
    this.refresh = this.refresh.bind(this)
    this.processNotifyList = this.processNotifyList.bind(this)
    this.handleAppStateChange = this.handleAppStateChange.bind(this)
    this.isGettingMore = true
  }

  processNotifyList() {
    var { notify, appSetting } = this.props;
    var notiArr = [];
    let notifyList = notify.arrays;
    console.log("ahihi",notify)
    if (notify.arrays.length <= 10) {
      this.setState({ notifyArray: notifyList })
    } else {
      notiArr = notifyList.slice(this.state.start, this.state.end)
      this.setState({
        notifyArray: this.state.notifyArray.concat(notiArr),
        start: this.state.start + 10,
        end: this.state.end + 10
      })
    }
  }

  refresh() {
    this.setState({
      start: 0,
      end: 10,
      notifyArray: []
    }, () => {
      this.processNotifyList()
    })
  }

  onGetMore() {
    super.onGetMore()
    this.processNotifyList()
  }

  handleAppStateChange(newState) {
    let { dispatch, navigation } = this.props
    switch (newState) {
      case 'active':
        if (navigation.isFocused()) {
          this.refresh()
          PushNotiManager.handlePopup();
        }
        break
      case 'inactive':
        break
      default:
        break
    }
  }

  isCloseToBottom({ layoutMeasurement, contentOffset, contentSize }) {
    const paddingToBottom = 30
    return layoutMeasurement.height + contentOffset.y >= contentSize.height - paddingToBottom
  }

  renderNavBar() {
    var { notify, appSetting } = this.props;
    const statusBarHeight = Platform.OS === 'ios' ? getStatusBarHeight(true) : StatusBar.currentHeight;
    return (
      <View
        style={{
          height: Platform.OS === 'ios' ? Define.constants.navBarHeight + statusBarHeight + 8 : Define.constants.navBarHeight,
          width: '100%',
          flexDirection: 'row',
          paddingTop: statusBarHeight,
          alignItems: 'center',
        }}>
        <TouchableOpacity
          onPress={() => {
            Actions.pop();
          }}
          style={{ marginLeft: 16, width: Define.constants.navBarHeight }}
        >
          {/* <HeyUIcon name={'fi-sr-arrow-left'} color={'#012548'} size={24} /> */}
        </TouchableOpacity>
        <View style={{ marginLeft: -22, flex: 1 }} pointerEvents={'none'}>
          <Include.Text numberOfLines={1} style={{ color: '#fff', fontSize: 18, fontFamily: Define.constants.fontBold500, textAlign: 'center' }}>Tin nhắn hệ thống</Include.Text>
        </View>
        <View style={{ width: Define.constants.navBarHeight }} />
      </View>
    )
  }

  createElementArray(notifyArray) {
    const self = this
    var { dispatch } = self.props
    var elementArray = []
    notifyArray.forEach((item, index) => {
      var element = (
        <TouchableOpacity
          style={{
            flexDirection: 'row',
            borderRadius: 4,
            alignItems: 'center',
            borderBottomWidth: index === notifyArray.length - 1 ? 0 : 0.5, borderBottomColor: '#dfe4ea', marginHorizontal: 10
          }}
          key={index}
          onPress={() => {
            dispatch(RDActions['Notify']['removeOnRequest']({ id: item._id }));
            if (item.link) {
              let objNavigator = {}
              if (item.link === 'DetailNotificationScreen') {
                objNavigator = { ...item.extras, createdAt: item.notifiedAt}
              } else {
                objNavigator = { ...item.extras, createdAt: item.notifiedAt, _id: item._id }
              }
              globalVariableManager.navigatorManager.handleNavigator(item.link, objNavigator)
            } else if (item.actionUrl) {
              globalVariableManager.navigatorManager.handleNavigator('WebviewScreen',{
                source: item.actionUrl
              })
            }

            this.refresh();
          }}>
          <View style={{ width: 60, alignItems: 'center', justifyContent: 'center', marginRight: 10 }}>
            <Image style={{ width: 50, height: 50, borderRadius: 25 }} source={{ uri: item.icon }} />
          </View>
          <View style={{ paddingVertical: 10, flex: 1,  }}>
            {item.title ? <Include.Text style={{ fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold500 }}>{item.title}</Include.Text> : null}
            <Include.Text style={{ fontSize: 14, color: '#ffffff' }}>{item.description}</Include.Text>
            <Include.Text numberOfLines={1} style={{ fontSize: 14, color: '#ffffff' }}>{Util.date2String(new Date(item.notifiedAt), 'HHhMM - dd/mm/yyyy')}</Include.Text>
          </View>
        </TouchableOpacity>
      )
      elementArray.push(element)
    })
    return elementArray
  }

  renderScreenContent() {
    var { dispatch, appSetting, notify } = this.props
    const elementArray = this.createElementArray(this.state.notifyArray)
    let notifyList = notify.arrays;
    var content = null
    content = (
      <View style={{ flex: 1 }}>
        <StatusBar
          backgroundColor={Platform.OS === 'android' ? null : '#fff'}
          barStyle={'light-content'}
        />
        <ImageBackground source={Define.assets.Images.bgAI} style={{ flex: 1 }}>
        {this.renderNavBar()}
        {notifyList.length > 0 ?
          <View style={{ backgroundColor: '#DFE6F3', flexDirection: 'row', justifyContent: 'space-between', paddingVertical: 6, paddingHorizontal: 16, alignItems: 'center' }}>
            <Include.Text style={{ fontSize: 14, fontWeight: '400', color: '#1E5D97' }}>Bạn còn {notifyList.length} tin nhắn chưa xem </Include.Text>
            <TouchableOpacity style={{ paddingHorizontal: 12, paddingVertical: 6, backgroundColor: '#1871B9', alignItems: 'center', justifyContent: 'center', borderRadius: 60 }}
              activeOpacity={0.6}
              disabled={notifyList.length > 0 ? false : true}
              onPress={() => {
                popupActions.setRenderContentAndShow(DefaultPopup, {
                  description: 'Bạn có muốn xoá tất cả thông báo',
                  buttonTitle: 'Xoá tất cả',
                  buttonTitle2: 'Để sau',
                  onPress: () => {
                    popupActions.popPopup()
                    dispatch(RDActions['Notify']['removeAllNotify']())
                    this.refresh()
                  },
                  onPress2: () => {
                    popupActions.popPopup()
                  }
                })
              }}>
              <Include.Text style={{ fontSize: 14, color: '#fff', fontFamily: Define.constants.fontBold500 }}>Xoá tất cả</Include.Text>
            </TouchableOpacity>
          </View> : null}
        <Include.ScrollView
          style={[Themes.current.screen.bodyView, this.props.bodyStyle, {  paddingTop: 0 }]}
          refreshing={false}
          onRefresh={this.refresh}
          onScroll={({ nativeEvent }) => {
            if (this.isCloseToBottom(nativeEvent)) {
              this.onGetMore()
            }
          }}
          scrollEventThrottle={400}
          removeClippedSubviews={false}
          showsVerticalScrollIndicator={false}
        >
          {elementArray.length === 0 ?
            <View style={{ alignItems: 'center', justifyContent: 'center', marginTop: Define.constants.heightScreen * 0.2, padding: 16 }}>
              <Image
                source={Define.assets.Images.paperMessage}
                style={{ width: 215, height: 176, resizeMode: 'contain' }}
              />
              <Include.Text style={{ color: '#fff', fontSize: 18, fontFamily: Define.constants.fontBold600, textAlign: 'center', marginBottom: 8, marginTop: 32 }}>
                Bạn chưa có tin nhắn
              </Include.Text>
              {/* <Include.Text style={{ color: '#143250', fontSize: 16, fontFamily: Define.constants.fontBold400, textAlign: 'center' }}>
                Những tin nhắn về đơn hàng và những thông tin liên quan sẽ hiển thị tại đây
              </Include.Text> */}
            </View>
            :
            elementArray
          }
        </Include.ScrollView>
        </ImageBackground>
        <SafeAreaView style={{ flex: 0, backgroundColor: '#fff' }} />
      </View>
    )
    return content
  }

  onFocus = () => {
    PushNotiManager.handlePopup();
  }

  componentDidMount() {
    super.componentDidMount()
    this.processNotifyList()

    const {navigation} = this.props;
    this.unsubscribe = navigation.addListener('focus', this.onFocus);
  }

  UNSAFE_componentWillMount() {
    super.UNSAFE_componentWillMount()
    this.appStateSubscription = AppState.addEventListener('change', this.handleAppStateChange)
  }

  componentWillUnmount() {
    super.componentWillUnmount()
    this.appStateSubscription.remove()
    if (this.unsubscribe) {
      this.unsubscribe();
    }
  }
}
/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  return {
    navigator: state.Navigator,
    notify: state.Notify,
    notifications: state.Notifications,
    appSetting: state.AppSetting
  }
}

export default connect(selectActions, undefined, undefined, { withRef: true })(BellNotifyScreen)
