var _ = require('lodash')
//LIB
import React  from 'react';
import {
  View,
  ScrollView,
  RefreshControl,
  Button,
  Image,
  ActivityIndicator ,
  Modal,
  Platform,
  AppState,
  TouchableOpacity,
  StatusBar,
  LayoutAnimation,
  PermissionsAndroid,
  Alert,
  SafeAreaView,
  Text,
  Linking
} from 'react-native';

var DeviceInfo = require('react-native-device-info');
var {Actions} = require('react-native-router-flux');
import { connect } from 'react-redux';

import * as Animatable from 'react-native-animatable';
import LinearGradient from 'react-native-linear-gradient';
var locationManager = require('../modules/LocationManager');
import LottieView from 'lottie-react-native';
import Swiper from 'react-native-swiper';
//action

//components
import RDActionsTypes from '../../actions/RDActionsTypes'
import RDActions from '../../actions/RDActions'
var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
var Util = require('../../Util/Util');
var Include = require('../../Include');

var {popupActions} = require('../popups/PopupManager');
var {globalVariableManager}= require('../modules/GlobalVariableManager');
import SocketOrderManager from '../modules/SocketOrder';

var ButtonWrap = require('../elements/ButtonWrap');

//screens
import Screen from './Screen'

// popups
import DefaultPopup from '../popups/DefaultPopup'
import DestroyTokenPopup from '../popups/DestroyTokenPopup'

// actions
import UserActions_MiddleWare from '../../actions/UserActions_MiddleWare'
import AppStateActions_MiddleWare from '../../actions/AppStateActions_MiddleWare'
import FeedsSystemActions_MiddleWare from '../../actions/FeedsSystemActions_MiddleWare'
//variable

// var styles = StyleSheet.create({
//
// })

//

class SwitchModeScreen extends Screen{
  static componentName = 'SwitchModeScreen'
  static sceneConfig ={
    ...Screen.sceneConfig,
    hideNavBar: true
  }
  // static defaultProps = {}
  // static propTypes = {}
  constructor(props){
    super(props);
    this.state = _.merge(this.state,
    {
      configStep:1
    })

    this.onPressMode = this.onPressMode.bind(this);
    this.handleAppStateChange = this.handleAppStateChange.bind(this);
    this.setLocation = this.setLocation.bind(this)
    this.handleAfterRequestPermission = this.handleAfterRequestPermission.bind(this);
  }
  // static renderRightButton(scene){
  //   return (
  //     <View style={Themes.current.screen.rightButtonWrapNavBar}>
  //       <Include.Text>RightButton</Include.Text>
  //     </View>
  //   )
  // }
  // static renderLeftButton(scene){
  //   return (
  //     <View style={Themes.current.screen.leftButtonWrapNavBar}>
  //       <Include.Text>LeftButton</Include.Text>
  //     </View>
  //   )
  // }
  // static renderTitle(scene){
  //   return(
  //     <View style={Themes.current.screen.titleWrapNavBarCenter}>
  //       <Include.Text style={Themes.current.text.navBartitle}>title</Include.Text>
  //     </View>
  //   )
  // }

  onRefresh(){
    super.onRefresh();
    var {dispatch} = this.props;
  }

  onGetMore(){
    super.onGetMore();
    var {dispatch} = this.props;
  }

  showLoading() {
    return (
      <View style={{position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, alignItems: 'center', justifyContent: 'center'}}>
        <View
          style={{paddingVertical: 10, backgroundColor: '#000', borderRadius: 5, paddingHorizontal: 30, opacity: 0.5}}>
          <ActivityIndicator
            color = {'#fff'}
            size = 'large'/>
        </View>
      </View>
    )
  }
  renderButtonMode(text, styleContainer, styleText, onPress, image) {
    return (
      <View

        style={{backgroundColor: '#000'}}
        >
        <TouchableOpacity
          style={[styles.buttonContainer]}
          onPress={onPress}>
          {image?
            <View style={{flexDirection: 'row'}}>

              <View style={{flex:1, backgroundColor:'transparent', alignItems:'center', justifyContent:'center'}}>
                <Include.Text style={[styleText]}>{text}</Include.Text>
              </View>
            </View>
            :<Include.Text style={styleText}>{text}</Include.Text>
          }
        </TouchableOpacity>
      </View>
    )
  }
  onPressMode(type) {
    popupActions.setRenderContentAndShow(DefaultPopup, {
      title: 'Lưu ý',
      description: `${type === 0 ? 'Bạn chuẩn bị chọn vai trò là Tài xế HeyU ' : 'Bạn chuẩn bị chọn sử dụng dịch vụ của HeyU'}`,
      buttonTitle: 'Đồng ý',
      buttonTitle2: 'Chọn lại',
      onPress: () => {
        popupActions.popPopup();
        const {dispatch, appSetting} = this.props;

        dispatch(UserActions_MiddleWare.chooseMode({type}))
        .then(() => {
          let mode = 'shipper';
          if(type === 2) {
            mode = 'shop';
          }
          dispatch(RDActions.AppSetting.setMode({mode}))

          this.moveToLocationStep(true);
        })
      },
      onPress2: () => {
        popupActions.popPopup();
      }
    })
  }

  handleNavigate() {
    const {appSetting, dispatch} = this.props;

    let screenName = '';
    if(appSetting.mode === 'shop') {
      screenName = 'ServiceAvailableListContainer';
    } else if(appSetting.mode === 'shipper') {
      screenName = 'FeedsScreenContainer';
    }

    dispatch(AppStateActions_MiddleWare.setShowLoading(true));
    dispatch(AppStateActions_MiddleWare.getConfig())
      .then((res) => {
        dispatch(AppStateActions_MiddleWare.setShowLoading(false));
        Actions[screenName]({
          type: 'reset'
        });
      })
      .catch(() => {
        dispatch(AppStateActions_MiddleWare.setShowLoading(false));
        Actions[screenName]({
          type: 'reset'
        });
      })
  }

  onPressAcceptLocation(isUserPress = false, appStateActive = false) {
    const {dispatch, appSetting} = this.props;
    if(Platform.OS === 'android') {
      locationManager.checkAndRequestPermission()
       .then((result) => {
         locationManager
           .checkAndRequestPermissionAR(appSetting.mode)
           .then((res) => {
             this.handleAfterRequestPermission(true)
           })
           .catch((err)=>{
             Alert.alert('', 'Bạn vui lòng cho ứng dụng truy cập Hoạt động thể chất để có thể bắn đơn cho bạn kể cả khi đang ẩn app.', [
               { text: 'Đồng ý', onPress: () => Linking.openSettings() },
               { text: 'Bỏ qua', onPress: () => console.log('No Pressed'), style: 'cancel' }
             ]);
           })
       })
       .catch((err) => {
         if(isUserPress) {
             Linking.openSettings();
         }
       })
    } else {
      if(isUserPress) {
          Linking.openSettings();
          return
      }
      this.handleAfterRequestPermission(appStateActive)
    }
  }

  handleAfterRequestPermission(appStateActive = false) {

    const {dispatch, appSetting} = this.props;
    dispatch(AppStateActions_MiddleWare.setShowLoading(true))
    locationManager
      .getCurrentLocation()
      .then((location) => {

        // this.props.dispatch(FeedsSystemActions_MiddleWare.getLocationName({ lat: location.lat, lng: location.lng }))
        //   .then(data => {
        //     let obj = { name: data.res.data, location: { lat: location.lat, lng: location.lng } }
        //     this.props.dispatch(RDActions.AppSetting.addSenderInf(obj))
        //   })

        return dispatch(UserActions_MiddleWare.getRegionByLatLng({location}));
      })
      .then((region) => {
        if (!appStateActive) {
          return;
        }

        dispatch(AppStateActions_MiddleWare.setShowLoading(false))
        // TODO: Check region empty
        this.handleNavigate();
      })
      .catch((err) => {
        dispatch(AppStateActions_MiddleWare.setShowLoading(false))
        globalVariableManager.rootView.showToast("Đã có lỗi xảy ra vui lòng thử lại");
      })
  }

  moveToLocationStep(appStateActive = false) {
    const {dispatch, appSetting} = this.props;
    if(this.state.configStep !== 2) {
      this.setState({
        configStep: 2
      })
    }
  }

  renderDesctiptionChooseMode() {
    return (
      <Animatable.View
        animation="zoomIn"
        duration={750}
        style={{
            justifyContent:'center',
            alignItems:'center',
            flex: 1,
            paddingBottom:15
          }}>


      </Animatable.View>
    )
  }

  renderDesctiptionRequireLocation() {
    const {appSetting, user} = this.props;
    const {heightScreen} = Define.constants;
    return (
        <View style={{marginHorizontal:5, marginTop: heightScreen < 890 ? 8 : 10, justifyContent:'center', paddingBottom: heightScreen < 890 ? 8 : 20}}>
          <View style={{justifyContent: 'center', alignItems: 'center'}}>
            <Text allowFontScaling={false} style={{fontSize: 20, color:'#012548', textAlign: 'center', fontFamily: Define.constants.fontBold500, marginBottom: heightScreen < 890 ? 8 : 16}}>Bạn đang sử dụng vị trí</Text>
            <Text allowFontScaling={false} style={{fontSize: 18, color: '#7F8182', textAlign: 'center', fontFamily: Define.constants.fontBold500}}>{user.addressPicked.name}</Text>
          </View>
        </View>
    )
  }
  renderAskForLocationPermissions() {
    const {user} = this.props;
    const {heightScreen} = Define.constants;
    let title = `Cho phép truy cập vị trí?`
    const description = 'HeyU sử dụng vị trí của bạn để tính khoảng cách đơn hàng, cải thiện việc giao nhận hàng chính xác hơn và giúp HeyU hỗ trợ tốt hơn bạn nhé'
    let message = `Bằng việc "Cho phép truy cập vị trí" sẽ giúp tài xế đến lấy hàng, giao hàng nhanh chóng, chính xác. Hỗ trợ quý khách tốt hơn trong quá trình sử dụng.`
    return (
      <View style={{marginHorizontal:5, marginTop: heightScreen < 890 ? 8 : 10, justifyContent:'center', paddingBottom: heightScreen < 890 ? 8 : 20}}>
        <View>
          <Text allowFontScaling={false}
            style={{
              fontSize: 24,
              color:'#012548',
              textAlign: 'center',
              fontFamily: Define.constants.fontBold500
            }}>
              {title}
            </Text>
          <Text allowFontScaling={false}
            style={{
              fontSize: 18,
              color:'#7F8182',
              textAlign: 'center',
              fontFamily: Define.constants.fontBold400,
              marginTop: 16,
              lineHeight: 25.2
            }}>
              {message}
            </Text>
        </View>
        <View
          style={{alignItems:'center', justifyContent:'center'}}>
            <LottieView source={require('../../../assets/Animation/animationRequireLocation')} autoPlay style={{width: '100%', height: Define.constants.widthScreen * (heightScreen < 890 ? 0.4 : 0.5)}}/>
        </View>
      </View>
   )
  }
  renderContentChooseMode() {
    if (this.props.appState.configRouting) {
      return null;
    }

    return (
      <View style={{paddingBottom: 30, paddingHorizontal: 24, backgroundColor: 'transparent'}}>
        <View style={{marginVertical: 10}}>
          <TouchableOpacity
            style={{backgroundColor: '#0473CD', height: 60, justifyContent: 'center', alignItems: 'center', borderRadius: 6}}
            onPress={() => {this.onPressMode(2)}}
          >
            <Include.Text style={{color: '#fff', backgroundColor: 'transparent', fontSize: 18}}>
              Tôi muốn sử dụng dịch vụ
            </Include.Text>
          </TouchableOpacity>
        </View>
        <View style={{marginVertical: 10}}>
          <TouchableOpacity
            style={{backgroundColor: 'transparent', height: 60, justifyContent: 'center', alignItems: 'center', borderRadius: 6}}
            onPress={() => {this.onPressMode(0)}}
          >
            <Include.Text style={{color: '#0473CD', backgroundColor: 'transparent', fontSize: 18}}>
              Tôi là tài xế HeyU
            </Include.Text>
          </TouchableOpacity>
        </View>
      </View>
    )
  }

  renderRequireLocation() {
    const {heightScreen} = Define.constants;
    let textButton = 'Cho phép truy cập vị trí';
    if(this.props.user && this.props.user.addressPicked && this.props.user.addressPicked.location) {
      return (
        <View style={{paddingBottom: 40, paddingHorizontal: 24}}>
          <View style={{marginVertical: heightScreen < 890 ? 0 : 10}}>
            <TouchableOpacity
              style={{backgroundColor: 'transparent', height: 60, justifyContent: 'center', alignItems: 'center', borderRadius: 6}}
              onPress={() => {
                Actions.PickLocationByMapScreen({
                  placeholder: 'Vị trí của bạn',
                  textButton: 'Chọn làm vị trí hiện tại',
                  onPress: this.setLocation
                })
              }}
            >
              <Text allowFontScaling={false} style={{color: '#1589D8', fontSize:18, textAlign: 'center', fontFamily: Define.constants.fontBold500 }}>Nhập vị trí mới</Text>
            </TouchableOpacity>
          </View>
          <View style={{marginVertical: heightScreen < 890 ? 8 : 10}}>
            <TouchableOpacity
              style={{backgroundColor: '#1589D8', height: 60, justifyContent: 'center', alignItems: 'center', borderRadius: 16}}
              onPress={() => {
                this.onPressAcceptLocation(true)
              }}>
                <Text allowFontScaling={false} style={{color: '#fff', fontSize:18, backgroundColor:'transparent', fontFamily: Define.constants.fontBold500}}>Cho phép truy cập vị trí</Text>
            </TouchableOpacity>
          </View>
          <View style={{marginVertical: heightScreen < 890 ? 0 : 10}}>
            <TouchableOpacity
              style={{backgroundColor: 'transparent', height: 60, justifyContent: 'center', alignItems: 'center', borderRadius: 6}}
              onPress={() => {
                this.setLocation(this.props.user.addressPicked && this.props.user.addressPicked.location ? this.props.user.addressPicked : null)
              }}
            >
              <Text allowFontScaling={false} style={{color: '#1589D8',textAlign: 'center', fontSize: 18, fontFamily: Define.constants.fontBold500}}>Giữ vị trí hiện tại</Text>
            </TouchableOpacity>
          </View>
        </View>
      )
    }
    return (
      <View style={{paddingBottom: 30, paddingHorizontal: 24}}>
          <View style={{marginVertical: 10}}>
            <TouchableOpacity
              style={{backgroundColor: '#1589D8', height: 56, justifyContent: 'center', alignItems: 'center', borderRadius: 16}}
              onPress={() => {
                this.onPressAcceptLocation(true)
              }}>
                <Include.Text style={{color: '#fff', fontSize:18, backgroundColor:'transparent', fontFamily: Define.constants.fontBold500}}>{textButton}</Include.Text>
            </TouchableOpacity>
          </View>
          <View style={{marginVertical: 10}}>
            <TouchableOpacity
              style={{
                backgroundColor: 'transparent',
                height: 56,
                justifyContent: 'center',
                alignItems: 'center',
                borderRadius: 16,
                borderWidth: 1,
                borderColor: '#1589d8'
              }}
              onPress={() => {
                Actions.PickLocationByMapScreen({
                  placeholder: 'Vị trí của bạn',
                  textButton: 'Chọn làm vị trí hiện tại',
                  onPress: this.setLocation
                })
              }}
            >
              <Include.Text style={{color: '#1589D8', fontSize: 18, fontFamily: Define.constants.fontBold500}}>Nhập vị trí</Include.Text>
            </TouchableOpacity>
          </View>
      </View>
    )
  }

  setLocation(addr) {

    const {dispatch, user} = this.props;
    let location = user.addressPicked && user.addressPicked.location ? user.addressPicked.location : null
    if(addr) {
      location = addr.location;
      dispatch(RDActions.User.setAddressPick(addr))
      dispatch(RDActions.AppSetting.addCurrentLocation(addr))
      // dispatch(RDActions.AppSetting.addSenderInf(addr))
    }

    dispatch(UserActions_MiddleWare.getRegionByLatLng({location}))
      .then(() => {
        this.handleNavigate()
      })
  }

  renderScreenContent(){
    const {heightScreen} = Define.constants;

    return (
      <SafeAreaView style={{flex: 1, backgroundColor:'#fff', paddingTop:Platform.OS === 'ios'? 0 :StatusBar.currentHeight, justifyContent:'center',}}>
        <View style={{backgroundColor: 'transparent', flex: 1, justifyContent:'center'}}>
          {
            this.state.configStep === 1 ?
                <View style={{paddingHorizontal: 24, backgroundColor: '#fff', alignItems:'center', justifyContent:'center'}}>
                  <View style={{width: Define.constants.widthScreen , height: Define.constants.heightScreen * 0.4+20, alignItems:'center', justifyContent:'center', backgroundColor: '#fff'}}>
                    <Swiper
                      style={{backgroundColor: '#fff'}}
                      autoplay={true}
                      autoplayTimeout={3}
                      renderPagination={() => <View/>}
                    >
                      {/* <View style={{backgroundColor: 'transparent', alignItems:'center', justifyContent:'center', paddingHorizontal: 16, backgroundColor: '#fff'}}>
                        <LottieView source={require('../../../assets/Animation/animationWelcome')} autoPlay style={{width: '100%', height: Define.constants.widthScreen * 0.5}}/>
                        <Include.Text style={{fontSize: 28, color:'#514844', textAlign: 'center', fontWeight: '500'}}>Chào mừng bạn đến với HeyU</Include.Text>
                      </View> */}
                      <View style={{backgroundColor: 'transparent', alignItems:'center', justifyContent:'center', paddingHorizontal: 16, backgroundColor: '#fff'}}>
                        <LottieView source={require('../../../assets/Animation/animationSearchingDriver')} autoPlay style={{width: '100%', height: Define.constants.widthScreen * 0.5}}/>
                        <Include.Text style={{fontSize: 28, color:'#514844', textAlign: 'center', fontWeight: '500'}}>Giao gì cũng được HeyU giao là đảm bảo</Include.Text>
                      </View>
                      <View style={{backgroundColor: 'transparent', alignItems:'center', justifyContent:'center', paddingHorizontal: 16, backgroundColor: '#fff'}}>
                        <LottieView source={require('../../../assets/Animation/animationOrderFood')} autoPlay style={{width: '100%', height: Define.constants.widthScreen * 0.5}}/>
                        <Include.Text style={{fontSize: 28, color:'#514844', textAlign: 'center', fontWeight: '500'}}>Ở nhà cũng được HeyU mua hộ là được</Include.Text>
                      </View>
                      <View style={{backgroundColor: 'transparent', alignItems:'center', justifyContent:'center', paddingHorizontal: 16, backgroundColor: '#fff'}}>
                        <LottieView source={require('../../../assets/Animation/animationEnjoyMeal')} autoPlay style={{width: '100%', height: Define.constants.widthScreen * 0.5}}/>
                        <Include.Text style={{fontSize: 28, color:'#514844', textAlign: 'center', fontWeight: '500'}}>Ăn gì cũng được HeyU giao hộ nóng giòn</Include.Text>
                      </View>
                      <View style={{backgroundColor: 'transparent', alignItems:'center', justifyContent:'center', paddingHorizontal: 16, backgroundColor: '#fff'}}>
                        <LottieView source={require('../../../assets/Animation/animationLoginScreen')} autoPlay style={{width: '100%', height: Define.constants.widthScreen * 0.5}}/>
                        <Include.Text style={{fontSize: 28, color:'#514844', textAlign: 'center', fontWeight: '500'}}>Đi đâu cũng được HeyU chở đi là được</Include.Text>
                      </View>
                    </Swiper>
                  </View>
                  <View style={{alignItems:'center', justifyContent:'center', paddingHorizontal: 24, marginBottom: 10}}>
                    <Include.Text style={{fontSize: 18, color:'#708090', textAlign: 'center'}}>Bạn hãy chọn một trong hai lựa chọn bên dưới để tiếp tục</Include.Text>
                  </View>
                </View>
            :
            <View style={{paddingHorizontal: 24, backgroundColor: 'transparent'}}>
              {this.props.user && this.props.user.addressPicked && this.props.user.addressPicked.location ?
                <View style={{ marginTop: 16}}>
                  <Text allowFontScaling={false}
                    style={{
                      fontSize: 24,
                      color: '#012548',
                      textAlign: 'center',
                      fontFamily: Define.constants.fontBold500,
                      marginBottom: 16,
                      lineHeight: 35,
                      marginHorizontal: 8
                    }}>
                    Cho phép truy cập vị trí rất quan trọng
                  </Text>
                  <Text allowFontScaling={false}
                    style={{
                      fontSize: 18,
                      color: '#7F8182',
                      textAlign: 'center',
                      fontFamily: Define.constants.fontBold400,
                      lineHeight: 25,
                    }}>
                    Vị trí chính xác sẽ giúp tài xế di chuyển đến đúng điểm lấy hàng và giao hàng nhanh chóng hơn
                  </Text>
                  <View style={{ alignItems: 'center', justifyContent: 'center' }}>
                    <LottieView source={require('../../../assets/Animation/animationRequireLocation')} autoPlay style={{ width: '100%', height: Define.constants.widthScreen * (heightScreen < 890 ? 0.3 : 0.4) }} />
                  </View>
                  {this.renderDesctiptionRequireLocation()}
                </View>
              : this.renderAskForLocationPermissions()}
            </View>
          }
        </View>
        <View style={{backgroundColor: 'transparent'}}>
          {this.state.configStep === 1 ? this.renderContentChooseMode() : this.renderRequireLocation()}
        </View>
      </SafeAreaView>
    )
  }
  componentDidMount(){
    super.componentDidMount();
    SocketOrderManager.destroy();
    this.appStateSubscription = AppState.addEventListener('change', this.handleAppStateChange);
    StatusBar.setBarStyle('dark-content');
  }
  UNSAFE_componentWillMount() {
    super.UNSAFE_componentWillMount();
    const {dispatch, appSetting, appState} = this.props;

    if(appSetting.mode) {
      this.moveToLocationStep();
    } else {
      if (appState.configRouting) {
        dispatch(UserActions_MiddleWare.chooseMode({type: 2}))
          .then(() => {
            dispatch(RDActions.AppSetting.setMode({mode: 'shop'}));

            this.moveToLocationStep(true);
          })
      }
    }
  }

  componentWillUnmount() {
    this.appStateSubscription.remove();
    // StatusBar.setBarStyle('light-content');
  }
  componentDidUpdate() {
    if(Platform.OS === 'ios') {
      LayoutAnimation.easeInEaseOut();
    } else {
      LayoutAnimation.configureNext({
        duration: 300,
        update: {
          type: LayoutAnimation.Types.linear
        }
      });
    }
  }
  handleAppStateChange(currentAppState){
    switch (currentAppState) {
      case 'active':{
        if(this.state.configStep === 2 && !locationManager.isRequestingPermision) {
          this.moveToLocationStep(true);
        }

        break;
      }
      case 'background':{
        break;
      }
      case 'inactive':{
        break;
      }
      default:
    }
  }
}

const styles = {
  contentContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'flex-end',
    paddingBottom: 10,
  },
  buttonContainer: {
    flexDirection:'row',
    borderRadius: 25,
    height: 50,
    alignItems: 'center',
    justifyContent: 'center',
    width:Define.constants.widthScreen*4/5,
  }
}
/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  return {
    navigator: state.Navigator,
    appSetting: state.AppSetting,
    appState: state.AppState,
    user: state.User
  }
}

export default connect(selectActions, undefined, undefined, {withRef: true})(SwitchModeScreen);
