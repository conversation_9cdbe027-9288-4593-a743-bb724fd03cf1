
var _ = require('lodash')

//LIB
import React from 'react';
import {
  View,
  ScrollView,
  Image,
  FlatList,
  TouchableOpacity,
  Text,
  AppState,
  TouchableWithoutFeedback,
  Platform,
  StatusBar,
  SafeAreaView,
  Linking,
  Animated,
  LayoutAnimation,
  TextInput,
  ActivityIndicator,
  Dimensions,
  Keyboard,
  ImageBackground
} from 'react-native';


var { Actions } = require('react-native-router-flux');
import { connect } from 'react-redux';
import { TabView, SceneMap, TabBar } from 'react-native-tab-view';
var locationManager = require('../modules/LocationManager');
import Placeholder from 'rn-placeholder';

//action
import Carousel, { ParallaxImage, Pagination } from 'react-native-snap-carousel';

//components
var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
var Util = require('../../Util/Util');
var Include = require('../../Include');
import { getStatusBarHeight } from 'react-native-status-bar-height';
import HeyUIcon from '../elements/HeyUIcon/HeyUIcon';
import LinearGradient from 'react-native-linear-gradient';

var { popupActions } = require('../popups/PopupManager');
var { globalVariableManager } = require('../modules/GlobalVariableManager');

var ButtonWrap = require('../elements/ButtonWrap');
import PenguinLinearIcon from '../elements/PenguinLinearIcon/PenguinLinearIcon';
import PenguinBoldIcon from '../elements/PenguinBoldIcon/PenguinBoldIcon';
import IOCHBActions_MiddleWare from '../../actions/IOCHBActions_MiddleWare'

//screens
import Screen from './Screen'
import NotifyScreen from './NotifyScreen';
import AccountScreen from './AccountScreen';

// popups
import DefaultPopup from '../popups/DefaultPopup';


// actions

//variable

// var styles = StyleSheet.create({
//
// })

//

class InstructionsForUseScreen extends Screen {
  static componentName = 'InstructionsForUseScreen'
  static sceneConfig = {
    ...Screen.sceneConfig,
    hideNavBar: true,
  }
  // static defaultProps = {}
  // static propTypes = {}
  constructor(props) {
    super(props);
    this.state = _.merge(this.state,
      {
        bottom: 0,
        isLoading: true,
        listCategory: [],
        filter: false,
        type: '',
        currentFilter: {
          _id: '',
          name: 'Tất cả'
        },
        listHandBook: [],
        page: 1
      })
    this.listCategory =
      [{
        _id: '',
        name: 'Tất cả'
      }
      ]
    this.textSearch = ''
    this.canGetMore = true
    this.isGettingMore = false
    this.onRefresh = this.onRefresh.bind(this);
    this.onGetMore = this.onGetMore.bind(this);
    this.handleAppStateChange = this.handleAppStateChange.bind(this);
  }

  listHandBookCategory = () => {
    var { dispatch } = this.props;
    dispatch(IOCHBActions_MiddleWare.handbookCategory())
      .then((result) => {
        this.setState({
          listCategory: this.listCategory.concat(result.res.data)
        })
      })
  }

  getListHandBook = (getMore = false) => {
    const { dispatch } = this.props;
    if (!this.canGetMore) {
      this.isGettingMore = false;
      return;
    }
    let limit = 10;
    let page = getMore ? this.state.page + 1 : 1;
    let obj = { page, limit };
    if (this.state.currentFilter._id) {
      obj.category = this.state.currentFilter._id;
    }

    dispatch(IOCHBActions_MiddleWare.listHandbook(obj))
      .then(result => {
        const data = _.get(result, 'res.data', []);
        this.isGettingMore = false;
        if (data.length === limit) {
          this.canGetMore = true;
        }
        if (data.length < limit) {
          this.canGetMore = false;
        }
        if (data.length) {
          if (!getMore) {
            this.setState({ listHandBook: data, isLoading: false, page: 1 });
          } else {
            const handBookData = this.state.listHandBook.concat(data);
            this.setState({ listHandBook: handBookData, isLoading: false, page });
          }
        } else {
          this.setState({ isLoading: false });
        }
      })
      .catch(err => {
      });
  }


  onRefresh() {
    super.onRefresh();
    this.setState({ page: 1 }, () => {
      this.listHandBookCategory();
      this.getListHandBook(false);
    });
  }

  handleAppStateChange(newState) {
    switch (newState) {
      case 'active':
        this.onRefresh();
        break;
      case 'background':
        break;
      default:
        break;
    }
  }

  onGetMore() {
    super.onGetMore();
    this.getListHandBook(true);
  }

  renderListing() {
    var { navigation, user } = this.props;
    return (
      <View edges={this.state.bottom ? ['top'] : ['top', 'bottom']} style={{ position: 'absolute', paddingTop: 0, top: 0, bottom: this.state.bottom, width: Define.constants.widthScreen }}>
        <SafeAreaView style={{ flex: 0 }} />
        <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center', width: '100%', paddingVertical: 16, elevation: 3, zIndex: 0, paddingTop: Platform.OS === 'ios' ? 6 : StatusBar.currentHeight }}>
          <TouchableOpacity
            style={{ zIndex: 3, width: 30, height: 30, justifyContent: 'center', alignItems: 'center', position: 'absolute', left: 16, borderTopRightRadius: 16, borderTopLeftRadius: 16, top: Platform.OS === 'ios' ? 6 : StatusBar.currentHeight }}
            onPress={() => {
              navigation.goBack();
            }}
          >
            <View style={{ justifyContent: 'center', alignItems: 'center' }}>
              <HeyUIcon name={'fi-sr-arrow-left'} size={24} color={'#fff'} />
            </View>
          </TouchableOpacity>
          <Include.Text style={{ color: '#fff', fontSize: 18, fontFamily: Define.constants.fontBold500 }}>
            Cẩm nang nghiệp vụ
          </Include.Text>
        </View>
        <View style={{
          height: 38,
          zIndex: 5,
          elevation: 3,
          marginBottom: 8,
          marginHorizontal: 12
        }}>
          <FlatList
            horizontal={true}
            keyExtractor={(item, index) => {
              return index;
            }}
            contentContainerStyle={{}}
            showsHorizontalScrollIndicator={false}
            ref={ref => (this._tabScrollRef = ref)}
            initialScrollIndex={0}
            initialNumToRender={this.state.listCategory.length}
            data={this.state.listCategory}
            // getItemLayout={(data, index) => ({
            //   length: 135,
            //   offset: 135 * index,
            //   index,
            // })}
            renderItem={({ item, index }) => {
              return (
                <TouchableOpacity
                  key={index}
                  onPress={() => {
                    this.setState({ currentFilter: item, isLoading: true, listHandBook: []}, () => {
                      this.canGetMore = true
                      this.getListHandBook(false);
                    });
                    this._tabScrollRef.scrollToIndex({
                      index,
                      animated: true,
                      viewOffset: 150,
                    });
                  }}
                  style={{
                    paddingVertical: 6,
                    paddingHorizontal: 16,
                    marginHorizontal: 4,
                    backgroundColor: item.name === this.state.currentFilter.name ? '#021E38' : '#2E3236',
                    borderColor:
                      item.name === this.state.currentFilter.name
                        ? '#007CFE'
                        : '#656C75',
                    borderWidth: 1,
                    borderRadius: 16,
                    alignSelf: 'center',
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}>
                  <Text allowFontScaling={false}
                    style={{
                      fontSize: 14,
                      fontFamily:
                        item.name === this.state.currentFilter.name
                          ? Define.constants.fontBold600
                          : Define.constants.fontBold400,
                      color:
                        item.name === this.state.currentFilter.name
                          ? '#fff'
                          : '#F3F3F3',
                    }}>
                    {item.name}
                  </Text>
                </TouchableOpacity>
              );
            }}
          />
        </View>
        {!this.state.isLoading && !this.state.listHandBook.length ?
          <View style={{ justifyContent: 'center', alignItems: 'center', position: 'absolute', top: Define.constants.heightScreen / 2.5, alignSelf: 'center' }}>
            <Image
              style={{
                width: 200,
                height: 200,
                zIndex: 0
              }}
              resizeMode={'stretch'}
              source={{ uri: 'https://media.icongdanso.vn/uploads/mobiles/2025-02-22-image 301.png' }}
            />
            <Include.Text
              style={{
                fontFamily: Define.constants.fontBold600,
                fontSize: 18,
                color: '#fff',
                marginTop: 12
              }}>
              Chưa có thông tin
            </Include.Text>
          </View> : null
        }
        {this.state.isLoading ?
          <View
            style={[
              Themes.current.screen.bodyView,
              this.props.bodyStyle,
              { backgroundColor: '#042F71', paddingTop: 12, top: 0 },
            ]}>
            {Array(10)
              .fill(null)
              .map(() => {
                return (
                  <View
                    style={{
                      paddingHorizontal: 16,
                      paddingVertical: 10,
                      backgroundColor: '#042F71',
                      marginBottom: 5,
                      flexDirection: 'row',
                    }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                      <View style={{  marginRight: 6,alignItems: 'center' }}>
                        <Placeholder.Box
                          animate={'fade'}
                          color="#005ABA4D"
                          width={(Define.constants.widthScreen - 44) / 2}
                          height={170}
                          radius={12}
                        />
                      </View>
                      <View style={{ marginLeft: 6, alignItems: 'center' }}>
                        <Placeholder.Box
                          animate={'fade'}
                          color="#005ABA4D"
                          width={(Define.constants.widthScreen - 44) / 2}
                          height={170}
                          radius={12}
                        />
                      </View>
                    </View>
                  </View>
                );
              })}
          </View> : null
        }
        {!this.state.isLoading && this.state.listHandBook.length ?
          <FlatList
            ref={refs => {
              this.scrollRef = refs;
            }}
            data={this.state.listHandBook}
            renderItem={this.renderItem}
            keyExtractor={(item, index) => item + index}
            removeClippedSubviews={false}
            onEndReachedThreshold={0.4}
            contentContainerStyle={{ paddingBottom: 16, zIndex: 5, flexGrow: 1, flexDirection:'row', justifyContent:'center', paddingTop: 16}}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps='handled'
            refreshing={false}
            onScroll={() => {
              Keyboard.dismiss()
            }}
            onRefresh={() => {
              this.onRefresh(true)
            }}
            onEndReached={() => { this.onGetMore()}}
            ListFooterComponent={() => {
              if (this.canGetMore && this.state.listHandBook.length) {
                return (
                  <View style={{ alignSelf: 'center', padding: 10 }}>
                    <ActivityIndicator size={'small'} color={'#929394'} />
                  </View>
                );
              }
              return null;
            }}
          >
          </FlatList> : null}
      </View>
    )
  }

  renderItem = ({ item, index }) => {
    var { navigation, user } = this.props;
    return (
      <TouchableOpacity
        onPress={() => {
          Linking.openURL(item.file)
        }}
        // onPress={() => {
        //   navigation.navigate('DetailInstructionsForUseScreen', { data: item });
        // }}
        style={{
          alignItems: 'center',
          width: (Define.constants.widthScreen - 44) / 2,
          borderWidth: 1,
          borderColor: '#F3F3F3',
          backgroundColor: '#007CFE',
          marginBottom: 16,
          paddingTop: 12,
          borderRadius: 12,
          marginHorizontal: 6
        }}>
          <Image
            source={{ uri: 'https://media-hb-dev.canbo.ai/uploads/avatar/2025-08-21-images%201.png' }}
            resizeMode='contain'
            style={{ width: (Define.constants.widthScreen - 44) / 4, height: 111, borderRadius: 8 }}
          />
        <View style={{ backgroundColor: '#1C1E21', flex:1, width: (Define.constants.widthScreen - 48) / 2, borderBottomLeftRadius: 12, borderBottomRightRadius: 12, padding: 12}}>
          <Text allowFontScaling={false}
            style={{
              fontSize: 14,
              fontFamily: Define.constants.fontBold400,
              color: '#fff',
            }}>
            {item.title}
          </Text>
        </View>
      </TouchableOpacity>
    )
  }


  renderScreenContent() {
    var { dispatch } = this.props;
    var content = null;
    let index = this.state.indexTab
    const statusBarHeight = Platform.OS === 'ios' ? getStatusBarHeight(true) : 8;

    content = (
      <View style={{ flex: 1 }}>
          <LinearGradient start={{x: 0, y: 0.3}} end={{x: 0, y: 0}} colors={['#003F9E', '#042F71', '#390404ff']} style={{flex: 1}}>
          {this.renderListing()}
        </LinearGradient>
        <SafeAreaView style={{ flex: 0, backgroundColor: '#023d96' }} />
      </View>
    )
    return content;
  }
  componentDidMount() {
    super.componentDidMount();
  }
}

/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  return {
    navigator: state.Navigator,
    iHeyU: state.IHeyU
  }
}

export default connect(selectActions, undefined, undefined, { withRef: true })(InstructionsForUseScreen);
