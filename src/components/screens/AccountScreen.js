
var _ = require('lodash')

//LIB
import React  from 'react';
import {
  View,
  ScrollView,
  TouchableOpacity,
  Image,
  Platform,
  StatusBar,
  AppState,
  SafeAreaView,
  Linking,
  Text,
  FlatList,
  ImageBackground
} from 'react-native';

var {Actions} = require('react-native-router-flux');
import { connect } from 'react-redux';
//action
import { TabActions } from '@react-navigation/native';
import ImagePicker from 'react-native-image-crop-picker';
var uploadFileManager = require('../modules/UploadFileManager');
//components
var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
var Util = require('../../Util/Util');
var Include = require('../../Include');
var UserUtil = require('../../Util/user');
import NewTag from '../elements/NewTag';
import HeyUI<PERSON> from '../elements/HeyUIcon/HeyUIcon';
import PenguinBoldIcon from '../elements/PenguinBoldIcon/PenguinBoldIcon';
import LinearGradient from 'react-native-linear-gradient';
import PenguinLinearIcon from '../elements/PenguinLinearIcon/PenguinLinearIcon';
import { SwiperFlatList } from 'react-native-swiper-flatlist';
const DeviceInfo = require('react-native-device-info');
import FeedsSystemActions_MiddleWare from '../../actions/FeedsSystemActions_MiddleWare'
var {Actions} = require('react-native-router-flux');
var {popupActions} = require('../popups/PopupManager');
var {globalVariableManager}= require('../modules/GlobalVariableManager');
import IHeyUActions_MiddleWare from '../../actions/IHeyUActions_MiddleWare';
import { getStatusBarHeight } from 'react-native-status-bar-height';
var NotifyUtil = require('../../Util/notify');

//screens
import Screen from './Screen'

// popups
import DefaultPopup from '../popups/DefaultPopup';

// actions
import UserActions_MiddleWare from '../../actions/UserActions_MiddleWare';
import AppStateActions_MiddleWare from '../../actions/AppStateActions_MiddleWare';
import NotificationsActions_MiddleWare from '../../actions/NotificationsActions_MiddleWare';
var RDActions = require( '../../actions/RDActions');


//variable

// var styles = StyleSheet.create({
//
// })

//

class AccountScreen extends Screen{
  static componentName = 'AccountScreen'
  static sceneConfig = {
    ...Screen.sceneConfig
  }
  // static defaultProps = {}
  // static propTypes = {}
  constructor(props){
    super(props);
    let { user } = this.props;
    this.state = _.merge(this.state, {
      onHeadFlag: true,
      urlImageFailed: false,
      businessType:[],
      youtube: '',
      linkGuide:  '',
      linkFanpage: '',
      isOpenGuide: 0,
      news: [],
      shortcutService: null,
      dataNoti: null,
      isOpenEBill: 0,
      authenShop: false,
      isOpenMembership: 0,
      images: [],
      contactInfo: {
        hotline: '**********'
      },
      configReferralCode: {
        isOpenProfile: 0
      },
    })
    this.onRefresh = this.onRefresh.bind(this);
    this.handleAppStateChange = this.handleAppStateChange.bind(this);
    this.onGetMore = this.onGetMore.bind(this);
    this.canGetMore = false;
    this.limit = 3;
    this.isGettingMore = false
    this.category = '65090cbcb2ce4e4aea2aa8ea'
    this.idImg = []
    this.uploadSuccess = []
    this.inviteCode = '';
    this.userInfo = {
      name: _.get(user, 'memberInfo.name', ''),
      phone: _.get(user, 'memberInfo.phone', ''),
      picture: _.get(user, 'memberInfo.avatar', ''),
      idCard: _.get(user, 'memberInfo.idCard', ''),
      birthday: _.get(user, 'memberInfo.birthday', ''),
      email: _.get(user, 'memberInfo.email', ''),
    }
    this.timeRefresh = 5 * 60 * 1000;
  }

  onRefresh(){
    super.onRefresh();
    var {dispatch} = this.props;
    this.lastRefresh = Date.now();
    this.canGetMore = false;
  }

  onGetMore(){
    super.onGetMore();
    var {dispatch} = this.props;
    this.isGettingMore = true;
    if (this.state.news.length && this.canGetMore) {
      this.canGetMore = false
      this.getNews(this.state.news[this.state.news.length - 1].order)
  }
  }

  initMenu() {
    const {user, appState} = this.props;
    this.config = [
      {
        iconPenguin: 'frame',
        hasBorderTopWidth: 1,
        text: 'Thông tin cá nhân',
        onPress: () => {
          globalVariableManager.navigatorManager.handleNavigator('ProfileScreen', {
          });
        }
      },
      {
        hasBorderTopWidth: 1,
        text: 'Đổi mật khẩu',
        iconPenguin: 'lock-1',
        onPress: () => {
          globalVariableManager.navigatorManager.handleNavigator('ChangePassAccountScreen', {
          });
        }
      },
      {
        hasBorderTopWidth: 1,
        text: 'Cẩm nang nghiệp vụ',
        iconPenguin: 'book-1',
        onPress: () => {
          globalVariableManager.navigatorManager.handleNavigator('InstructionsForUseScreen', {
          });
        }
      },
      {
        hasBorderTopWidth: 1,
        text: 'Hướng dẫn sử dụng',
        iconPenguin: 'document-text-1',
        onPress: () => {
            Linking.openURL('https://media.heyu.asia/uploads/nhannt/2025-08-26-hdsdappiochongbang.pdf')
        }
      },
      {
        text: 'Thống kê công việc',
        iconPenguin: 'shield-tick',
        onPress: () => {
          globalVariableManager.navigatorManager.handleNavigator('StatisticsReportScreen', {
          });
        }
      },

    ]
  }

  selectPhotoTappedByLib = () => {
    const images = this.state.images;

    ImagePicker.openPicker({
      width: 500,
      height: 500,
      cropping: true,
      compressImageQuality: Platform.OS === 'ios' ? 0.8 : 1
    }).then(image => {
      this.uploadFile(0, image)
    })
  }

  selectPhotoTappedByCam = () => {
    const images = this.state.images;

    ImagePicker.openCamera({
      width: 500,
      height: 500,
      cropping: true,
      compressImageQuality: Platform.OS === 'ios' ? 0.8 : 1
    }).then(image => {
      this.uploadFile(0, image)
    })

  }

  uploadFile(index, image) {
    const images = this.state.images;
    this.idImg[index] = `SS_${Date.now()}`;
    uploadFileManager.addFile(this.idImg[index], 'local', image.path);

    const dataUpload = {
      fileUpload: image.path,
      folder: 'avatar',
      fileName: image.fileName || 'image.jpg',
      id: this.idImg[index]
    }

    this.uploadSuccess[index] = false;

    uploadFileManager.upload(dataUpload)
      .then((result) => {
        index = this.idImg.indexOf(result.arg.id);
        if (index === -1) {
          uploadFileManager.declineFile(result.arg.id);
        } else {
          this.uploadSuccess[index] = true;
          images[index] = `${Define.constants.serverMediaAddr}${result.res.filename}`;
          this.setState({
            images,
            showFullPic: true
          }, () => {
            this.handleUpdateProfile()
          });
        }
      })
      .catch((err) => {
        images.splice(index, 1);
        this.idImg.splice(index, 1);
        this.uploadSuccess.splice(index, 1);
        this.setState({ images });
      })
  }

  declineFile = (index) => {
    const images = this.state.images;

    images.splice(index, 1);
    this.setState({ images });

    if (this.idImg && this.idImg[index]) {
      uploadFileManager.declineFile(this.idImg[index]);
      this.idImg.splice(index, 1);
      this.uploadSuccess.splice(index, 1);
    }
  }

  handleUpdateProfile() {
    let { dispatch, user } = this.props;
    let message = '';
    let avatar = this.state.images[0]
    let name = this.userInfo.name.trim();
    let email = this.userInfo.email.trim();
    let idCard = this.userInfo.idCard.trim();
    let birthday = this.userInfo.birthday.trim();
    const objUpdate = {
      name,
      email,
      idCard,
      birthday,
      avatar
    };
    if (objUpdate.name.trim() === _.get(user, 'memberInfo.name', '')
      && objUpdate.email.trim() === _.get(user, 'memberInfo.email', '')
      && objUpdate.birthday.trim() === _.get(user, 'memberInfo.birthday', '')
      && objUpdate.idCard.trim() === _.get(user, 'memberInfo.idCard', '')
      && (!this.state.configReferralCode.isOpenProfile || (this.state.configReferralCode.isOpenProfile && this.inviteCode.trim() === ''))
      && ((!objUpdate.avatar && this.userInfo.avatar === _.get(user, 'memberInfo.avatar', '')) || (objUpdate.avatar && objUpdate.avatar === _.get(user, 'memberInfo.avatar', '')))
    ) {
      message = 'Bạn chưa thay đổi ảnh đại diện'
    }
    if (message) {
      popupActions.setRenderContentAndShow(DefaultPopup, {
        title: 'Thông báo',
        description: message,
        onPress: () => {
          popupActions.popPopup();
        },
        buttonTitle: 'Xong'
      })
    } else {
      this.updateProfile(objUpdate);
    }
  }

  updateProfile(objUpdate) {
    var { dispatch, user } = this.props;
    dispatch(UserActions_MiddleWare.updateProfile(objUpdate))
      .then(() => {
        NotifyUtil.pushAlertTopNotify({
          type: 'succes',
          content: 'Cập nhật ảnh đại diện thành công',
          timeClose: 3000,
        })
      })
      .catch(err => {
        NotifyUtil.pushAlertTopNotify({
          type: 'error',
          content: 'Cập nhật ảnh đại diện thất bại',
          timeClose: 3000,
        })
      })
  }

  renderMenu() {
    return this.config.map((item, index) => {
      return (
        <View key={index}>
          {item.title ?
            <View style={{  paddingHorizontal: 16, paddingVertical: 16, borderTopWidth: 8, borderTopColor: '#656C75' }}>
              <Text allowFontScaling={false} style={{ color: '#fff', fontSize: 14, fontFamily: Define.constants.fontBold600 }}>{item.title}</Text>
            </View> : null}
          <TouchableOpacity
            style={{ flex: 1, flexDirection: 'row', alignItems: 'center', paddingVertical: 16}}
            key={index}
            onPress={item.onPress}
          >
            {item.iconPenguin ?
              <View style={{ alignItems: 'center', justifyContent: 'center',   height: "100%", paddingRight: 10 }}>
                <PenguinBoldIcon name={item.iconPenguin} color={'#fff'} size={24} />
              </View>
              : null}
            <View style={{ flex: 1, alignItems: 'center', flexDirection: 'row'}}>
              <View style={{ flex: 1, flexDirection: 'row', alignItems: 'center'}}>
                <Include.Text style={{ flex: 1, color: '#fff', fontSize: 16, fontFamily: Define.constants.fontBold400 }}>{item.text}</Include.Text>
                {item.secondaryText ?
                  <View style={{ alignSelf: 'flex-end', marginRight: 16 }}>
                    <Include.Text style={{color: '#1589d8', fontSize: 14, fontFamily: Define.constants.fontBold400 }}>
                      {item.secondaryText}
                    </Include.Text>
                  </View>
                : null}
              </View>
              <NewTag style={{ marginLeft: 12 }} updateInstance={true} tagName={item.text} />
              <PenguinLinearIcon name={'arrow-right-1'} color={'#fff'} size={20} />
            </View>
          </TouchableOpacity>
          {item.hasBorderTopWidth ?
            <View style={{ borderTopWidth: 1, borderTopColor: '#656C75' }}>
            </View> : null}
        </View>
      )
    })
  }


  renderUserProfile() {
    let {user} = this.props;
    if(!_.get(globalVariableManager, 'reduxManager.state.User.memberInfo._id', '')) {
      return null;
    }

    let username = _.get(user, 'memberInfo.name', '')
    let urlImage = _.get(user, 'memberInfo.avatar', '')
    let code = _.get(user, 'memberInfo.idNumber', '')
    let phone = _.get(user, 'memberInfo.phones', []).join(', ');
    let rankImage = _.get(user, 'memberInfo.rankImage', '')
    let position = user.memberInfo.positions.length ? user.memberInfo.positions[0].name : ''
    let units = user.memberInfo.units.length ? user.memberInfo.units[user.memberInfo.units.length - 1].name : ''

    return (
      <View
        style={{
          paddingHorizontal: 16,
          paddingBottom: 12,
          paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight + 16 : 16,
          zIndex: 99,
        }}>
        <View style={{
          alignItems: 'center',
          paddingHorizontal: 16,
          borderRadius: 16,
          justifyContent:'center'
        }}>
        <Include.Text style={{ backgroundColor: 'transparent', fontSize: 18, color: '#fff',fontFamily: Define.constants.fontBold600, paddingVertical: 12}}>Hồ sơ cán bộ</Include.Text>
        <View>
          {urlImage ?
            <Image
              source={{
                uri: urlImage
              }}
              style={{
                width: 72,
                height: 72,
                borderRadius: 120,
              }}
            /> :
            <View style={{
              backgroundColor: '#6fbde8', width: 72, height: 72, borderRadius: 120,
              alignSelf: 'center', justifyContent: 'center', alignItems: 'center',
            }}>
              <Include.Text style={{ backgroundColor: 'transparent', fontSize: 32, color: '#fff' }}>{UserUtil.getNameAsAvatar(username)}</Include.Text>
            </View>}
        </View>
          {username ?
          <View
            style={{
              backgroundColor: '#fff',
              paddingHorizontal: 8,
              borderRadius: 16,
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
              marginTop: 12,
            }}>
            <Text
              allowFontScaling={false}
              style={{
                fontSize: 16,
                fontFamily: Define.constants.fontBold600,
                color: '#D30500',
                marginRight: 8,
              }}>{username}</Text>
              {rankImage ?
              <View style={{width: 36, height: 18, justifyContent: 'center', alignItems: 'center'}}>
                <Image
                  source={{
                    uri: rankImage,
                  }}
                  resizeMode={'contain'}
                  style={{
                    width: 14 ,
                    height: 36,
                    transform: [{ rotate: '-90deg' }], // Rotate the image horizontally
                  }}
                />
                </View>
                 : null}
          </View> : null }
          {code ?
            <Text allowFontScaling={false}
              style={{
                fontSize: 16,
                color: '#fff',
                fontFamily: Define.constants.fontBold400,
                marginTop: 8,
              }}>
              Số hiệu: {code}
            </Text> : null}
          {position && units ?
            <Text allowFontScaling={false}
              style={{
                fontSize: 16,
                color: '#fff',
                fontFamily: Define.constants.fontBold400,
                marginTop: 4,
                textAlign: 'center'
              }}>
              {position} - {units}
            </Text> : null}
        </View>
        <Image
          style={{
            width: 90,
            height: 143,
            position: 'absolute',
            left: 0,
            top: 50,
            zIndex: 0
          }}
          resizeMode={'stretch'}
          source={{ uri: 'https://media.heyu.asia/uploads/mobiles/2025-01-08-hoaphuongdonavbar.png' }}
        />
        <Image
          style={{
            width: 90,
            height: 143,
            position: 'absolute',
            right: 0,
            top: 50,
            zIndex: 0
          }}
          resizeMode={'stretch'}
          source={{ uri: 'https://media.heyu.asia/uploads/mobiles/2025-01-08-hoaphuongdonavbar2.png' }}
        />
      </View>
    )
  }

  renderScreenContent(){
    const {appSetting, user, dispatch, navigation} = this.props;
    this.initMenu()
    var content = null;
    const statusBarHeight = Platform.OS === 'ios' ? getStatusBarHeight(true) : StatusBar.currentHeight;
    content = (
      <View style={{flex: 1,}}>
        {Platform.OS === 'ios' ?
          <StatusBar
            backgroundColor={'#fff'}
            barStyle={'light-content'}
          /> : null}
        {/* <View style={{
          flexDirection: 'row',
          justifyContent: 'center',
          alignItems: 'center',
          paddingTop: Platform.OS === 'ios' ? 0 : StatusBar.currentHeight,
          height: Define.constants.navBarHeight,
                    zIndex: 10,
          elevation:  3,
          shadowColor: Platform.OS === 'ios' ? '#d1d1d1' : 'black',
          shadowOffset: { height: 2, width: 0 },
          shadowOpacity: 0.5,
          shadowRadius: 1,
          }}>
          <Include.Text style={{fontSize: 20, fontFamily: Define.constants.fontBold500,color:'#161616'}}>Tài khoản</Include.Text>
          {user.memberInfo?._id ?
            <TouchableOpacity
              style={{ position: 'absolute', right: 0, justifyContent: 'center', alignItems: 'center', paddingHorizontal: 22, paddingTop:Platform.OS === 'ios' ? 0 : StatusBar.currentHeight}}
              onPress={() => {
                popupActions.setRenderContentAndShow(DefaultPopup, {
                  title: 'Đăng xuất',
                  description: 'Bạn có chắc chắn muốn đăng xuất?',
                  buttonTitle2: 'Không',
                  buttonTitle: 'Đăng xuất',
                  onPress2: () => {
                    popupActions.popPopup();
                  },
                  onPress: () => {
                    this.props.dispatch(UserActions_MiddleWare.logout({}))
                    navigation.dispatch(TabActions.jumpTo('Trang chủ'))
                    navigation.navigate('LoginAccountScreen')
                    popupActions.popPopup();
                  }
                })
              }}
            >
              <PenguinLinearIcon name={'login'} size={24} color={'#161616'}/>
            </TouchableOpacity>
          : null}
        </View> */}
          <Image
            style={{
              width: Define.constants.widthScreen + 96,
              height: (Define.constants.widthScreen * 502) / 512,
              position: 'absolute',
              top: -(Define.constants.widthScreen * 110) / 414,
              right: -36,
              alignSelf: 'center',
              justifyContent: 'center',
              alignSelf: 'center',
              zIndex: 11,
            }}
            resizeMode={'contain'}
            source={Define.assets.Images.trongdongdongson}
          />
        <LinearGradient start={{x: 0, y: 1}} end={{x: 0, y: 0}} colors={['#003F9E', '#042F71', '#570204', '#A10606']} style={{flex: 1, paddingTop: Platform.OS === 'ios' ? statusBarHeight - 16 :  statusBarHeight - 32}}>
        <ScrollView style={{ zIndex: 10}}>
          {this.renderUserProfile()}
          <View style={{ borderWidth: 1, borderColor: '#656C75', borderRadius: 12, backgroundColor: '#1C1E21', marginHorizontal: 16, paddingHorizontal: 12, marginTop: 16}}>
            {this.renderMenu()}
          </View>
          {/* <View style={{  paddingTop: 24, }}>
            <Include.Text style={{  color: '#fff', fontSize: 14, fontFamily: Define.constants.fontBold400, fontStyle: 'italic', paddingLeft: 14 }}>Copyright HeyU JSC ©2025</Include.Text>
            <View style={{flexDirection:'row'}}>
              <Include.Text style={{  color: '#fff', paddingLeft: 14, fontSize: 14, fontFamily: Define.constants.fontBold400, fontStyle: 'italic' }}>{`Phiên bản thử nghiệm ${Platform.OS === 'ios' ? 'iOS' : 'Android'} v${DeviceInfo.getVersion()}`}</Include.Text>
              {Define.constants.versionCodePush ?
                <Include.Text style={{  color: '#fff', fontSize: 14, fontFamily: Define.constants.fontBold400, fontStyle: 'italic' }}> - {Define.constants.versionCodePush}</Include.Text> : null}
            </View>
          </View> */}
        </ScrollView>
        </LinearGradient>
      </View>
    )
    return content;
  }

  onFocus = () => {
    // const checkTime = Date.now() - this.lastRefresh >= this.timeRefresh
    // if (checkTime) {
    //   this.onRefresh();
    // }
  }
  handleAppStateChange = (newState) => {
    switch (newState) {
      case 'active':
        this.onFocus()
        break;
      case 'inactive':
        break;
      default:
        break;
    }
  }

  UNSAFE_componentWillMount() {
    super.UNSAFE_componentWillMount();
  }

  componentWillUnmount() {
    super.componentWillUnmount();
    if (this.unsubscribe) {
      this.unsubscribe();
    }
    this.appStateSubscription.remove();
  }
  componentDidMount(){
    super.componentDidMount();
    const {navigation} = this.props
    this.unsubscribe = navigation.addListener('focus', this.onFocus)
    this.appStateSubscription = AppState.addEventListener('change', this.handleAppStateChange);
  }

  shouldComponentUpdate(nextProps) {
    return true;
  }
}


/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  return {
    user: state.User,
    navigator: state.Navigator,
    appSetting: state.AppSetting,
    appState: state.AppState,
    notifications: state.Notifications
  }
}

export default connect(selectActions, undefined, undefined, {withRef: true})(AccountScreen);
