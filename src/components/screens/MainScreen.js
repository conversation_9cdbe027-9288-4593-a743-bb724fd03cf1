var _ = require('lodash');

//LIB
import React from 'react';
import {
  View,
  ScrollView,
  Image,
  FlatList,
  TouchableOpacity,
  Text,
  AppState,
  TouchableWithoutFeedback,
  Platform,
  StatusBar,
  SafeAreaView,
  Linking,
  Animated,
  LayoutAnimation,
  ImageBackground,
  Keyboard,
  KeyboardAvoidingView,
  RefreshControl,
  SectionList,
} from 'react-native';
import {SwiperFlatList} from 'react-native-swiper-flatlist';
import HeyUIcon from '../elements/HeyUIcon/HeyUIcon';
var {Actions} = require('react-native-router-flux');
import {connect} from 'react-redux';
import LinearGradient from 'react-native-linear-gradient';
import LottieView from 'lottie-react-native';
import Svg, {G, Path, Text as SvgText} from 'react-native-svg';
import * as d3Shape from 'd3-shape';

import Tooltip from 'react-native-walkthrough-tooltip';
//action
import ms from 'ms';
import moment from 'moment';
//components
var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
var Util = require('../../Util/Util');
var Include = require('../../Include');
var locationManager = require('../modules/LocationManager');
import RDActions from '../../actions/RDActions';
var UserUtil = require('../../Util/user');
import {TabActions} from '@react-navigation/native';
import {TabView, SceneMap, TabBar} from 'react-native-tab-view';
var {popupActions} = require('../popups/PopupManager');
var {globalVariableManager} = require('../modules/GlobalVariableManager');
var hotNewsManager = require('../modules/HotNewsManager');
import {getStatusBarHeight} from 'react-native-status-bar-height';
import Carousel, {ParallaxImage, Pagination} from 'react-native-snap-carousel';
import {check, request, PERMISSIONS, RESULTS} from 'react-native-permissions';
import {SafeAreaConsumer} from 'react-native-safe-area-context';
//screens
import Screen from './Screen';
import UserActions_MiddleWare from '../../actions/UserActions_MiddleWare';
import NotificationsActions_MiddleWare from '../../actions/NotificationsActions_MiddleWare';

// popups
import DefaultPopup from '../popups/DefaultPopup';
import ServicePopup from '../popups/ServicePopup';
import ChangePassWordPopup from '../popups/ChangePassWordPopup';

// actions
import TypeWriter from 'react-native-typewriter';
import Placeholder from 'rn-placeholder';
import PenguinLinearIcon from '../elements/PenguinLinearIcon/PenguinLinearIcon';
import PenguinBoldIcon from '../elements/PenguinBoldIcon/PenguinBoldIcon';
const async = require('async');
import IOCHBActions_MiddleWare from '../../actions/IOCHBActions_MiddleWare';
//variable
// var styles = StyleSheet.create({
//
// })

//
class MainScreen extends Screen {
  static componentName = 'MainScreen';
  static sceneConfig = {
    ...Screen.sceneConfig,
    hideNavBar: true,
  };
  // static defaultProps = {}
  // static propTypes = {}
  constructor(props) {
    super(props);
    this.state = _.merge(this.state, {
      showBackgroundSttBar: false,
      bottom: 0,
    });
    this.timeRefresh = 5 * 60 * 1000;
    this.onRefresh = this.onRefresh.bind(this);
    this.handleAppStateChange = this.handleAppStateChange.bind(this);
  }

  onRefresh() {
    super.onRefresh();
    var {dispatch} = this.props;
    // this.getWeather();
    this.lastRefresh = Date.now();
  }
  renderProfile() {
    const {widthScreen} = Define.constants;
    var {dispatch, user, navigation, iochb} = this.props;
    const statusBarHeight =
      Platform.OS === 'ios'
        ? getStatusBarHeight(true) + 8
        : StatusBar.currentHeight;
    let username = _.get(user, 'memberInfo.name', '');
    let urlImage = _.get(user, 'memberInfo.avatar', '');
    let code = _.get(user, 'memberInfo.idNumber', '');
    let rankImage = _.get(user, 'memberInfo.rankImage', '');
    return (
      <View
        key={'renderProfile'}
        style={{
          width: Define.constants.widthScreen,
          paddingTop: statusBarHeight,
          paddingHorizontal: 16,
          marginBottom: iochb.areaList.length ? 0 : 16,
          zIndex: 10,
        }}>
        <View
          style={{
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <Image
            source={Define.assets.Images.logocongan}
            style={{
              width: 61,
              height: 61,
            }}
          />
        </View>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <View style={{position: 'absolute', left: 0}}>
            {urlImage ? (
              <Image
                source={{
                  uri: urlImage,
                }}
                style={{
                  width: 48,
                  height: 48,
                  borderRadius: 120,
                }}
              />
            ) : (
              <View
                style={{
                  backgroundColor: '#6fbde8',
                  width: 48,
                  height: 48,
                  borderRadius: 120,
                  alignSelf: 'center',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <Include.Text
                  style={{
                    backgroundColor: 'transparent',
                    fontSize: 24,
                    color: '#fff',
                  }}>
                  {UserUtil.getNameAsAvatar(username)}
                </Include.Text>
              </View>
            )}
          </View>
          <View
            style={{
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <View
              style={{
                backgroundColor: '#fff',
                paddingHorizontal: 8,
                borderRadius: 16,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
                marginTop: 8,
                paddingVertical: 2,
              }}>
              <Text
                allowFontScaling={false}
                style={{
                  fontSize: 16,
                  fontFamily: Define.constants.fontBold600,
                  color: '#D30500',
                  marginRight: 8,
                }}>
                {username}
              </Text>
              {rankImage ? (
                <View
                  style={{
                    width: 36,
                    height: 18,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}>
                  <Image
                    source={{
                      uri: rankImage,
                    }}
                    resizeMode={'contain'}
                    style={{
                      width: 14,
                      height: 36,
                      transform: [{rotate: '-90deg'}],
                    }}
                  />
                </View>
              ) : null}
            </View>
            {code ? (
              <Text
                allowFontScaling={false}
                style={{
                  fontSize: 16,
                  color: '#fff',
                  fontFamily: Define.constants.fontBold500,
                  marginTop: 8,
                }}>
                Số hiệu: {code}
              </Text>
            ) : null}
          </View>
          <TouchableOpacity
            style={{
              backgroundColor: 'transparent',
              position: 'absolute',
              right: 0,
              zIndex: 1,
            }}
            onPress={() => {
              navigation.navigate('NotifyScreen');
            }}>
            <View
              style={{
                width: 24,
                height: 24,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <View
                style={{
                  width: 24,
                  height: 24,
                  justifyContent: 'center',
                  alignItems: 'center',
                  backgroundColor: '#fff',
                  borderRadius: 24,
                }}>
                <PenguinBoldIcon
                  name={'notification'}
                  size={20}
                  color={'#D30500'}
                />
              </View>
              {this.state.count !== 0 ? (
                <View
                  pointerEvents={'none'}
                  style={{
                    position: 'absolute',
                    top: -2,
                    right: 0,
                    width: 8,
                    height: 8,
                    borderRadius: 8,
                    backgroundColor: '#e74c3c',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <Include.Text
                    allowFontScaling={false}
                    numberOfLines={1}
                    style={{
                      color: '#fff',
                      backgroundColor: 'transparent',
                      fontSize: 12,
                      top: -2,
                    }}>
                    {}
                  </Include.Text>
                </View>
              ) : null}
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );
  }
  renderScreenContent() {
    var {dispatch, user, navigation, iochb} = this.props;
    var content = null;
    const statusBarHeight =
      Platform.OS === 'ios' ? getStatusBarHeight(true) : 0;
    content = (
      <View style={{backgroundColor: '#023367', flex: 1}}>
        {Platform.OS === 'ios' ? (
          <View
            style={{
              backgroundColor: this.state.showBackgroundSttBar
                ? '#fff'
                : 'transparent',
              height: statusBarHeight,
              position: 'absolute',
              top: 0,
              bottom: 0,
              right: 0,
              left: 0,
              zIndex: 8,
            }}
          />
        ) : null}
        <StatusBar
          backgroundColor={
            Platform.OS === 'android' && this.state.showBackgroundSttBar
              ? '#fff'
              : 'transparent'
          }
          barStyle={
            this.state.showBackgroundSttBar ? 'dark-content' : 'light-content'
          }
        />
        <ImageBackground
          source={Define.assets.Images.bgrmain}
          style={{
            flex: 1,
            overflow: 'hidden',
          }}>
          <ScrollView
            nestedScrollEnabled={true}
            refreshControl={
              <RefreshControl refreshing={false} onRefresh={this.onRefresh} />
            }
            contentContainerStyle={{flexGrow: 1}}>
            <Image
              style={{
                width: Define.constants.widthScreen + 96,
                height: (Define.constants.widthScreen * 502) / 512,
                position: 'absolute',
                top: -(Define.constants.widthScreen * 110) / 414,
                right: -36,
                alignSelf: 'center',
                justifyContent: 'center',
                alignSelf: 'center',
                zIndex: 1,
              }}
              resizeMode={'contain'}
              source={Define.assets.Images.trongdongdongson}
            />
            {this.renderProfile()}
          </ScrollView>
        </ImageBackground>
      </View>
    );
    return content;
  }

  keyboardWillShow = e => {
    const heightKeyboard = e.endCoordinates.height;
    if (Platform.OS === 'ios') {
      this.setState({
        bottom: heightKeyboard,
        keyboardShow: true,
      });
    } else {
      setTimeout(() => {
        this.setState({
          bottom: heightKeyboard,
          keyboardShow: true,
        });
      }, 100);
    }
  };

  keyboardWillHide = e => {
    if (Platform.OS === 'ios') {
      this.setState({
        bottom: 0,
        keyboardShow: false,
      });
    } else {
      this.setState({
        bottom: 0,
        keyboardShow: false,
      });
    }
  };

  onFocus = () => {
    const checkTime = Date.now() - this.lastRefresh >= this.timeRefresh;
    if (checkTime) {
      this.lastRefresh = Date.now();
    }
  };

  UNSAFE_componentWillMount() {
    super.UNSAFE_componentWillMount();

    if (Platform.OS === 'android') {
      this.keyboardDidShowSubscription = Keyboard.addListener(
        'keyboardDidShow',
        this.keyboardWillShow,
      );
      this.keyboardDidHideSubscription = Keyboard.addListener(
        'keyboardDidHide',
        this.keyboardWillHide,
      );
    } else {
      this.keyboardDidShowSubscription = Keyboard.addListener(
        'keyboardWillShow',
        this.keyboardWillShow,
      );
      this.keyboardDidHideSubscription = Keyboard.addListener(
        'keyboardWillHide',
        this.keyboardWillHide,
      );
    }
  }
  componentWillUnmount() {
    const {dispatch} = this.props;
    if (this.unsubscribe) {
      this.unsubscribe();
    }
    this.appStateSubscription.remove();
    super.componentWillUnmount();
  }
  onBlur = () => {
    this.lastRefresh = Date.now();
  };
  handleAppStateChange = newState => {
    var {dispatch, navigation} = this.props;
    switch (newState) {
      case 'active':
        if (navigation.isFocused()) {
          this.onFocus();
        }
        break;
      case 'background':
        break;
      default:
        break;
    }
  };

  checkActiveChangePassWord() {
    const { navigator, user, dispatch } = this.props;

    if (user && user.memberInfo && user.memberInfo.active === 0) {
      popupActions.setRenderContentAndShow(ChangePassWordPopup, {
        title: 'Đổi mật khẩu',
        buttonTitle2: 'Không',
        buttonTitle: 'Đổi mật khẩu',
        onPress: () => {
          if (user.memberInfo._id) {
            dispatch(UserActions_MiddleWare.get())
            popupActions.popPopup();
          }
        }
      })
    }
  }

  componentDidMount() {
    super.componentDidMount();
    this.checkActiveChangePassWord();
    const {navigation} = this.props;
    this.unsubscribe = navigation.addListener('focus', this.onFocus);
    this.blur = navigation.addListener('blur', this.onBlur);
    this.appStateSubscription = AppState.addEventListener(
      'change',
      this.handleAppStateChange,
    );
  }
  shouldComponentUpdate(nextProps, nextState) {
    return true;
  }
  componentDidUpdate(prevProps, prevState) {
    const addAnimation = prevState.isShowFullChat !== this.state.isShowFullChat;

    if (Platform.OS === 'ios' && addAnimation) {
      // LayoutAnimation.easeInEaseOut();
    }
  }
}

/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  return {
    navigator: state.Navigator,
    iHeyU: state.IHeyU,
    user: state.User,
    appSetting: state.AppSetting,
    iochb: state.IocHB,
    notifications: state.Notifications,
  };
}

export default connect(selectActions, undefined, undefined, {withRef: true})(
  MainScreen,
);
