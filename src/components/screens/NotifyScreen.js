var _ = require('lodash')
const ms = require('ms');
import moment from 'moment';
//LIB
import React from 'react';
import {
  View,
  ScrollView,
  RefreshControl,
  AppState,
  Image,
  TouchableOpacity,
  SafeAreaView,
  ActivityIndicator,
  StatusBar,
  Platform,
  ImageBackground,
  FlatList
} from 'react-native';

import { connect } from 'react-redux';
import { getStatusBarHeight } from 'react-native-status-bar-height';
import Placeholder from 'rn-placeholder';

//components
var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
var Util = require('../../Util/Util');
var Include = require('../../Include');
const PushNotiManager = require('../modules/PushNotiManager');
var { globalVariableManager } = require('../modules/GlobalVariableManager');
import {Entypo as Icon} from '@react-native-vector-icons/entypo'
import LinearGradient from 'react-native-linear-gradient';
import PenguinLinearIcon from '../elements/PenguinLinearIcon/PenguinLinearIcon';

// popups
import DefaultPopup from '../popups/DefaultPopup';

import NotificationsActions_MiddleWare from '../../actions/NotificationsActions_MiddleWare';

//screens
import Screen from './Screen';

class NotifyScreen extends Screen {
  static componentName = 'NotifyScreen';
  static sceneConfig = { ...Screen.sceneConfig };

  constructor(props) {
    super(props);
    this.state = {
      ...this.state,
      list: [],
      count: 0,
      isLoading: false,
      loadingMore: false,
      page: 1,
      cantGetMore: false,
      limit: 10
    };
    this.timeRefresh = 5 * 60 * 1000;
    this.lastRefresh = 0;

    this.onRefresh = this.onRefresh.bind(this);
    this.onGetMore = this.onGetMore.bind(this);
    this.handleAppStateChange = this.handleAppStateChange.bind(this);
  }

  normalize = (noti) => ({
    _id: noti._id,
    category: _.get(noti, 'category', []),
    notifiedAt: noti.createdAt,
    title: _.get(noti, 'title', ''),
    description: _.get(noti, 'message', ''),
    icon: _.get(noti, 'data.image', ''),
    link: _.get(noti, 'data.link', ''),
    seen: _.get(noti, 'hasSeen', false),
    extras: _.get(noti, 'data.extras', {}),
    type: _.get(noti, 'type', ''),
    actionUrl: _.get(noti, 'data.actionUrl', '')
  });

  fetchNotifyList(page = 1) {
    const { dispatch } = this.props;
    const { limit } = this.state;

    if (page === 1) this.setState({ isLoading: true });
    else this.setState({ loadingMore: true });

    return dispatch(NotificationsActions_MiddleWare.notify({ limit, page }))
      .then((result) => {
        const rows = result?.res?.data || [];
        const mapped = rows.map(this.normalize);

        this.setState((prev) => {
          let nextList;
          if (page === 1) nextList = mapped;
          else nextList = _.unionBy(prev.list, mapped, '_id');

          return {
            list: nextList,
            page,
            cantGetMore: mapped.length < limit,
            isLoading: false,
            loadingMore: false
          };
        });
        this.lastRefresh = Date.now();
      })
      .catch(() => {
        this.setState({ isLoading: false, loadingMore: false });
      });
  }

  fetchUnreadCount() {
    const { dispatch } = this.props;
    return dispatch(NotificationsActions_MiddleWare.countNotify())
      .then((result) => {
        const c = result?.res?.data ?? 0;
        this.setState({ count: c });
      })
      .catch(() => {});
  }

  onRefresh() {
    super.onRefresh();
    this.setState({ page: 1, cantGetMore: false }, () => {
      Promise.all([this.fetchNotifyList(1), this.fetchUnreadCount()]).catch(()=>{});
    });
  }

  onGetMore() {
    super.onGetMore();
    const { cantGetMore, isLoading, loadingMore, page } = this.state;
    if (cantGetMore || isLoading || loadingMore) return;
    this.fetchNotifyList(page + 1);
  }

  handleAppStateChange(newState) {
    if (newState === 'active') this.onFocus();
  }

  onFocus = () => {
    const checkTime = Date.now() - (this.lastRefresh || 0) >= this.timeRefresh;
    if (checkTime) this.onRefresh();
  }

  handleItemPress = (item) => {
    const { dispatch } = this.props;

    this.setState((prev) => {
      const idx = prev.list.findIndex(x => x._id === item._id);
      if (idx === -1) return null;

      const wasSeen = prev.list[idx].seen;
      const nextList = [...prev.list];
      nextList[idx] = { ...nextList[idx], seen: true };

      return {
        list: nextList,
        count: wasSeen ? prev.count : Math.max(0, prev.count - 1)
      };
    });

    dispatch(NotificationsActions_MiddleWare.seenNotify({ _id: item._id }))
      .catch(() => {
      });

    if (item.link) {
      const objNavigator = { ...item.extras, createdAt: item.notifiedAt};
      globalVariableManager.navigatorManager.handleNavigator(item.link, objNavigator);
    } else if (item.actionUrl) {
      globalVariableManager.navigatorManager.handleNavigator('WebviewScreen', {
        source: item.actionUrl
      });
    }
  }

  renderNavBar() {
    const { navigation } = this.props;
    const statusBarHeight =
      Platform.OS === 'ios' ? getStatusBarHeight(true) : StatusBar.currentHeight;
    return (
      <View style={{
        height: Platform.OS === 'ios'
          ? Define.constants.navBarHeight + statusBarHeight + 8
          : Define.constants.navBarHeight,
        width: '100%',
        flexDirection: 'row',
        paddingTop: statusBarHeight + 8,
        alignItems: 'center',
      }}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={{ marginLeft: 16, width: Define.constants.navBarHeight }}>
          <PenguinLinearIcon name={'arrow-left'} color={'#fff'} size={24} />
        </TouchableOpacity>
        <View style={{ marginLeft: -22, flex: 1 }} pointerEvents={'none'}>
          <Include.Text
            numberOfLines={1}
            style={{
              color: '#fff',
              fontSize: 18,
              fontFamily: Define.constants.fontBold600,
              textAlign: 'center',
            }}>
            Thông báo
          </Include.Text>
        </View>
        <View style={{ width: Define.constants.navBarHeight }} />
      </View>
    );
  }

  renderNotifyList() {
    const { isLoading, list, count, loadingMore, cantGetMore } = this.state;
    const statusBarHeight = Platform.OS === 'ios' ? getStatusBarHeight(true) : 8;

    return (
      <View style={{ flex: 1 }}>
        {list.length !== 0 && count !== 0 ? (
          <View style={{ backgroundColor: '#DFE6F3', flexDirection: 'row', justifyContent: 'space-between', paddingVertical: 6, paddingHorizontal: 16, alignItems: 'center' }}>
            <Include.Text style={{ fontSize: 14, fontWeight: '400', color: '#1E5D97' }}>
              Bạn còn {count} thông báo chưa xem
            </Include.Text>
          </View>
        ) : null}

        <FlatList
          data={list}
          keyExtractor={(item, idx) => item._id || String(idx)}
          style={ { paddingTop: 8, flex: 1 }}
          refreshControl={
            <RefreshControl refreshing={isLoading} onRefresh={this.onRefresh} />
          }
          contentContainerStyle={{paddingBottom: 100}}
          onEndReached={this.onGetMore}
          onEndReachedThreshold={0.2}
          removeClippedSubviews
          initialNumToRender={10}
          windowSize={7}
          ListEmptyComponent={
            !isLoading ? (
              <View style={{ height: Define.constants.heightScreen - (Platform.OS === 'ios' ? Define.constants.navBarHeight + statusBarHeight : Define.constants.navBarHeight) - 102, justifyContent: 'center', alignItems: 'center' }}>
                <Image
                  source={Define.assets.Images.paperMessage}
                  style={{ width: 215, height: 176, resizeMode: 'contain' }}
                />
                <Include.Text style={{ color: '#fff', justifyContent: 'center', textAlign: 'center', marginTop: 20, fontSize: 16, fontFamily: Define.constants.fontBold500 }}>
                  Chưa có thông báo nào!
                </Include.Text>
              </View>
            ) : null
          }
          renderItem={({ item }) => (
            <TouchableOpacity
              key={item._id}
              style={{
                width: Define.constants.widthScreen - 32,
                backgroundColor: !item.seen ? '#2E3236' : '#1C1E21',
                borderRadius: 12,
                alignItems: 'center',
                borderWidth: 1,
                borderColor: '#4A4F55',
                marginHorizontal: 16,
                padding: 12,
                marginTop: 12
              }}
              onPress={() => this.handleItemPress(item)}
            >
              {!item.seen ? (
                <View style={{ width: 50, position: 'absolute', right: -14, top: -26, zIndex: 9 }}>
                  <Icon name='dot-single' style={{ fontSize: 60, color: '#cf2828ff' }} />
                </View>
              ) : null}
              <View style={{ backgroundColor: 'transparent', flexDirection: 'row', alignItems: 'center' }}>
                <Image style={{ width: 24, height: 24, marginRight: 12 }} source={{ uri: 'https://media.icongdanso.vn/uploads/mobiles/2025-08-21-iconnotify.png' }} />
                <Include.Text numberOfLines={3} style={{ fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold400, flex: 1 }}>
                  {item.title}
                </Include.Text>
              </View>
              <Include.Text numberOfLines={1} style={{ fontSize: 14, color: '#fff', fontStyle: 'italic', alignSelf: 'flex-end' }}>
              {moment(item.notifiedAt).format('HH:mm - DD/MM/YYYY')}
              </Include.Text>
            </TouchableOpacity>
          )}
          ListFooterComponent={
            loadingMore && !cantGetMore
              ? <ActivityIndicator size={'large'} color={'#3498db'} style={{ padding: 6 }} />
              : null
          }
        />
      </View>
    );
  }

  renderScreenContent() {
    return (
      <View style={{ flex: 1 }}>
        <StatusBar backgroundColor={Platform.OS === 'android' ? null : '#fff'} barStyle={'light-content'} />
        <LinearGradient start={{x: 0, y: 0.3}} end={{x: 0, y: 0}} colors={['#003F9E', '#042F71', '#390404ff']} style={{flex: 1}}>
          {this.renderNavBar()}
          {this.renderNotifyList()}
        </LinearGradient>
        <SafeAreaView style={{ flex: 0, backgroundColor: '#023d96' }} />
      </View>
    );
  }

  UNSAFE_componentWillMount() {
    super.UNSAFE_componentWillMount();
    const { navigation } = this.props;
    this.unsubscribe = navigation.addListener('focus', this.onFocus);
    this.appStateSubscription = AppState.addEventListener('change', this.handleAppStateChange);
  }

  componentWillUnmount() {
    super.componentWillUnmount();
    this.appStateSubscription?.remove?.();
    this.unsubscribe && this.unsubscribe();
  }

  componentDidMount() {
    super.componentDidMount();
    PushNotiManager.handlePopup();
  }
}

/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  return {
    navigator: state.Navigator,
    notify: state.Notify,
    notifications: state.Notifications
  }
}

export default connect(selectActions, undefined, undefined, { withRef: true })(NotifyScreen);
