
var _ = require('lodash')

//LIB
import React from 'react';
import {
  View,
  ScrollView,
  RefreshControl,
  StyleSheet,
  Platform,
  Image,
  TouchableOpacity,
  TextInput,
  Text,
  Keyboard,
  LayoutAnimation,
  SafeAreaView,
  ImageBackground,
  StatusBar
} from 'react-native';

var { Actions } = require('react-native-router-flux');
import { connect } from 'react-redux';
import { Hoshi } from 'react-native-textinput-effects';
//action
var NotifyUtil = require('../../Util/notify')

//components
var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
var Util = require('../../Util/Util');
var UserUtil = require('../../Util/user');
var Include = require('../../Include');

var { popupActions } = require('../popups/PopupManager');
var { globalVariableManager } = require('../modules/GlobalVariableManager');

var ButtonWrap = require('../elements/ButtonWrap');
import HeyUIcon from '../elements/HeyUIcon/HeyUIcon';


//screens
import Screen from './Screen'

// popups
import DefaultPopup from '../popups/DefaultPopup';
// import FadeDownDefaultPopup from '../popups/FadeDownDefaultPopup'
import PenguinLinearIcon from '../elements/PenguinLinearIcon/PenguinLinearIcon';
import PenguinBoldIcon from '../elements/PenguinBoldIcon/PenguinBoldIcon';
// actions
import UserActions_MiddleWare from '../../actions/UserActions_MiddleWare'
import AppStateActions_MiddleWare from '../../actions/AppStateActions_MiddleWare'
import { Icon, Button, Thumbnail } from 'native-base';
import LinearGradient from 'react-native-linear-gradient';
import * as Animatable from 'react-native-animatable';
import StarRating from 'react-native-star-rating';
import { check } from 'react-native-permissions';


//variable

//

class ChangePassAccountScreen extends Screen {
  static componentName = 'ChangePassAccountScreen'
  static sceneConfig = {
    ...Screen.sceneConfig,
    hideNavBar: true
  }
  // static defaultProps = {}
  // static propTypes = {}
  constructor(props) {
    super(props);
    let { user } = this.props;
    this.state = _.merge(this.state, {
      keyboardShow: false,
      bottom: 0,
      newPasswordValidate: false,
      confirmNewPasswordValidate: false,
      passwordValidate: false,
      checkPassword: false,
      eyePassword: true,
      eyeNewPassword: true,
      eyeConfirmPassword: true
    })
    this.password = '';
    this.newPassword = '';
    this.confirmNewPassword = '';
    this.keyboardWillShow = this.keyboardWillShow.bind(this);
    this.keyboardWillHide = this.keyboardWillHide.bind(this);
  }


  onRefresh() {
    super.onRefresh();
    var { dispatch } = this.props;
  }

  onGetMore() {
    super.onGetMore();
    var { dispatch } = this.props;
  }

  checkNewPassword = () => {
    let objUpdate = {
      password : this.password,
      newPassword : this.newPassword,
      rePassword: this.confirmNewPassword
    }
    if(!this.password && !this.newPassword && !this.confirmNewPassword){
      this._password && this._password.focus();
    }else if (!this.password && this.newPassword && this.confirmNewPassword) {
      this._password && this._password.focus();
    } else if (this.password && !this.newPassword && this.confirmNewPassword) {
      this._newPassword && this._newPassword.focus();
    } else if (this.password && !this.newPassword && !this.confirmNewPassword) {
      this._newPassword && this._newPassword.focus();
    } else if (this.password && !this.confirmNewPassword.includes(objUpdate.newPassword)){
      this._newConfirmPassword && this._newConfirmPassword.focus();
      this.setState({
        checkPassword: true
      })
    } else if (objUpdate.password && this.confirmNewPassword.includes(objUpdate.newPassword)) {
      this.setState({
        checkPassword: false
      },()=>{
        this.handlePassWordChange(objUpdate)
      })
    }
  }


  handlePassWordChange = (objUpdate) => {
    var {dispatch,navigation} = this.props;
    dispatch(UserActions_MiddleWare.changePassword(objUpdate))
    .then((res)=>{
      NotifyUtil.pushAlertTopNotify({
        type: 'success',
        content: 'Thay đổi mật khẩu thành công',
        timeClose: 3000,
      })
      Keyboard.dismiss()
      navigation.goBack();
    })
    .catch(err => {
      NotifyUtil.pushAlertTopNotify({
        type: 'error',
        content: 'Thay đổi mật khẩu không thành công',
        timeClose: 3000,
      })
    })
  }

  keyboardWillShow(e) {
    const heightKeyboard = e.endCoordinates.height;
    if (Platform.OS === 'ios') {
      this.setState({
        bottom: heightKeyboard,
        keyboardShow: true
      });
    } else {
      this.setState({
        keyboardShow: true
      })
    }
  }

  keyboardWillHide(e) {
    if (Platform.OS === 'ios') {
      if (this.scrollProfile) {
        this.scrollProfile.scrollTo({ x: 0, y: 0, animated: false })
      }
      this.setState({
        bottom: 0,
        keyboardShow: false
      });
    } else {
      this.setState({
        keyboardShow: false
      })
    }
  }

  renderNavbar() {
    var { dispatch, navigation } = this.props;
    return (
      <View
        style={{
          width: Define.constants.widthScreen,
          zIndex: 2,
          shadowColor: Platform.OS === 'ios' ? '#d1d1d1' : 'black',
          shadowOffset: { height: 1, width: 0 },
          shadowOpacity: 0.5,
          shadowRadius: 1,
          elevation: 3,
        }}>
        <View style={{ flexDirection: 'row', justifyContent: 'center', alignItems: 'center' }}>

          <TouchableOpacity
            style={{ position: 'absolute', left: 0,  paddingHorizontal: 16, paddingTop: Platform.OS === 'ios' ? 0 : 10 }}
            onPress={() => {
              navigation.goBack();
            }}>
            <HeyUIcon name={'fi-sr-arrow-left'} size={24} color={'#fff'} />
          </TouchableOpacity>

          <View style={Themes.current.screen.titleWrapNavBarCenter} pointerEvents={'none'}>
            <Include.Text style={{ fontSize: 18, fontFamily: Define.constants.fontBold500, color: '#fff' }}>Đổi mật khẩu</Include.Text>
          </View>

        </View>
      </View>
    )
  }

  renderScreenContent() {
    var { navigation, user } = this.props;
    var content = null;

    content = (
      <View style={[Themes.current.screen.bodyView, this.props.bodyStyle, { position: 'absolute', paddingTop: 0, top: 0, bottom: this.state.bottom }]}>
          {Platform.OS === 'ios' ?
            <StatusBar
              backgroundColor={'#fff'}
              barStyle={'light-content'}
            /> : null}
          <LinearGradient start={{x: 0, y: 0.3}} end={{x: 0, y: 0}} colors={['#003F9E', '#042F71', '#390404ff']} style={{flex: 1}}>
          <SafeAreaView style={{ flex: 1, backgroundColor: 'transparent' }}>
            {this.renderNavbar()}
            <ScrollView
              showsVerticalScrollIndicator={false}
              style={{ flex: 1 }}
              ref={(ref) => this.scrollProfile = ref}
              keyboardShouldPersistTaps="handled"
            // refreshing={false}
            // onRefresh={this.onRefresh}
            // onGetMore={this.onGetMore}
            >
              <View style={{ width: '100%', backgroundColor: 'transparent' }}>
              </View>
              <View
                onLayout={e => this.setState({ heightProfile: e.nativeEvent.layout.height })}
                style={{ justifyContent: 'center', padding: 16, zIndex: 5 }}>
                <View>
                  <Include.Text style={{ fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold400, marginBottom: 12 }}>
                    Mật khẩu hiện tại <Include.Text style={{ color: '#E93940' }}>*</Include.Text>
                  </Include.Text>
                  <View
                    style={{
                      flexDirection: 'row',
                      backgroundColor: '#1C1E21',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      borderWidth: 1,
                      borderColor: '#4A4F55',
                      padding: 12, borderRadius: 12,
                    }}>
                    <TextInput
                      placeholder={'Nhập mật khẩu hiện tại'}
                      placeholderTextColor={'#CCCFD3'}
                      textContentType={'password'}
                      secureTextEntry={this.state.eyePassword}
                      returnKeyType="next"
                      defaultValue={this.password}
                      ref={(ref) => this._password = ref}
                      style={{ backgroundColor: '#1C1E21',  color: '#fff', fontSize: 16, flex:1, fontFamily: Define.constants.fontBold500,}}
                      blurOnSubmit={false}
                      autoFocus={false}
                      onSubmitEditing={() => {
                        this._newPassword && this._newPassword.focus();
                      }}
                      onChangeText={(text) => {
                        this.password = text
                        this.forceUpdate();
                        clearTimeout(this.timeoutPassword);
                        this.timeoutPassword = setTimeout(() => {
                          if (this.password.length > 5) {
                            this.setState({ passwordValidate: true });
                          } else {
                            this.setState({ passwordValidate: false });
                          }
                        }, 500);
                      }}
                    />
                    <View style={{ flexDirection: 'row',}}>
                      {this.password ?
                        <TouchableOpacity
                          onPress={() => {
                            this.password = ''
                            this.forceUpdate()
                          }}
                          style={{ justifyContent: 'flex-end', marginRight: 16 }}>
                          <PenguinBoldIcon name='close-circle' size={20} color={'#fff'} />
                        </TouchableOpacity> : null}
                      <TouchableOpacity
                        onPress={() => {
                          this.setState({
                            eyePassword: !this.state.eyePassword
                          })
                        }}
                        style={{ justifyContent: 'flex-end' }}>
                        {!this.state.eyePassword ?
                          <PenguinBoldIcon name='eye' size={20} color={'#fff'} />
                          : <PenguinBoldIcon name='eye-slash' size={20} color={'#fff'} />}
                      </TouchableOpacity>
                    </View>
                  </View>
                  </View>
                  <View style={{ marginTop: 20 }}>
                    <Include.Text style={{ fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold400, marginBottom: 12 }}>
                      Mật khẩu mới <Include.Text style={{ color: '#E93940' }}>*</Include.Text>
                    </Include.Text>
                    <View
                      style={{
                        flexDirection: 'row',
                        backgroundColor: '#1C1E21',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        borderRadius: 12,
                        borderWidth: 1,
                        borderColor: '#4A4F55',
                        padding: 12,
                      }}>
                      <TextInput
                        placeholder={'Nhập mật khẩu mới'}
                        placeholderTextColor={'#CCCFD3'}
                        secureTextEntry={this.state.eyeNewPassword}
                        textContentType={'password'}
                        returnKeyType="next"
                        defaultValue={this.newPassword}
                        ref={(ref) => this._newPassword = ref}
                        style={{ backgroundColor: '#1C1E21', color: '#fff', fontSize: 16, flex:1, fontFamily: Define.constants.fontBold500}}
                        blurOnSubmit={false}
                        onSubmitEditing={() => {
                          this._newConfirmPassword && this._newConfirmPassword.focus();
                        }}
                        onChangeText={(text) => {
                          this.newPassword = text
                          this.forceUpdate();
                          clearTimeout(this.timeoutPassword);
                          this.timeoutPassword = setTimeout(() => {
                            if (this.newPassword.length > 5) {
                              this.setState({ newPasswordValidate: true });
                            } else {
                              this.setState({ newPasswordValidate: false });
                            }
                          }, 500);
                        }}
                      />
                      <View style={{ flexDirection: 'row',}}>
                        {this.newPassword ?
                          <TouchableOpacity
                            onPress={() => {
                              this.newPassword = ''
                              this.forceUpdate()
                            }}
                            style={{ justifyContent: 'flex-end', marginRight: 16 }}>
                            <PenguinBoldIcon name='close-circle' size={20} color={'#fff'} />
                          </TouchableOpacity> : null}
                        <TouchableOpacity
                          onPress={() => {
                            this.setState({
                              eyeNewPassword: !this.state.eyeNewPassword
                            })
                          }}
                          style={{ justifyContent: 'flex-end' }}>
                          {!this.state.eyeNewPassword ?
                            <PenguinBoldIcon name='eye' size={20} color={'#fff'} />
                            : <PenguinBoldIcon name='eye-slash' size={20} color={'#fff'} />}
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                  <View style={{ marginTop: 20 }}>
                    <Include.Text style={{ fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold400, marginBottom: 12 }}>
                      Xác nhận mật khẩu mới <Include.Text style={{ color: '#E93940' }}>*</Include.Text>
                    </Include.Text>
                    <View
                    style={{
                      flexDirection: 'row',
                      backgroundColor: '#1C1E21',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      borderRadius: 12,
                      borderWidth: 1,
                      borderColor: this.state.checkPassword ? '#E93940' : '#4A4F55',
                      padding: 12,
                    }}>
                    <TextInput
                      placeholder={'Xác nhận mật khẩu mới'}
                      placeholderTextColor={'#CCCFD3'}
                      textContentType={'password'}
                      secureTextEntry={this.state.eyeConfirmPassword}
                      returnKeyType={'done'}
                      defaultValue={this.confirmNewPassword}
                      ref={(ref) => this._newConfirmPassword = ref}
                      style={{ backgroundColor: '#1C1E21', color: '#fff', fontSize: 16, flex:1, fontFamily: Define.constants.fontBold500, }}
                      blurOnSubmit={false}
                      onSubmitEditing={() => {
                        this.checkNewPassword()
                      }}
                      onChangeText={(text) => {
                        this.confirmNewPassword = text
                        this.forceUpdate();
                        clearTimeout(this.timeoutPassword);
                        this.timeoutPassword = setTimeout(() => {
                          if (this.confirmNewPassword.length > 5) {
                            this.setState({ confirmNewPasswordValidate: true });
                          } else {
                            this.setState({ confirmNewPasswordValidate: false, checkPassword: false });
                          }
                        }, 500);
                      }}
                    />
                    <View style={{ flexDirection: 'row'}}>
                      {this.confirmNewPassword ?
                        <TouchableOpacity
                          onPress={() => {
                            this.confirmNewPassword = ''
                            this.forceUpdate()
                          }}
                          style={{ justifyContent: 'flex-end', marginRight: 16 }}>
                          <PenguinBoldIcon name='close-circle' size={20} color={'#fff'} />
                        </TouchableOpacity> : null}
                      <TouchableOpacity
                        onPress={() => {
                          this.setState({
                            eyeConfirmPassword: !this.state.eyeConfirmPassword
                          })
                        }}
                        style={{ justifyContent: 'flex-end' }}>
                        {!this.state.eyeConfirmPassword ?
                          <PenguinBoldIcon name='eye' size={20} color={'#fff'} />
                          : <PenguinBoldIcon name='eye-slash' size={20} color={'#fff'} />}
                      </TouchableOpacity>
                    </View>
                  </View>
                    {this.state.checkPassword ?
                      <Include.Text style={{ fontSize: 14, color: '#E93940', fontFamily: Define.constants.fontBold500, paddingTop: 6, paddingHorizontal: 12 }}>
                        Mật khẩu xác nhận đang không khớp với mật khẩu mới
                      </Include.Text> : null}
                  </View>
                  <Include.Text style={{ fontSize: 16, color: '#fff', fontFamily: Define.constants.fontBold400, paddingTop: 16, paddingHorizontal: 12, fontStyle: 'italic' }}>
                    Sử dụng 6 ký tự trở lên, bao gồm kết hợp chữ cái, số và ký hiệu. Không được chứa tên hoặc tên người dùng của bạn.
                  </Include.Text>
                </View>
                {/* <TouchableOpacity
                  onPress={() => {
                    navigation.navigate('LoginAccountScreen', {
                      FORGOTPW: true
                    })
                }}>
                <Include.Text style={{ fontSize: 16, color: '#007CFE', fontFamily: Define.constants.fontBold600, padding:16}}>
                      Quên mật khẩu
                    </Include.Text>
                </TouchableOpacity> */}
                {/* <View style={{ zIndex: 0 }}>
                  <Image
                    style={{
                      width: 165,
                      height: 175,
                      position: 'absolute',
                      left: 0,
                      top: 30,
                      zIndex: 1
                    }}
                    resizeMode={'stretch'}
                    source={{ uri: 'https://media.heyu.asia/uploads/mobiles/2025-01-08-hoaphuongdonavbar.png' }}
                  />
                  <Image
                    style={{
                      width: 165,
                      height: 175,
                      position: 'absolute',
                      right: 0,
                      top: 30,
                      zIndex: 1
                    }}
                    resizeMode={'stretch'}
                    source={{ uri: 'https://media.heyu.asia/uploads/mobiles/2025-01-08-hoaphuongdonavbar2.png' }}
                  />
                </View> */}
            </ScrollView>
            <View style={{
              padding: 16,
              backgroundColor: '#021E38',
              shadowColor: Platform.OS === 'ios' ? '#929394' : 'black',
              shadowOffset: { height: 2, width: 0 },
              shadowOpacity: 1,
              shadowRadius: 2,
            }}>
              <TouchableOpacity
                style={{ padding: 12, borderRadius: 8, alignItems: 'center', backgroundColor: this.password && this.newPassword && this.confirmNewPassword ? '#007CFE' : '#F3F3F3', width: Define.constants.widthScreen - 32, justifyContent: 'center', flexDirection: 'row', marginBottom: 16 }}
                onPress={() => {
                  this.checkNewPassword()
                }}>
                <Include.Text style={{ backgroundColor: 'transparent', color: this.password && this.newPassword && this.confirmNewPassword ? '#fff' : '#A1A7AE', fontSize: 18, fontFamily: Define.constants.fontBold600, }}>Cập nhật mật khẩu</Include.Text>
              </TouchableOpacity>
            </View>
          </SafeAreaView>
        </LinearGradient>
        <SafeAreaView style={{ flex: 0, backgroundColor: '#021E38' }} />
      </View>
    )

    return content;
  }
  UNSAFE_componentWillMount() {
    super.UNSAFE_componentWillMount()
    if (Platform.OS === 'android') {
      this.keyboardDidShowSubscription = Keyboard.addListener('keyboardDidShow', this.keyboardWillShow)
      this.keyboardDidHideSubscription = Keyboard.addListener('keyboardDidHide', this.keyboardWillHide)
    } else {
      this.keyboardDidShowSubscription = Keyboard.addListener('keyboardWillShow', this.keyboardWillShow)
      this.keyboardDidHideSubscription = Keyboard.addListener('keyboardWillHide', this.keyboardWillHide)
    }
  }
  componentDidMount() {
    super.componentDidMount();
  }
  componentDidUpdate() {
    if (Platform.OS === 'ios') {
      LayoutAnimation.easeInEaseOut();
    }
  }
}

/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  return {
    user: state.User,
    navigator: state.Navigator,
    appState: state.AppState,
    appSetting: state.AppSetting
  }
}

export default connect(selectActions, undefined, undefined, { withRef: true })(ChangePassAccountScreen);
