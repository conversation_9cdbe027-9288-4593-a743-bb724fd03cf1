
var _ = require('lodash')

//LIB
import React  from 'react';
import {
  View,
  ScrollView,
  RefreshControl,
  ActivityIndicator
} from 'react-native';

var {Actions} = require('react-native-router-flux');
import {Ionicons as Icon} from '@react-native-vector-icons/ionicons';
import { connect } from 'react-redux';
import { WebView } from 'react-native-webview';
//action

//components
var Define = require('../../Define');
var Debug = require('../../Util/Debug');
var Themes = require('../../Themes');
var Util = require('../../Util/Util');
var Include = require('../../Include');

var {popupActions} = require('../popups/PopupManager');
var {globalVariableManager}= require('../modules/GlobalVariableManager');

var ButtonWrap = require('../elements/ButtonWrap');

//screens
import Screen from './Screen'

// popups
import DefaultPopup from '../popups/DefaultPopup';

// actions

//variable

// var styles = StyleSheet.create({
//
// })

//

class TutorialScreen extends Screen{
  static componentName = 'TutorialScreen'
  static sceneConfig ={
    ...Screen.sceneConfig
  }
  // static defaultProps = {}
  // static propTypes = {}
  constructor(props){
    super(props);
    this.state = _.merge(this.state,
    {
      isLoadDone: false
    })
  }

  static renderBackButton(scene){
    if (scene.noMenuButton) {
      return (
        <ButtonWrap onPress={()=>{
            globalVariableManager.rootView.drawSideMenu(false);Actions.pop();
          }}>
          <View style={Themes.current.screen.leftButtonWrapNavBar}>
              <Icon name='arrow-back' style={{fontSize: 32, lineHeight: 36, color: '#fff',marginRight:6}} />
          </View>
        </ButtonWrap>
      )
    }else{
      return (
        <ButtonWrap onPress={()=>{globalVariableManager.rootView.drawSideMenu(true)}}>
          <View style={Themes.current.screen.leftButtonWrapNavBar}>
              <Icon name='menu' style={{fontSize: 32, lineHeight: 36, color: '#fff',marginRight:6}} />
          </View>
        </ButtonWrap>
      )
    }
  }

  // static renderLeftButton(scene){
  //   return (
  //     <View style={Themes.current.screen.leftButtonWrapNavBar}>
  //       <Include.Text>LeftButton</Include.Text>
  //     </View>
  //   )
  // }
  static renderRightButton(scene){
    return null;
  }
  static renderTitle(scene){
    return(
      <View style={Themes.current.screen.titleWrapNavBarCenter}>
        <Include.Text style={Themes.current.text.navBartitle}>HƯỚNG DẪN SỬ DỤNG</Include.Text>
      </View>
    )
  }

  onRefresh(){
    super.onRefresh();
    var {dispatch} = this.props;
  }

  onGetMore(){
    super.onGetMore();
    var {dispatch} = this.props;
  }
  renderScreenContent(){
    var {dispatch, appSetting} = this.props;
    var content = null;
    content =(
      <View
        style={{flex: 1}}>
        <WebView
          javaScriptEnabled ={true}
          source={{uri: appSetting.mode === 'shipper' ? Define.constants.tutorial : Define.constants.tutorialForShop}}
          style={{marginTop: 44}}
          onLoadEnd={() => {
            this.setState({
              isLoadDone: true
            })
          }}
        />
        {!this.state.isLoadDone ?
          <View
            style={{position: 'absolute', top: 0, left: 0, bottom: 0, right: 0, alignItems: 'center', justifyContent: 'center'}}>
            <ActivityIndicator size={'large'} />
            <Include.Text style={{backgroundColor: 'transparent'}}>Loading...</Include.Text>
          </View>
        : null}
      </View>
    )
    return content;
  }
  componentDidMount(){
    super.componentDidMount();
  }
}


/**
 * [selectActions description]
 * @param  {[type]} state [description]
 * @return {[type]}       [description]
 */
function selectActions(state) {
  return {
    navigator: state.Navigator,
    appSetting: state.AppSetting
  }
}

export default connect(selectActions, undefined, undefined, {withRef: true})(TutorialScreen);
