var _ = require('lodash')

import React from 'react';
import {
    StyleSheet,
    Text,
    View,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    Fla
} from 'react-native';
import * as Animatable from 'react-native-animatable';
import { connect } from 'react-redux';
var Define = require('../../../Define');


class TabBar extends React.PureComponent {

    constructor(props) {
        super(props);
        this.state = _.merge(this.state, {
            tabFocus: this.props.tabFocus || 0
        })
    }

    render() {
        return (
            <View
                style={{
                    width: Define.constants.widthScreen,
                    zIndex: 2,
                }}>
                <View style={[styles.tabs, this.props.style]}>
                    <ScrollView horizontal={true} showsHorizontalScrollIndicator={false} contentContainerStyle={{}}>
                        <View style={{  flexDirection: 'row', backgroundColor: '#fff', justifyContent: 'center', alignItems: 'center', height: 48 }}>
                            {this.props.tabs.map((tab, i) => {
                                let borderColor;
                                if (this.state.tabFocus === i) {
                                    borderColor = '#0473CD'
                                } else {
                                    borderColor = '#E2E2E2'
                                }

                                return (
                                    <TouchableOpacity
                                        activeOpacity={0.6}
                                        onPress={() => {
                                            this.setState({ tabFocus: i });
                                            this.props.goToPage(i);
                                        }}
                                        style={{
                                            width: Define.constants.widthScreen / 4 + 50, borderBottomWidth: 4, borderColor: borderColor, backgroundColor: 'transparent', height: 48, justifyContent: 'center'
                                        }}
                                    >
                                        <Text allowFontScaling={false} style={{ color: '#0473CD', backgroundColor: 'transparent', fontWeight: '500', fontSize: 14, alignSelf: 'center' }} numberOfLines={1}>{tab}</Text>
                                    </TouchableOpacity>
                                )
                            })}
                        </View>
                    </ScrollView>
                </View>
            </View>
        );
    }

    UNSAFE_componentWillReceiveProps(nextProps) {
        if (this.state.tabFocus !== nextProps.tabFocus) {
            this.setState({ tabFocus: nextProps.tabFocus });
        }
    }
}

const styles = StyleSheet.create({
    tabs: {
        backgroundColor: 'transparent'
    }
});
function selectActions(state) {
    return {
        appSetting: state.AppSetting,
        notifications: state.Notifications
    }
}

export default connect(selectActions, undefined, undefined, { withRef: true })(TabBar);
