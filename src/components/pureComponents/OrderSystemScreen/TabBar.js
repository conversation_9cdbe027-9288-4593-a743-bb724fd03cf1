var _ = require('lodash')

import React from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ScrollView,
  StatusBar
} from 'react-native';
import * as Animatable from 'react-native-animatable';
var Themes = require('../../../Themes');
import { connect } from 'react-redux';
var Define = require('../../../Define');
var {Actions} = require('react-native-router-flux');
import LinearGradient from 'react-native-linear-gradient';


const ORDER_STATUS = {
  LOOKING_SHIPPER: 0,
  FOUND_SHIPPER: 1,
  SHIPPING: 2,
  DONE: 3,
  CAN_NOT_FIND_SHIPPER: 4,
  REJECT: 5,
  DO_NOT_GET_GOODS: 6,
  TURNING_ORDER: 7,
  TURNING_ORDER_DONE: 8
}

class TabBar extends React.PureComponent {

  constructor(props) {
    super(props);
    this.state = _.merge(this.state, {
      tabFocus: this.props.tabFocus || 0
    })
  }

  render() {
    let title = '';

    const status = this.props.queryFilter.status;
    if(!status && !this.props.queryFilter['$or']) {
      title = `Đơn hàng hệ thống`
    } else if(status === ORDER_STATUS.FOUND_SHIPPER) {
      title += `Đơn chưa lấy hàng`
    } else if(status === ORDER_STATUS.SHIPPING) {
      title += `Đơn chưa giao xong`
    } else if(status === ORDER_STATUS.DONE) {
      title += `Đơn đã giao xong`
    } else {
      title += `Đơn đã hủy`
    }
    return (
      <View
        style={{
          width: Define.constants.widthScreen,
          zIndex: 2,
        }}>
          <View style={{paddingTop: (Platform.OS === 'ios' ? 0 : StatusBar.currentHeight),backgroundColor: 'transparent',flexDirection: 'row', alignItems: 'center', justifyContent: 'center'}} pointerEvents={'none'}>
            <Text allowFontScaling={false} style={{fontSize: 16, textAlign: 'center', color: '#161616', fontWeight: '500'}}>Lịch sử</Text>
          </View>
          <View style={[styles.tabs, this.props.style]}>
            <ScrollView horizontal={true} showsHorizontalScrollIndicator={false} contentContainerStyle={{}}>
              <View style={{width: Define.constants.widthScreen, flexDirection: 'row', backgroundColor: '#fff', justifyContent: 'center', alignItems: 'center', height: 56}}>
                {this.props.tabs.map((tab, i) => {
                  let borderColor;
                  if(this.state.tabFocus === i) {
                    borderColor = '#0473CD'
                  } else {
                    borderColor = '#E2E2E2'
                  }

                  return (
                    <TouchableOpacity
                      activeOpacity = {0.6}
                      onPress={() => {
                        this.setState({tabFocus: i});
                        this.props.goToPage(i);
                      }}
                      style={{
                        width: Define.constants.widthScreen / this.props.tabs.length, borderBottomWidth: 4, borderColor: borderColor, backgroundColor: 'transparent', height: 56, justifyContent: 'center'
                      }}
                    >
                      <Text allowFontScaling={false} style={{color: '#0473CD', backgroundColor: 'transparent', fontWeight: '500', fontSize: 14, alignSelf: 'center'}} numberOfLines={1}>{tab}</Text>
                    </TouchableOpacity>
                  )
                })}
              </View>
            </ScrollView>
          </View>
      </View>
    );
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (this.state.tabFocus !== nextProps.tabFocus) {
      this.setState({tabFocus: nextProps.tabFocus});
    }
  }
}

const styles = StyleSheet.create({
  tabs: {
    backgroundColor: 'transparent'
  }
});
function selectActions(state) {
  return {
    appSetting: state.AppSetting,
    notifications: state.Notifications
  }
}

export default connect(selectActions, undefined, undefined, {withRef: true})(TabBar);
