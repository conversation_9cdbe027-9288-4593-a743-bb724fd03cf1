
var _ = require('lodash')

//LIB
import React  from 'react';
import {
  View,
  ScrollView,
  RefreshControl,
  Switch,
  StyleSheet,
  Platform,
  TouchableOpacity
} from 'react-native';

var {Actions} = require('react-native-router-flux');
import {Ionicons as Icon} from '@react-native-vector-icons/ionicons';
import { connect } from 'react-redux';
//action

//components
var Define = require('../../../Define');
var Debug = require('../../../Util/Debug');
var Themes = require('../../../Themes');
var Util = require('../../../Util/Util');
var Include = require('../../../Include');
import HeyUIcon from '../../elements/HeyUIcon/HeyUIcon';

var {popupActions} = require('../../popups/PopupManager');
var {globalVariableManager}= require('../../modules/GlobalVariableManager');

var ButtonWrap = require('../../elements/ButtonWrap');

//screens
import BellNotifyScreen from '../../screens/BellNotifyScreen';

// popups
import DefaultPopup from '../../popups/DefaultPopup';
import RegisterServicePopop from '../../popups/RegisterServicePopop';
import LinearGradient from 'react-native-linear-gradient';

export default class MyListItem extends React.PureComponent {
  render() {
    return (
      <View
        style={styles.container}>
          <View style={{height:Define.constants.navBarHeight, flexDirection: 'row', position: 'absolute', alignItems: 'center'}}>
            {this.props.serviceRunning ?
              <TouchableOpacity
                style={{alignItems: 'center', flexDirection: 'row', marginLeft: 16,top:Platform.OS === 'android' ? 7 : 0}}
                onPress={() => {
                  popupActions.setRenderContentAndShow(RegisterServicePopop,{
                    handleSwitchServiceSuccess: this.props.handleSwitchServiceSuccess
                  })
                }}
              >
                  <View>
                    <Icon name='funnel' style={{color:'#0473CD', fontSize:25,backgroundColor: 'transparent'}}></Icon>

                      <View
                        pointerEvents={'none'}
                        style={{position: 'absolute', top: -3, right: -3, width: 13, height: 13, borderRadius: 6.5, backgroundColor: '#e74c3c', alignItems: 'center', justifyContent: 'center'}}>
                          <Include.Text numberOfLines={1} style={{color: '#fff', backgroundColor: 'transparent',fontSize:10, top: -2}}>{this.props.countService}</Include.Text>
                      </View>

                  </View>
                  <Include.Text style={{color:'#000', fontSize:16,backgroundColor: 'transparent', paddingLeft:6}}>Dịch vụ</Include.Text>
              </TouchableOpacity> : null
            }
          </View>

          <View style={styles.rightNavbar}>
            <Include.Text style={{color: '#000', fontSize: 14, backgroundColor: 'transparent', marginRight: 5}}>{this.props.switchOrderValue ? 'Trực tuyến' : 'Ngoại tuyến'}</Include.Text>

            {Platform.OS === 'ios' ?
              <View
                key={'_switch_order_system'}
                style={{backgroundColor: 'transparent'}}>
                <Switch
                  disabled={this.props.disabledOrder}
                  onTintColor={'#3498db'}
                  thumbTintColor={'#fff'}
                  value={this.props.switchOrderValue ? true : false}
                  onValueChange={this.props.onToggleOrder}/>
              </View>
             :<View
               style={{backgroundColor: 'transparent'}}>
               <Switch
                 key={'_switch_order_system'}
                 disabled={this.props.disabledOrder}
                 onTintColor={'#3498db'}
                 thumbTintColor={'#fff'}
                 value={this.props.switchOrderValue ? true : false}
                 onValueChange={this.props.onToggleOrder}/>
             </View>
           }


            <ButtonWrap
              style={{paddingHorizontal: 8}}
              key={'_notify'}
              onPress={()=>{
                Actions.BellNotifyScreen({
                  type: 'push'
                })
            }}>
              <HeyUIcon name={'fi-rr-comment-alt'} color={'#000'} size={25}/>
              {this.props.notify.arrays.length > 0 ?
                <View
                  pointerEvents={'none'}
                  style={{position: 'absolute', top: -6, right: 1, width: 15, height: 15, borderRadius: 7.5, backgroundColor: '#e74c3c', alignItems: 'center', justifyContent: 'center'}}>
                    <Include.Text numberOfLines={1} style={{color: '#fff', backgroundColor: 'transparent',fontSize:10, top: -1}}>{this.props.notify.arrays.length}</Include.Text>
                </View>
              : null}
            </ButtonWrap>
          </View>
      </View>
    )
  }
}

const styles = {
  container: {
    height:Define.constants.navBarHeight,
    width: Define.constants.widthScreen,
    borderBottomWidth:1,
    borderColor:'#e1e1e1',
    zIndex: 2,
  },
  rightNavbar: {
    position:'absolute',
    top: Platform.OS === 'android' ? 9 : 0,
    right:0,
    height:Define.constants.navBarHeight,
    flexDirection:'row',
    alignItems:'center',
    justifyContent:'flex-end',
    flexDirection: 'row'
  },
  notifyIcon: {
    backgroundColor: 'transparent',
    fontSize: 27,
    lineHeight: 36,
    color: '#000',
    paddingHorizontal: 5,
    marginTop:Platform.OS === 'android' ? -2 : 0
  }
}
