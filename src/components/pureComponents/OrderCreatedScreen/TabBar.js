var _ = require('lodash')

import React from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ScrollView,
  StatusBar,
  Platform,
  FlatList,
  InteractionManager,
} from 'react-native';
import * as Animatable from 'react-native-animatable';
var Themes = require('../../../Themes');
import { connect } from 'react-redux';
var Define = require('../../../Define');
import LinearGradient from 'react-native-linear-gradient';

class TabBar extends React.PureComponent {

  constructor(props) {
    super(props);
    this.state = _.merge(this.state, {
      tabFocus: this.props.tabFocus || 0
    })
  }

  render() {
    let title = '<PERSON><PERSON><PERSON> sử';
    return (
      <View
        style={{
          width: Define.constants.widthScreen,
          zIndex: 2,
        }}>
        <View style = {{
            backgroundColor: '#fff',
            paddingBottom: 16,
            elevation: 3,
            zIndex: 10,
            shadowColor: Platform.OS === 'android' ? '#000' : '#d1d1d1',
            shadowOpacity: 0.5,
            shadowOffset:{height: 2, width: 0} ,
            shadowRadius: 1
            }}>
          <View style={{paddingTop: (Platform.OS === 'ios' ? 16 : StatusBar.currentHeight + 16), paddingBottom: 16, backgroundColor: 'transparent', flexDirection: 'row', alignItems: 'baseline', justifyContent: 'center'}}>
            <Text allowFontScaling={false} style={{fontSize: 20, textAlign: 'center', color: '#161616', fontFamily: Define.constants.fontBold500}}> {title} </Text>
          </View>
          {
            this.props.tabs.length == 1
            ?
            null
            :
            <View style={[styles.tabs, this.props.style]}>
              <FlatList
              horizontal={true}
              keyExtractor={(item, index) => {
                return item;
              }}
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={{flexGrow: 1}}
              ref={(ref) => this._tabScrollRef = ref}
              style = {{width: '100%'}}
              initialScrollIndex={this.props.tabFocus}
              initialNumToRender={this.props.tabs.length}
              data={this.props.tabs}
              getItemLayout={(data, index) => (
                { length: 70, offset: 70 * index, index }
              )}
              renderItem={({item, index}) => {
                const tab = item;
                const i = index
                let backGroundColor, textColor;
                  if(this.state.tabFocus === i) {
                    backGroundColor = '#1589D8',
                    textColor = 'white'
                  } else {
                    backGroundColor = '#1589D826',
                    textColor = '#0675C0'
                  }
                  return (
                    <TouchableOpacity
                      key={i}
                      activeOpacity = {0.6}
                      onPress={() => {
                        this.setState({tabFocus: i});
                        this._tabScrollRef.scrollToIndex({index: i, animated: true, viewOffset: 50})
                        InteractionManager.runAfterInteractions(() => {
                          this.props.goToPage(i);
                        });

                      }}
                      style={{
                        flex: 1,
                        paddingHorizontal: 12,
                        backgroundColor: backGroundColor,
                        paddingTop: 6,
                        paddingBottom: 8,
                        justifyContent: 'space-evenly',
                        marginHorizontal: 6,
                        borderRadius: 20,
                      }}
                    >
                      <Text allowFontScaling={false} style={{color: textColor, backgroundColor: 'transparent', fontWeight: '500', fontSize: 14, alignSelf: 'center', fontFamily: Define.constants.fontBold500}} numberOfLines={1}>{tab}</Text>
                    </TouchableOpacity>
                  )
              }}
              />
            </View>
          }

          </View>
      </View>
    );
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (this.state.tabFocus !== nextProps.tabFocus) {
      this.setState({tabFocus: nextProps.tabFocus});
      this._tabScrollRef.scrollToIndex({index: nextProps.tabFocus, animated: true, viewOffset: 50})
    }
  }
}

const styles = StyleSheet.create({
  tabs: {
    backgroundColor: 'transparent'
  }
});
function selectActions(state) {
  return {
    appSetting: state.AppSetting,
    notifications: state.Notifications
  }
}

export default connect(selectActions, undefined, undefined, {withRef: true})(TabBar);
