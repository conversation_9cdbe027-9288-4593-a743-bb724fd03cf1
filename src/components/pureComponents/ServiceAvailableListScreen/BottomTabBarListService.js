var _ = require('lodash')

import React from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Image
} from 'react-native';
import * as Animatable from 'react-native-animatable';
var Themes = require('../../../Themes');
import { connect } from 'react-redux';
var Define = require('../../../Define');
import HeyUIcon from '../../elements/HeyUIcon/HeyUIcon';
import { menuIconContainer } from '../../elements/SideBar/style';
import SBHStoreActions_MiddleWare from '../../../actions/SBHStoreActions_MiddleWare';
import PenguinBoldIcon from '../../elements/PenguinBoldIcon/PenguinBoldIcon';
var RDActions = require('../../../actions/RDActions');

class BottomTabBarListService extends React.PureComponent {

  constructor(props) {
    super(props);
    this.state = _.merge(this.state,
    {
      chosseIcon: 0
    })

    this.icons = [];
    this.names = [];
  }

  componentDidMount() {
    this.setAnimationValue({value: this.props.tabFocus || 0})
    this._listener = this.props.scrollValue.addListener(this.setAnimationValue);
  }

  componentWillUnmount() {
    this.props.scrollValue.removeListener(this.setAnimationValue);
    this._listener = null;
  }

  setAnimationValue = ({ value }) => {
    this.setState({chosseIcon: value})

    this.names.forEach((name, i) => {
      const progress = (Math.abs(value - i) >= 0 && Math.abs(value - i) <= 1) ? Math.abs(value - i) : 1;
      name && name.setNativeProps({
        style: {
          color: this.iconColor(progress)
        }
      });
    });
  }

  //color between rgb(34, 111, 183) and rgb(154, 163, 170)
  iconColor(progress) {
    const red = Math.round((1 - progress) * 34 + progress * 154);
    const green = Math.round((1 - progress) * 111 + progress * 163);
    const blue = Math.round((1 - progress) * 183 + progress * 170);
    return `rgb(${red}, ${green}, ${blue})`;
  }

  render() {
    return <View style={[styles.tabs, this.props.style]}>
      {this.props.tabs.map((tab, i) => {
        let icon;
        if (tab === 'Trang chủ') {
          icon = 'home-1';
        } else if (tab === 'Lịch sử') {
          icon = 'clock';
        } else if (tab === 'Thanh toán') {
          icon = 'wallet';
        } else if (tab === 'Thông báo') {
          icon = 'notification-bing';
        } else if (tab === 'Tài khoản') {
          icon = 'frame';
        } else if (tab === 'Ưu đãi') {
          icon = 'box';
        } else if(tab ==='Gian hàng'){
          icon = 'shop'
        }
        return <TouchableOpacity
          key={tab}
          onPress={() => {
            this.setState({
              chosseIcon: i
            })
            if(i === this.props.tabs.indexOf('Lịch sử')){
              this.props.userPress()
            }
            this.props.goToPage(i)
            }
          }
          style={styles.tab}>
          <View
            style={{flexDirection:'column', alignSelf:'center', alignItems:'center', justifyContent:'center', height: 58}}
          >
            <View style={{marginVertical: 8}}>
              <PenguinBoldIcon
                name={icon}
                color={i === this.state.chosseIcon ? '#1589D8' : '#929394'}
                size={20}
                ref={(icon) => { this.icons[i] = icon; }}
              />
              {tab === 'Thông báo' && this.props.notifications.count ?
                <View
                  pointerEvents={'none'}
                  style={{position: 'absolute', right: -7, top: -3, width: 14, height: 14, borderRadius: 7, backgroundColor: '#e74c3c', alignItems: 'center', justifyContent: 'center'}}>
                    <Text allowFontScaling={false} numberOfLines={1} style={{color: '#fff', backgroundColor: 'transparent', fontSize: 9}}>{this.props.notifications.count}</Text>
                </View> : null
              }
              {tab === 'Ưu đãi' && this.props.user.count ?
                <View
                  pointerEvents={'none'}
                  style={{position: 'absolute', right: -7, top: -3, width: 14, height: 14, borderRadius: 7, backgroundColor: '#e74c3c', alignItems: 'center', justifyContent: 'center'}}>
                    <Text allowFontScaling={false} numberOfLines={1} style={{color: '#fff', backgroundColor: 'transparent', fontSize: 9}}>{this.props.user.count}</Text>
                </View> : null
              }
              {/* {tab === 'Gian hàng' && this.props.user?.countPending ?
                <View
                  pointerEvents={'none'}
                  style={{position: 'absolute', right: -7, top: -3, width: 14, height: 14, borderRadius: 7, backgroundColor: '#e74c3c', alignItems: 'center', justifyContent: 'center'}}>
                    <Text allowFontScaling={false} numberOfLines={1} style={{color: '#fff', backgroundColor: 'transparent', fontSize: 9}}>{this.props.user?.countPending}</Text>
                </View>
                : tab === 'Gian hàng' && this.props.appState?.configStore?.tag && this.props.appSetting?.showTagStore ?
                  <Image
                    resizeMode='stretch'
                    style={{ width: 20, height: 20, position: 'absolute', right: -10, top: -6 }}
                    source={{uri: this.props.appState?.configStore?.tag}}
                  /> : null} */}
            </View>
            <View>
              <Text allowFontScaling={false}
                ref={(name) => { this.names[i] = name; }}
                style={{fontSize: 12, backgroundColor: 'transparent', color: '#696969', textAlign: 'center'}}>
                {tab}</Text>
            </View>
          </View>

        </TouchableOpacity>;
      })}
    </View>;
  }
}

const styles = StyleSheet.create({
  tab: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 5
  },
  tabs: {
    elevation: 10,
    shadowColor: '#000000',
    shadowOpacity: 0.1,
    shadowOffset: {height: -1, width:0},
    borderColor: '#000',
    shadowRadius: 1,
    flexDirection: 'row',
    backgroundColor: '#fff'
  }
});
function selectActions(state) {
  return {
    appState: state.AppState,
    appSetting: state.AppSetting,
    notifications: state.Notifications,
    user:state.User,
  }
}

export default connect(selectActions, undefined, undefined, {withRef: true})(BottomTabBarListService);
