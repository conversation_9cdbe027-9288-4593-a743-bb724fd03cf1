var _ = require('lodash')
//LIB
import React  from 'react';
import {
  View,
  ScrollView,
  RefreshControl,
  Switch,
  StyleSheet,
  Platform,
  TouchableOpacity,
  Image,
  Text,
} from 'react-native';
var {Actions} = require('react-native-router-flux');
import { connect } from 'react-redux';
//action
//components
var Define = require('../../../Define');
var Debug = require('../../../Util/Debug');
var Themes = require('../../../Themes');
var Util = require('../../../Util/Util');
var Include = require('../../../Include');
var {popupActions} = require('../../popups/PopupManager');
var {globalVariableManager}= require('../../modules/GlobalVariableManager');
var ButtonWrap = require('../../elements/ButtonWrap');
//screens
import NotifyScreen from '../../screens/NotifyScreen';
// popups
import DefaultPopup from '../../popups/DefaultPopup';
export default class Service extends React.PureComponent {
  render() {
    const { widthScreen, heightScreen } = Define.constants;
    return(
      <TouchableOpacity
        key={this.props.service._id}
        onPress={() => {
          if (this.props.service.seeMore) {
            return popupActions.setRenderContentAndShow(ServicePopup, {
              services: this.props.listService
            })
          }

          if (this.props.service.active) {
            if(!this.props.service.blockCreate) {
              Actions[this.props.service.link]({
                ...this.props.service.extras,
                serviceId: this.props.service._id,
                initialRegion: this.props.service.delivery ? {
                  latitude: _.get(this.props.senderLocation, 'lat', 0),
                  longitude: _.get(this.props.senderLocation, 'lng', 0),
                  ...Define.constants.defaultLocationDeltaShowShipper
                } : null,
                dayResetPoint: this.props.service.link === 'RewardForShopScreen' ? (this.props.dayResetPoint || 60) : null,
                expirationDate: this.props.service.link === 'RewardForShopScreen' ? (this.props.expirationDate) : null
              });
            }
          } else {
            popupActions.setRenderContentAndShow(DefaultPopup, {
              title: 'Thông báo',
              description: this.props.service.message,
              buttonTitle2: 'Xong',
              buttonTitle: this.props.service.source ? 'Tìm hiểu thêm' : null,
              onPress2: () => {
                popupActions.popPopup();
              },
              onPress: () => {
                popupActions.popPopup();
                if (this.props.service.source) {
                  Actions.WebviewScreen({
                    source: this.props.service.source
                  });
                }
              }
            });
          }
        }}
        style={{width: this.props.lengthService <= 4 ? this.props.width || (Define.constants.widthScreen - 32) / 4 : null,justifyContent: 'center', alignItems: 'center', marginTop: this.props.index < this.props.lengthService ? 0 : 16}}
      >
        <Image style={{width: 56, height: 56}} resizeMode={'stretch'} source={{uri: this.props.service.icon}} />
        <Include.Text style={{fontSize: 14, color: !this.props.service.blockCreate ? '#012548' : '#b2bec3', marginTop: 8, textAlign:'center', fontFamily: Define.constants.fontBold400}}>{this.props.service.name}</Include.Text>
        {this.props.service.name === 'Ưu đãi' && this.props.user?.count ?
          <View
            pointerEvents={'none'}
            style={{position: 'absolute', right: 12, top: 5, width: 20, height: 20, borderRadius: 12, backgroundColor: '#e74c3c', alignItems: 'center', justifyContent: 'center'}}>
              <Text allowFontScaling={false} numberOfLines={1} style={{color: '#fff', backgroundColor: 'transparent', fontSize: 11}}>{this.props.user?.count}</Text>
          </View> : null
        }
        {this.props.service.tag ?
          <View
            pointerEvents={'none'}
            style={{position: 'absolute', right: 4, top: 0, padding: 4, borderRadius: 12, backgroundColor: '#e17055', alignItems: 'center', justifyContent: 'center', borderWidth: 2, borderColor: '#fff', shadowColor: '#fff', shadowOpacity: 0.5, shadowOffset:{height:1, width:0}}}>
              <Text allowFontScaling={false} numberOfLines={1} style={{color: '#fff', backgroundColor: 'transparent', fontSize: 10}}>{this.props.service.tag}</Text>
          </View> : null}
        {this.props.service.tagImage ?
          <Image style={{width: 32, height: 32, position: 'absolute', top: 2, right: this.props.lengthService <= 4 ? 8 : -8}} resizeMode={'contain'} source={{uri: this.props.service.tagImage}} /> : null}
      </TouchableOpacity>
    )
  }
}
