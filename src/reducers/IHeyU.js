var _ = require('lodash');

var RDActionsTypes = require('../actions/RDActionsTypes');

// components
var Define = require('../Define');
var Debug = require('../Util/Debug');
var Util = require('../Util/Util');
var RDUtil = require('./RDUtil');
// NOTE : if want to use promise of middleware action , this reducer must update state to a temp to use in then of promise
// =>no no no , only need update state variable from reduxManager in then funciton   (maybe because pointer changed)

function initLoading() {
  let retObj = {};
  Object.keys(RDActionsTypes.IHeyU).forEach(key => {
    if (key === 'constants') {
      return;
    }
    retObj[key] = {loading: 0};
  });
  return retObj;
}

function IHeyU(
  state = {
    ...initLoading(),
    documentList: [],
    sharedDocument: [],
    listMyPetition: [],
    petitionInf: {},
    messageChatBot: '',
    conversationId: '',
    justLogin: false,
    pickedDocument: [],
    documentReportHistory: [],
    documentSpeechHistory: [],
    documentSummaryHistory: [],
    documentPlanningHistory: [],
    decisionHistory: [],
    socioHistory: [],
    readNewspaperHistory: [],
    imageToTextHistory: [],
    documentChatHistory: [],
    videoExcerptHistory: [],
    documentReportHistoryLength: 0,
    documentPlanningHistoryLength: 0,
    documentSpeechHistoryLength: 0,
    documentSummaryHistoryLength: 0,
    decisionHistoryLength: 0,
    socioHistoryLength: 0,
    readNewspaperHistoryLength: 0,
    imageToTextHistoryLength: 0,
    documentChatHistoryLength: 0,
    videoExcerptHistoryLength: 0,
    decisionData: {},
    socioData: {},
    documentReportData: {},
    documentPlanningData: {},
    documentSpeechData: {},
    documentSummaryData: {},
    videoExcerptData: {},
    imageToTextData: {},
    documentChatData: {},
    readNewspaperData: {},
  },
  action,
) {
  var stateTemp = state;
  switch (action.type) {
    case RDActionsTypes.IHeyU.getDocument: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'getDocument', {
        onSuccess: stateTempIn => {
          let id = action.data.res?.data?._id;
          let index = _.findIndex(stateTempIn.documentList, {_id: id});
          let indexShared = _.findIndex(stateTempIn.sharedDocument, {_id: id});
          if (index !== -1) {
            stateTempIn.documentList[index] = action.data.res.data;
          }
          if (indexShared !== -1) {
            stateTempIn.documentList[index] = action.data.res.data;
            stateTempIn.sharedDocument[indexShared] = action.data.res.data;
          }
          return stateTempIn;
        },
      });
      break;
    }
    case RDActionsTypes.IHeyU.listDocument: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'listDocument', {
        onSuccess: stateTempIn => {
          let result = action.data.res.data || [];
          let arr = stateTempIn.documentList || [];
          result.length &&
            result.map((item, index) => {
              if (_.some(arr, {_id: item._id})) {
                return;
              }
              arr.push(item);
            });
          stateTempIn.documentList = arr;
          return stateTempIn;
        },
      });
      break;
    }
    case RDActionsTypes.IHeyU.listSharedDocuments: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'listSharedDocuments',
        {
          onSuccess: stateTempIn => {
            let arr = stateTempIn.sharedDocument || [];
            let result = action.data.res.data || [];
            result.length &&
              result.map((item, index) => {
                if (_.some(arr, {_id: item._id})) {
                  return;
                }
                arr.push(item);
              });
            stateTempIn.sharedDocument = arr;
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.clearListDocument: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'clearListDocument',
        {
          onRequest: stateTempIn => {
            stateTempIn.documentList = [];
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.clearListSharedDocuments: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'clearListSharedDocuments',
        {
          onRequest: stateTempIn => {
            stateTempIn.sharedDocument = [];
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.listConversationReadNewspaper: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'listConversationReadNewspaper',
        {
          onSuccess: stateTempIn => {
            let arr = stateTempIn.readNewspaperHistory || [];
            action.data.res.data.length &&
              action.data.res.data.map((item, index) => {
                if (item.newConversation && item.status === 'COMPLETED') {
                  return;
                } else {
                  arr.push(item);
                }
              });
            stateTempIn.readNewspaperHistoryLength = action.data.res?.total;
            stateTempIn.readNewspaperHistory = arr;
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.clearConversationReadNewspaper: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'clearConversationReadNewspaper',
        {
          onRequest: stateTempIn => {
            stateTempIn.readNewspaperHistory = [];
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.getSummaryReadNewspaper: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'getSummaryReadNewspaper',
        {
          onSuccess: stateTempIn => {
            stateTempIn.readNewspaperData = action.data.res.data;
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.clearDataReadNewspaper: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'clearDataReadNewspaper',
        {
          onRequest: stateTempIn => {
            stateTempIn.readNewspaperData = {};
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.listConversationVideoExcerpt: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'listConversationVideoExcerpt',
        {
          onSuccess: stateTempIn => {
            let arr = stateTempIn.videoExcerptHistory || [];
            action.data.res.data.length &&
              action.data.res.data.map((item, index) => {
                if (item.newConversation && item.status === 'COMPLETED') {
                  return;
                } else {
                  arr.push(item);
                }
              });
            stateTempIn.videoExcerptHistoryLength = action.data.res?.total;
            stateTempIn.videoExcerptHistory = arr;
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.clearConversationVideoExcerpt: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'clearConversationVideoExcerpt',
        {
          onRequest: stateTempIn => {
            stateTempIn.videoExcerptHistory = [];
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.getConversationVideoExcerpt: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'getConversationVideoExcerpt',
        {
          onSuccess: stateTempIn => {
            stateTempIn.videoExcerptData = action.data.res.data;
            let id = action.data.res?.data?._id;
            let index = _.findIndex(stateTempIn.videoExcerptHistory, {_id: id});
            stateTempIn.videoExcerptHistory[index] = action.data.res.data;
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.clearDataVideoExcerpt: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'clearDataVideoExcerpt',
        {
          onRequest: stateTempIn => {
            stateTempIn.videoExcerptData = {};
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.listConversationImageToText: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'listConversationImageToText',
        {
          onSuccess: stateTempIn => {
            let arr = stateTempIn.imageToTextHistory || [];
            action.data.res.data.length &&
              action.data.res.data.map((item, index) => {
                if (item.newConversation && item.status === 'COMPLETED') {
                  return;
                } else {
                  arr.push(item);
                }
              });
            stateTempIn.imageToTextHistoryLength = action.data.res?.total;
            stateTempIn.imageToTextHistory = arr;
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.clearConversationImageToText: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'clearConversationImageToText',
        {
          onRequest: stateTempIn => {
            stateTempIn.imageToTextHistory = [];
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.getConversationImageToText: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'getConversationImageToText',
        {
          onSuccess: stateTempIn => {
            stateTempIn.imageToTextData = action.data.res.data;
            let id = action.data.res?.data?._id;
            let index = _.findIndex(stateTempIn.imageToTextHistory, {_id: id});
            stateTempIn.imageToTextHistory[index] = action.data.res.data;
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.clearDataImageToText: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'clearDataImageToText',
        {
          onRequest: stateTempIn => {
            stateTempIn.imageToTextData = {};
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.listConversationChatWithDoc: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'listConversationChatWithDoc',
        {
          onSuccess: stateTempIn => {
            let arr = stateTempIn.documentChatHistory || [];
            action.data.res.data.length &&
              action.data.res.data.map((item, index) => {
                if (item.newConversation) {
                  return;
                } else {
                  arr.push(item);
                }
              });
            stateTempIn.documentChatHistoryLength = action.data.res?.total;
            stateTempIn.documentChatHistory = arr;
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.clearConversationChatWithDoc: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'clearConversationChatWithDoc',
        {
          onRequest: stateTempIn => {
            stateTempIn.documentChatHistory = [];
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.getConversationChatWithDoc: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'getConversationChatWithDoc',
        {
          onSuccess: stateTempIn => {
            stateTempIn.documentChatData = action.data.res.data;
            let id = action.data.res?.data?._id;
            let index = _.findIndex(stateTempIn.documentChatHistory, {_id: id});
            stateTempIn.documentChatHistory[index] = action.data.res.data;
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.clearDataChatWithDoc: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'clearDataChatWithDoc',
        {
          onRequest: stateTempIn => {
            stateTempIn.documentChatData = {};
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.listConversationMakeDecision: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'listConversationMakeDecision',
        {
          onSuccess: stateTempIn => {
            let arr = stateTempIn.decisionHistory || [];
            action.data.res.data.length &&
              action.data.res.data.map((item, index) => {
                if (item.newConversation && item.status === 'COMPLETED') {
                  return;
                } else {
                  arr.push(item);
                }
              });
            stateTempIn.decisionHistoryLength = action.data.res?.total;
            stateTempIn.decisionHistory = arr;
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.clearConversationMakeDecision: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'clearConversationMakeDecision',
        {
          onRequest: stateTempIn => {
            stateTempIn.decisionHistory = [];
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.getConversationMakeDecision: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'getConversationMakeDecision',
        {
          onSuccess: stateTempIn => {
            stateTempIn.decisionData = action.data.res.data;
            let id = action.data.res?.data?._id;
            let index = _.findIndex(stateTempIn.decisionHistory, {_id: id});
            stateTempIn.decisionHistory[index] = action.data.res.data;
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.clearDataMakeDecision: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'clearDataMakeDecision',
        {
          onRequest: stateTempIn => {
            stateTempIn.decisionData = {};
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.listConversationSocioEconomicReport: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'listConversationSocioEconomicReport',
        {
          onSuccess: stateTempIn => {
            let arr = stateTempIn.socioHistory || [];
            action.data.res.data.length &&
              action.data.res.data.map((item, index) => {
                if (item.newConversation && item.status === 'COMPLETED') {
                  return;
                } else {
                  arr.push(item);
                }
              });
            stateTempIn.socioHistoryLength = action.data.res?.total;
            stateTempIn.socioHistory = arr;
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.clearConversationSocioEconomicReport: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'clearConversationSocioEconomicReport',
        {
          onRequest: stateTempIn => {
            stateTempIn.socioHistory = [];
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.getConversationSocioEconomicReport: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'getConversationSocioEconomicReport',
        {
          onSuccess: stateTempIn => {
            stateTempIn.socioData = action.data.res.data;
            let id = action.data.res?.data?._id;
            let index = _.findIndex(stateTempIn.socioHistory, {_id: id});
            stateTempIn.socioHistory[index] = action.data.res.data;
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.clearDataSocioEconomicReport: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'clearDataSocioEconomicReport',
        {
          onRequest: stateTempIn => {
            stateTempIn.socioData = {};
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.listConversationReport: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'listConversationReport',
        {
          onSuccess: stateTempIn => {
            let arr = stateTempIn.documentReportHistory || [];
            action.data.res.data.length &&
              action.data.res.data.map((item, index) => {
                if (item.newConversation && item.status === 'COMPLETED') {
                  return;
                } else {
                  arr.push(item);
                }
              });
            stateTempIn.documentReportHistoryLength = action.data.res.total;
            stateTempIn.documentReportHistory = arr;
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.clearConversationReport: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'clearConversationReport',
        {
          onRequest: stateTempIn => {
            stateTempIn.documentReportHistory = [];
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.listConversationSpeech: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'listConversationSpeech',
        {
          onSuccess: stateTempIn => {
            let arr = stateTempIn.documentSpeechHistory || [];
            action.data.res.data.length &&
              action.data.res.data.map((item, index) => {
                if (item.newConversation && item.status === 'COMPLETED') {
                  return;
                } else {
                  arr.push(item);
                }
              });
            stateTempIn.documentSpeechHistoryLength = action.data.res?.total;
            stateTempIn.documentSpeechHistory = arr;
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.clearConversationSpeech: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'clearConversationSpeech',
        {
          onRequest: stateTempIn => {
            stateTempIn.documentSpeechHistory = [];
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.listConversationPlanning: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'listConversationPlanning',
        {
          onSuccess: stateTempIn => {
            let arr = stateTempIn.documentPlanningHistory || [];
            action.data.res.data.length &&
              action.data.res.data.map((item, index) => {
                if (item.newConversation && item.status === 'COMPLETED') {
                  return;
                } else {
                  arr.push(item);
                }
              });
            stateTempIn.documentPlanningHistoryLength = action.data.res?.total;
            stateTempIn.documentPlanningHistory = arr;
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.clearConversationPlanning: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'clearConversationPlanning',
        {
          onRequest: stateTempIn => {
            stateTempIn.documentPlanningHistory = [];
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.listConversationDocumentSummary: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'listConversationDocumentSummary',
        {
          onSuccess: stateTempIn => {
            let arr = stateTempIn.documentSummaryHistory || [];
            action.data.res.data.length &&
              action.data.res.data.map((item, index) => {
                if (item.newConversation && item.status === 'COMPLETED') {
                  return;
                } else {
                  arr.push(item);
                }
              });
            stateTempIn.documentSummaryHistoryLength = action.data.res?.total;
            stateTempIn.documentSummaryHistory = arr;
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.clearConversationDocumentSummary: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'clearConversationDocumentSummary',
        {
          onRequest: stateTempIn => {
            stateTempIn.documentSummaryHistory = [];
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.getConversationReport: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'getConversationReport',
        {
          onSuccess: stateTempIn => {
            stateTempIn.documentReportData = action.data.res.data;
            let index = _.findIndex(stateTempIn.documentReportHistory, {
              _id: action.data.res.data._id,
            });
            stateTempIn.documentReportHistory[index] = action.data.res.data;
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.clearDataReport: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'clearDataReport',
        {
          onRequest: stateTempIn => {
            stateTempIn.documentReportData = {};
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.clearDataSpeech: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'clearDataSpeech',
        {
          onRequest: stateTempIn => {
            stateTempIn.documentSpeechData = {};
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.clearDataPlanning: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'clearDataPlanning',
        {
          onRequest: stateTempIn => {
            stateTempIn.documentPlanningData = {};
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.clearDataDocumentSummary: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'clearDataDocumentSummary',
        {
          onRequest: stateTempIn => {
            stateTempIn.documentSummaryData = {};
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.getConversationPlanning: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'getConversationPlanning',
        {
          onSuccess: stateTempIn => {
            stateTempIn.documentPlanningData = action.data.res.data;
            let id = action.data.res?.data?._id;
            let index = _.findIndex(stateTempIn.documentPlanningHistory, {
              _id: id,
            });
            stateTempIn.documentPlanningHistory[index] = action.data.res.data;
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.getConversationSpeech: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'getConversationSpeech',
        {
          onSuccess: stateTempIn => {
            stateTempIn.documentSpeechData = action.data.res.data;
            let id = action.data.res?.data?._id;
            let index = _.findIndex(stateTempIn.documentSpeechHistory, {
              _id: id,
            });
            stateTempIn.documentSpeechHistory[index] = action.data.res.data;
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.getConversationDocumentSummary: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'getConversationDocumentSummary',
        {
          onSuccess: stateTempIn => {
            stateTempIn.documentSummaryData = action.data.res.data;
            let id = action.data.res?.data?._id;
            let index = _.findIndex(stateTempIn.documentSummaryHistory, {
              _id: id,
            });
            stateTempIn.documentSummaryHistory[index] = action.data.res.data;
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.addDocument: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'addDocument', {
        onRequest: stateTempIn => {
          stateTempIn.pickedDocument = stateTempIn.pickedDocument.concat(
            action.data,
          );
          return stateTempIn;
        },
      });
      break;
    }
    case RDActionsTypes.IHeyU.clearDocument: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'clearDocument', {
        onRequest: stateTempIn => {
          stateTempIn.pickedDocument = [];
          return stateTempIn;
        },
      });
      break;
    }
    case RDActionsTypes.IHeyU.removeDocument: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'removeDocument',
        {
          onRequest: stateTempIn => {
            let id = action.data.id;
            let index = _.findIndex(stateTempIn.pickedDocument, {_id: id});
            stateTempIn.pickedDocument.splice(index, 1);
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.getDocument: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'getDocument', {
        onSuccess: stateTempIn => {
          let id = action.data.res?.data?._id;
          let index = _.findIndex(stateTempIn.pickedDocument, {_id: id});
          stateTempIn.pickedDocument[index] = action.data.res.data;
          return stateTempIn;
        },
      });
      break;
    }
    case RDActionsTypes.IHeyU.listPetition: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'listPetition', {
        onRequest: stateTempIn => {
          if (!action.data.arg.from) {
            stateTempIn.listMyPetition = [];
            stateTempIn.petitionInf = {};
          }
          return stateTempIn;
        },
        onSuccess: stateTempIn => {
          action.data.res.data.forEach(data => {
            const index = stateTempIn.listMyPetition.indexOf(data._id);
            if (index > -1) {
              stateTempIn.listMyPetition.splice(index, 1);
            }
            stateTempIn.listMyPetition.push(data._id);
            stateTempIn.petitionInf[data._id] = data;
          });
          return stateTempIn;
        },
      });
      break;
    }
    case RDActionsTypes.IHeyU.saveConversationId: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'saveConversationId',
        {
          onRequest: stateTempIn => {
            stateTempIn.conversationId = action.data;
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.setJustLogin: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'setJustLogin', {
        onRequest: stateTempIn => {
          stateTempIn.justLogin = action.data;
          return stateTempIn;
        },
      });
      break;
    }
    case RDActionsTypes.IHeyU.deleteConversationId: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'deleteConversationId',
        {
          onRequest: stateTempIn => {
            if (stateTempIn.conversationId === action.data) {
              stateTempIn.conversationId = '';
            }
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IHeyU.getPetition: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'getPetition', {
        onSuccess: stateTempIn => {
          const data = action.data.res.data;
          if (!stateTempIn.listMyPetition.includes(data._id)) {
            stateTempIn.listMyPetition.unshift(data._id);
          }
          stateTempIn.petitionInf = _.cloneDeep(stateTempIn.petitionInf);
          stateTempIn.petitionInf[data._id] = data;
          return stateTempIn;
        },
      });

      break;
    }
    default:
      // Debug.log('ServerConnection:unknown type:'+action.type);
      break;
  }

  return stateTemp;
}

module.exports = IHeyU;
