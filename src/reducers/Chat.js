var _ = require('lodash')

var RDActionsTypes = require( '../actions/RDActionsTypes');

// components
var Define = require('../Define');
var Debug = require('../Util/Debug');
var Util = require('../Util/Util');
var RDUtil = require('./RDUtil');
// NOTE : if want to use promise of middleware action , this reducer must update state to a temp to use in then of promise
// =>no no no , only need update state variable from reduxManager in then funciton   (maybe because pointer changed)

function initLoading(){
  let retObj={};
  Object.keys(RDActionsTypes.Chat).forEach((key)=>{
    if (key === 'constants') { return;}
    retObj[key] = {loading:0};
  })
  return retObj;
}

function Chat(state ={
                ...initLoading(),
                listConversations: [],
                conversations: {},
                messages: {}
              } , action) {
  var stateTemp =state;
  switch (action.type) {
    case RDActionsTypes.Chat.clear :{
      stateTemp = RDUtil.processReducerLoading(state,action,'clear',
                {
                  onRequest: (stateTempIn) => {
                    stateTempIn.listConversations = [];
                    stateTempIn.conversations = {};
                    stateTempIn.messages = {};
                    return stateTempIn;
                  }
                })
      break;
    }

    case RDActionsTypes.Chat.clearMessages :{
      stateTemp = RDUtil.processReducerLoading(state,action,'clearMessages',
                {
                  onRequest: (stateTempIn) => {
                    stateTempIn.messages = {};
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.Chat.listHistory:{
      stateTemp = RDUtil.processReducerLoading(state,action,'listHistory',
                {
                  onRequest: (stateTempIn) => {
                    return stateTempIn;
                  },
                  onSuccess: (stateTempIn) => {
                    action.data.res.data.forEach((conversation) => {
                      stateTempIn.listConversations.push(conversation._id);
                      stateTempIn.conversations[conversation._id] = conversation;
                    });


                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.Chat.newMessage:{
      stateTemp = RDUtil.processReducerLoading(state,action,'newMessage',
                {
                  onRequest: (stateTempIn) => {
                    // Update conversation
                    for(let i=0; i<stateTempIn.listConversations.length; i++) {
                      if(stateTempIn.listConversations[i] === action.data._id) {
                        stateTempIn.listConversations.splice(i, 1);
                        break;
                      }
                    }

                    stateTempIn.listConversations.unshift(action.data._id);
                    stateTempIn.conversations[action.data._id] = action.data;

                    stateTempIn.messages[action.data._id] = stateTempIn.messages[action.data._id] || [];
                    if(action.data.latestMessage.location){
                      stateTempIn.messages[action.data._id].unshift({
                        conversation: action.data._id,
                        message: action.data.latestMessage.message,
                        imageUrl: action.data.latestMessage.imageUrl || '',
                        senderId: action.data.latestMessage.senderId,
                        location: action.data.latestMessage.location,
                        _id: action.data.latestMessage._id,
                        seen: 0,
                        createdAt: action.data.updatedAt
                      })
                    } else{
                      stateTempIn.messages[action.data._id].unshift({
                        conversation: action.data._id,
                        message: action.data.latestMessage.message,
                        imageUrl: action.data.latestMessage.imageUrl || '',
                        senderId: action.data.latestMessage.senderId,
                        _id: action.data.latestMessage._id,
                        seen: 0,
                        createdAt: action.data.updatedAt
                      })
                    }

                    return stateTempIn;
                  },
                  onSuccess: (stateTempIn) => {
                    return stateTempIn;
                  }
                })
      break;
    }

    case RDActionsTypes.Chat.getHistoryByConversation:{
      stateTemp = RDUtil.processReducerLoading(state,action,'getHistoryByConversation',
                {
                  onRequest: (stateTempIn) => {
                    if(!action.data.arg.from) {
                      stateTempIn.messages[action.data.arg.conversation] = [];
                    }

                    return stateTempIn;
                  },
                  onSuccess: (stateTempIn) => {
                    const conversation = action.data.arg.conversation;
                    stateTempIn.messages[conversation] = stateTempIn.messages[conversation] || [];

                    action.data.res.data.messages.forEach((message) => {
                      message.status = 1;
                      stateTempIn.messages[conversation].push(message);
                    });

                    return stateTempIn;
                  }
                })
      break;
    }

    case RDActionsTypes.Chat.getHistoryByUser:{
      stateTemp = RDUtil.processReducerLoading(state,action,'getHistoryByUser',
                {
                  onRequest: (stateTempIn) => {
                    return stateTempIn;
                  },
                  onSuccess: (stateTempIn) => {
                    if(action.data.res.data) {
                      stateTempIn.listConversations.push(action.data.res.data.conversation._id);
                      stateTempIn.conversations[action.data.res.data.conversation._id] = action.data.res.data.conversation;
                      stateTempIn.messages[action.data.res.data.conversation._id] = [];
                      action.data.res.data.messages.forEach((message) => {
                        message.status = 1;
                        stateTempIn.messages[action.data.res.data.conversation._id].push(message);
                      })
                    }

                    return stateTempIn;
                  }
                })
      break;
    }

    case RDActionsTypes.Chat.typingMessage:{
      stateTemp = RDUtil.processReducerLoading(state,action,'typingMessage',
                {
                  onRequest: (stateTempIn) => {
                    if(stateTempIn.conversations[action.data.conversation]) {
                      stateTempIn.conversations[action.data.conversation].typing = action.data.value;
                    }

                    return stateTempIn;
                  },
                  onSuccess: (stateTempIn) => {
                    return stateTempIn;
                  }
                })
      break;
    }

    case RDActionsTypes.Chat.sendMessage:{
      stateTemp = RDUtil.processReducerLoading(state,action,'sendMessage',
                {
                  onRequest: (stateTempIn) => {
                    if(action.data.conversation) {
                      stateTempIn.messages[action.data.conversation] = stateTempIn.messages[action.data.conversation] || [];
                      stateTempIn.messages[action.data.conversation].unshift({
                        _id: action.data._id,
                        conversation: action.data.conversation,
                        senderId: action.data.senderId,
                        message: action.data.newMessage,
                        file: action.data.file || '',
                        status: 0,
                        createdAt: Date.now()
                      });
                    }

                    return stateTempIn;
                  },
                  onSuccess: (stateTempIn) => {
                    action.data.data.data.message.status = 1;

                    if(action.data.data.data.conversation) {
                      const conversation = action.data.data.data.conversation;
                      stateTempIn.listConversations.unshift(conversation._id);
                      stateTempIn.conversations[conversation._id] = conversation;
                      stateTempIn.messages[conversation._id] = [action.data.data.data.message];
                    } else {
                      const conversationId = action.data.arg.conversation;
                      const fakeId = action.data.arg._id;

                      // Update conversation
                      for(let i=0; i<stateTempIn.listConversations.length; i++) {
                        if(stateTempIn.listConversations[i] === conversationId) {
                          stateTempIn.listConversations.splice(i, 1);
                          break;
                        }
                      }
                      stateTempIn.listConversations.unshift(conversationId);

                      if(stateTempIn.conversations[conversationId]) {
                        stateTempIn.conversations[conversationId].latestMessage = action.data.data.data.message;
                      }

                      // Update message
                      if(stateTempIn.messages[conversationId]) {
                        for(let i=0; i<stateTempIn.messages[conversationId].length; i++) {
                          if(stateTempIn.messages[conversationId][i]._id === fakeId) {
                            stateTempIn.messages[conversationId][i] = action.data.data.data.message;
                            break;
                          }
                        }
                      }
                    }

                    return stateTempIn;
                  },
                  onError: (stateTempIn) => {
                    if(action.data.arg.conversation) {
                      const fakeId = action.data.arg._id;
                      for(let i=0; i<stateTempIn.messages[action.data.arg.conversation].length; i++) {
                        if(stateTempIn.messages[action.data.arg.conversation][i]._id === fakeId) {
                          stateTempIn.messages[action.data.arg.conversation][i].status = 2;
                          break;
                        }
                      }
                    }

                    return stateTempIn;
                  }
                })
      break;
    }

    case RDActionsTypes.Chat.reSendMessage:{
      stateTemp = RDUtil.processReducerLoading(state,action,'reSendMessage',
                {
                  onRequest: (stateTempIn) => {
                    stateTempIn.messages[action.data.conversation].forEach((message) => {
                      if(message.status === 2 && message._id === action.data._id ) {
                        message.status = 0;
                      }
                    })
                    return stateTempIn;
                  },
                  onSuccess: (stateTempIn) => {
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.Chat.seenMessage:{
      stateTemp = RDUtil.processReducerLoading(state,action,'seenMessage',
                {
                  onRequest: (stateTempIn) => {
                    const conversationId = action.data.conversation;
                    const idMessage = action.data.id;
                    if(stateTempIn.conversations[conversationId] && (stateTempIn.conversations[conversationId].latestMessage._id === idMessage)) {
                      stateTempIn.conversations[conversationId].latestMessage.seen = 1;
                    }

                    if(stateTempIn.messages[conversationId]) {
                      let needUpdate = false;
                      for(let i=0; i<stateTempIn.messages[conversationId].length; i++) {
                        if(stateTempIn.messages[conversationId][i]._id === idMessage) {
                          needUpdate = true;
                        }

                        if(needUpdate) {
                          if(!stateTempIn.messages[conversationId][i].seen) {
                            stateTempIn.messages[conversationId][i].seen = 1;
                          }
                        }
                      }
                    }

                    return stateTempIn;
                  },
                  onSuccess: (stateTempIn) => {
                    return stateTempIn;
                  }
                })
      break;
    }
    default:
      // Debug.log('ServerConnection:unknown type:'+action.type);
      break;
  }

  return stateTemp;

}


module.exports= Chat;
