var _ = require('lodash')

var RDActionsTypes = require( '../actions/RDActionsTypes');

// components
var Define = require('../Define');
var Debug = require('../Util/Debug');
var Util = require('../Util/Util');
var RDUtil = require('./RDUtil');
// NOTE : if want to use promise of middleware action , this reducer must update state to a temp to use in then of promise
// =>no no no , only need update state variable from reduxManager in then funciton   (maybe because pointer changed)

function initLoading(){
  let retObj={};
  Object.keys(RDActionsTypes.Shipper).forEach((key)=>{
    if (key === 'constants') { return;}
    retObj[key] = {loading:0};
  })
  return retObj;
}

const shipperFormat = {
  name: '',
  likes: 0,
  dislikes: 0,
  phone: '',
  avatar: '',
  location: {

  },
  updatedAt: 0
}

function Shipper(state ={
                ...initLoading(),
                shippers: [],
              } , action) {
  var stateTemp =state;
  switch (action.type) {
    case RDActionsTypes.Shipper.getNearest:{
      stateTemp = RDUtil.processReducerLoading(state,action,'getNearest',
                {
                  onRequest:(stateTempIn) => {
                    stateTempIn.shippers = []
                    return stateTempIn
                  },
                  onSuccess:(stateTempIn)=>{
                    stateTempIn.shippers = action.data.res.data.map((shipper) => {
                      return {
                        location: shipper.location
                      }
                    })
                    return stateTempIn
                  },
                  onError:(stateTempIn) => {
                    stateTempIn.shippers = []
                    return stateTempIn
                  }
                })

      break;
    }

    case RDActionsTypes.Shipper.clear:{
      stateTemp = RDUtil.processReducerLoading(state,action,'clear',
                {
                  onRequest:(stateTempIn) => {
                    stateTempIn.shippers = []
                    return stateTempIn
                  }
                })
      break;
    }

    default:
      // Debug.log('ServerConnection:unknown type:'+action.type);
      break;
  }

  return stateTemp;

}


module.exports= Shipper;
