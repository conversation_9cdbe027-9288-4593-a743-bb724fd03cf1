var _ = require('lodash')

var RDActionsTypes = require( '../actions/RDActionsTypes');

// components
var Define = require('../Define');
var Debug = require('../Util/Debug');
var Util = require('../Util/Util');
var RDUtil = require('./RDUtil');
// NOTE : if want to use promise of middleware action , this reducer must update state to a temp to use in then of promise
// =>no no no , only need update state variable from reduxManager in then funciton   (maybe because pointer changed)
const ORDER_STATUS = {
    ON_TIMER: -2,
    WAIT_FOR_PAYMENT: -1,
    LOOKING_SHIPPER: 0,
    FOUND_SHIPPER: 1,
    SHIPPING: 2,
    DONE: 3,
    CAN_NOT_FIND_SHIPPER: 4,
    REJECT: 5,
    DO_NOT_GET_GOODS: 6,
    TURNING_ORDER: 7,
    TURNING_ORDER_DONE: 8,
    START_RETURN: 9,
    FINISH_RETURN: 10
}

function initLoading(){
  let retObj={};
  Object.keys(RDActionsTypes.FeedsSystem).forEach((key)=>{
    if (key === 'constants') { return;}
    retObj[key] = {loading:0};
  })
  return retObj;
}

const feedFormat = {

}

const orderTypeFormat={
  "_id": "",
  "name": "",
  "message": "",
  "icon": "",
  "showTip": 0,
  "forceMoney": 0,
  "forceAuthen": 0,
  "multipleDes": 0,
  "focus": 0
}

function FeedsSystem(state ={
                ...initLoading(),
                listFeeds: [],
                listInstanceFeeds: [],
                feedInf: {},
                orderType:[],
                feedsOrderSystem:[],
                stateOrder: {},
                currentOrder: {},
                currentBike: {},
                currentOrderStore: {},
                configOrder: {
                  message: "",
                  maxDistance: 100,
                  tabs: ['system']
                },
                isListing: false,
                configOrderSystem: {
                  isOpenCall: 1,
                  isOpenMessage: 1,
                  isOpenCallWeb: 1,
                  isOpenMessageWeb: 1,
                  timeTakeImage: 300000,
                  isOpenTakeImageOrder: 0,
                  timeContactDoneOrder: 60*60*1000,
                  timeShareLinkDoneOrder: 24*60*60*1000,
                  timeContactDoneOrderForShop: 60*60*1000,
                  timeContactDoneHeyCare: 60*60*1000,
                  timeShareLinkDoneHeyCare: 60*60*1000,
                  timeShowJobDoneHeyCare: 60*60*1000,
                  takeBillImg: 1
                },
                configImageOrderForShop: {
                  isOpen: 0,
                  maxTimeShowTip: 2,
                  message: '',
                  image: ''
                },
                isLater: 0,
                configInsurance: {
                  isOpen: 0,
                  imagePopup: '',
                  titlePopup: '',
                  descriptionPopup: '',
                  description: '',
                  title: '',
                  image: '',
                  money: 0,
                },
              } , action) {
  var stateTemp =state;
  switch (action.type) {
    case RDActionsTypes.FeedsSystem.updateStatus:{
      stateTemp = RDUtil.processReducerLoading(state,action,'updateStatus',
                {
                  onSuccess:(stateTempIn) => {
                    const orderId = action.data.arg.id;
                    const status = action.data.arg.status;
                    const location = action.data.arg.location.geometry;
                    if(stateTempIn.feedInf[orderId]) {
                      stateTempIn.feedInf[orderId].status = status;
                      if (status === 2) {
                        stateTempIn.feedInf[orderId].takeOrderInf = {
                          location: {
                            coordinates: [location.long, location.lat]
                          },
                          time: Date.now()
                        }
                      } else if (status === 3) {
                        stateTempIn.feedInf[orderId].doneOrderInf = {
                          location: {
                            coordinates: [location.long, location.lat]
                          },
                          time: Date.now()
                        }
                      } else if (status === 8) {
                        stateTempIn.feedInf[orderId].returnDoneOrderInf = {
                          location: {
                            coordinates: [location.long, location.lat]
                          },
                          time: Date.now()
                        }
                      } else if(status === 9){
                        stateTempIn.feedInf[orderId].startReturnOrderInf = {
                          location: {
                            coordinates: [location.long, location.lat]
                          },
                          time: Date.now()
                        }
                      } else if(status === 10){
                        stateTempIn.feedInf[orderId].finishReturnOrderInf = {
                          location: {
                            coordinates: [location.long, location.lat]
                          },
                          time: Date.now()
                        }
                      }
                    }
                    if(status !== 1 && status !== 2) {
                      for (let i = 0; i < stateTempIn.listInstanceFeeds.length; i++) {
                        if(stateTempIn.listInstanceFeeds[i]._id === orderId) {
                          stateTempIn.listInstanceFeeds.splice(i,1);
                        }
                      }
                    }
                    for (let i = 0; i < stateTempIn.listInstanceFeeds.length; i++) {
                      if(stateTempIn.listInstanceFeeds[i]._id === orderId) {
                        stateTempIn.listInstanceFeeds[i].status = status
                        break;
                      }
                    }
                    if (status === 3 || status === 8 || status === 10) {
                      stateTempIn.feedInf[orderId].canRate = 1;
                    }
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.FeedsSystem.get:{
      stateTemp = RDUtil.processReducerLoading(state,action,'get',
                {
                  onSuccess:(stateTempIn) => {
                    const feed = action.data.res.data;
                    stateTempIn.feedInf[feed._id] = feed
                    return stateTempIn;
                  }
                })

      break;
    }

    case RDActionsTypes.FeedsSystem.getConfigOrder:{
      stateTemp = RDUtil.processReducerLoading(state,action,'getConfigOrder',
                {
                  onSuccess:(stateTempIn) => {
                    stateTempIn.configOrder = action.data.res.data;
                    return stateTempIn;
                  }
                })

      break;
    }

    case RDActionsTypes.FeedsSystem.getConfigForCreatedOrders:{
      stateTemp = RDUtil.processReducerLoading(state,action,'getConfigForCreatedOrders',
                {
                  onSuccess:(stateTempIn) => {
                    stateTempIn.configOrderSystem = action.data.res.data;
                    return stateTempIn;
                  }
                })

      break;
    }

    case RDActionsTypes.FeedsSystem.uploadBill:{
      stateTemp = RDUtil.processReducerLoading(state,action,'uploadBill',
                {
                  onSuccess:(stateTempIn) => {
                    const result = action.data.res.data;
                    if(result && result.orderId && result.url && stateTempIn.feedInf[result.orderId]) {
                      let bills = stateTempIn.feedInf[result.orderId].bills && stateTempIn.feedInf[result.orderId].bills.length ? stateTempIn.feedInf[result.orderId].bills : []
                      bills.unshift({url: result.url, createdAt: Date.now()})
                      stateTempIn.feedInf[result.orderId].bills = bills
                    }
                    return stateTempIn;
                  }
                })

      break;
    }

    case RDActionsTypes.FeedsSystem.getForShipper:{
      stateTemp = RDUtil.processReducerLoading(state,action,'getForShipper',
                {
                  onSuccess:(stateTempIn) => {
                    const feed = action.data.res.data;
                    stateTempIn.feedInf[feed._id] = feed
                    return stateTempIn;
                  }
                })

      break;
    }
    case RDActionsTypes.FeedsSystem.getForShop:{
      stateTemp = RDUtil.processReducerLoading(state,action,'getForShop',
                {

                  onRequest: (stateTempIn) => {
                    stateTempIn.isListing = true
                    return stateTempIn;
                  },
                  onSuccess:(stateTempIn) => {
                    const feed = action.data.res.data;
                    stateTempIn.feedInf[feed._id] = feed
                    if(stateTempIn.isListing) {
                      stateTempIn.isListing = false
                    }
                    return stateTempIn;
                  }
                })

      break;
    }
    case RDActionsTypes.FeedsSystem.getChangedOrdersForShop:{
      stateTemp = RDUtil.processReducerLoading(state,action,'getChangedOrdersForShop',
                {
                  onRequest: (stateTempIn) => {
                    stateTempIn.isListing = true
                    return stateTempIn;
                  },
                  onSuccess:(stateTempIn) => {
                    let feed = action.data.res.data;
                    if(feed.length > 0) {
                      feed.forEach((newFeed) => {
                        if(stateTempIn.feedInf[newFeed._id]) {
                          stateTempIn.feedInf[newFeed._id] = newFeed
                        }
                      })
                    }
                    stateTempIn.listFeeds.sort((firstOrder,secondOrder) => {
                      if(stateTempIn.feedInf[firstOrder].updatedAt < stateTempIn.feedInf[secondOrder].updatedAt) {
                        return 1;
                      }
                      return -1;
                    })
                    if(stateTempIn.isListing) {
                      stateTempIn.isListing = false
                    }
                    return stateTempIn;
                  }
                })

      break;
    }
    case RDActionsTypes.FeedsSystem.getChangedOrdersForShipper:{
      stateTemp = RDUtil.processReducerLoading(state,action,'getChangedOrdersForShipper',
                {
                  onSuccess:(stateTempIn) => {
                    let feed = action.data.res.data;

                    if(feed.length > 0) {
                      feed.forEach((newFeed) => {
                        if(stateTempIn.feedInf[newFeed._id]) {
                          stateTempIn.feedInf[newFeed._id] = newFeed
                        }
                      })
                    }
                    stateTempIn.listFeeds.sort((firstOrder,secondOrder) => {
                      if(stateTempIn.feedInf[firstOrder].acceptedAt < stateTempIn.feedInf[secondOrder].acceptedAt) {
                        return 1;
                      }
                      return -1;
                    })
                    return stateTempIn;
                  }
                })

      break;
    }
    case RDActionsTypes.FeedsSystem.getOrderType:{
      stateTemp = RDUtil.processReducerLoading(state,action,'getOrderType',
                {
                  onSuccess:(stateTempIn) => {
                    if (action.data.res.data) {
                      stateTempIn.orderType = action.data.res.data.map((current) => {
                        let temp = Util.dataProtectAndMap(current, orderTypeFormat);
                        return temp
                      })
                    }
                    return stateTempIn;
                  }
                })

      break;
    }
    case RDActionsTypes.FeedsSystem.list:{
      stateTemp = RDUtil.processReducerLoading(state,action,'list',
                {
                  onRequest: (stateTempIn) => {
                    if(!action.data.arg.from){
                      stateTempIn.listFeeds = [];
                      stateTempIn.feedInf = {};
                    }
                    return stateTempIn;
                  },
                  onSuccess:(stateTempIn)=>{
                    action.data.res.data.forEach((feed) => {
                      stateTempIn.listFeeds.push(feed._id);
                      stateTempIn.feedInf[feed._id] = feed
                    });
                    return stateTempIn;
                  }
                })

      break;
    }
    case RDActionsTypes.FeedsSystem.listForShop:{
      stateTemp = RDUtil.processReducerLoading(state,action,'listForShop',
                {
                  onRequest: (stateTempIn) => {
                    if(!action.data.arg.from){
                      stateTempIn.listFeeds = [];
                      stateTempIn.feedInf = {};
                      stateTempIn.stateOrder = {};
                      stateTempIn.isListing = true
                    }
                    return stateTempIn;
                  },
                  onSuccess:(stateTempIn)=>{
                    const status = _.get(action, 'data.arg.query.status' ,'')
                    let scheduleOrders = []
                    action.data.res.data.forEach((feed) => {
                      if(status === ORDER_STATUS.ON_TIMER) {
                        scheduleOrders.push(feed._id)
                      } else {
                        stateTempIn.listFeeds.push(feed._id);
                      }
                      stateTempIn.feedInf[feed._id] = feed
                    });
                    const data = scheduleOrders.concat(stateTempIn.listFeeds);
                    stateTempIn.listFeeds = data
                    if(stateTempIn.isListing) {
                      stateTempIn.isListing = false
                    }
                    return stateTempIn;
                  }
                })

      break;
    }
    case RDActionsTypes.FeedsSystem.listInstanceOrder:{
      stateTemp = RDUtil.processReducerLoading(state,action,'listInstanceOrder',
                {
                  onSuccess:(stateTempIn)=>{
                    stateTempIn.listInstanceFeeds = action.data.res.data;
                    return stateTempIn;
                  }
                })
      break;
    }

    case RDActionsTypes.FeedsSystem.modify:{
      stateTemp = RDUtil.processReducerLoading(state,action,'modify',
                {
                  onSuccess:(stateTempIn)=>{
                    const orderId = action.data.arg.id;
                    delete stateTempIn.stateOrder[orderId];
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.FeedsSystem.rejectForShop:{
      stateTemp = RDUtil.processReducerLoading(state,action,'rejectForShop',
                {
                  onSuccess:(stateTempIn)=>{
                    const orderId = action.data.arg.id;
                    if(stateTempIn.feedInf[orderId]) {
                      stateTempIn.feedInf[orderId].status = 5;
                    }
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.FeedsSystem.rejectForShipper:{
      stateTemp = RDUtil.processReducerLoading(state,action,'rejectForShipper',
                {
                  onSuccess:(stateTempIn)=>{
                    const orderId = action.data.arg.id;
                    if(stateTempIn.feedInf[orderId]) {
                      stateTempIn.feedInf[orderId].status = 0;
                    }
                    return stateTempIn;
                  }
                })

      break;
    }
    case RDActionsTypes.FeedsSystem.trackCall:{
      stateTemp = RDUtil.processReducerLoading(state,action,'trackCall',
                {
                  onSuccess:(stateTempIn)=>{
                    const orderId = action.data.arg.id;
                    if(stateTempIn.feedInf[orderId]) {
                      stateTempIn.feedInf[orderId].hasCalled = 1;
                    }
                    for (let i = 0; i < stateTempIn.listInstanceFeeds.length; i++) {
                      if(stateTempIn.listInstanceFeeds[i]._id === orderId) {
                        stateTempIn.listInstanceFeeds[i].hasCalled = 1
                        break;
                      }
                    }
                    return stateTempIn;
                  }
                })

      break;
    }
    case RDActionsTypes.FeedsSystem.retry:{
      stateTemp = RDUtil.processReducerLoading(state,action,'retry',
                {
                  onSuccess:(stateTempIn)=>{
                    const orderId = action.data.arg.id;
                    const confirmNow = action.data.arg.confirmNow;
                    delete stateTempIn.stateOrder[orderId];
                    if(stateTempIn.feedInf[orderId]) {
                      let statusUpdate = ORDER_STATUS.LOOKING_SHIPPER
                      if(Define.constants.pendingPaymentMethods.includes(stateTempIn.feedInf[orderId].paymentMethod)) {
                        statusUpdate = ORDER_STATUS.WAIT_FOR_PAYMENT
                      } else if(stateTempIn.feedInf[orderId].timeSchedule && !confirmNow) {
                        statusUpdate = ORDER_STATUS.ON_TIMER
                      }
                      stateTempIn.feedInf[orderId].status = statusUpdate;
                      stateTempIn.feedInf[orderId].updatedAt = Date.now();

                      _.remove(stateTempIn.listFeeds, item => item === orderId);
                      stateTempIn.listFeeds.unshift(orderId);
                    }
                    return stateTempIn;
                  }
                })

      break;
    }
    case RDActionsTypes.FeedsSystem.currentShipperReceivingPush:{
      stateTemp = RDUtil.processReducerLoading(state,action,'currentShipperReceivingPush',
                {
                  onRequest:(stateTempIn)=>{
                    const orderId = action.data.id;
                    stateTempIn.stateOrder = _.cloneDeep(stateTempIn.stateOrder);
                    stateTempIn.stateOrder[orderId] = action.data;
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.FeedsSystem.getCurrentOrder:{
      stateTemp = RDUtil.processReducerLoading(state,action,'getCurrentOrder',
                {
                  onSuccess: (stateTempIn) => {
                    stateTempIn.currentOrder = action.data.res.data.currentOrder;
                    stateTempIn.currentBike = action.data.res.data.currentOrderBike;
                    stateTempIn.currentOrderStore = action.data.res.data.currentOrderStore;
                    return stateTempIn;
                  },
                })
      break;
    }
    case RDActionsTypes.FeedsSystem.getCurrentState:{
      stateTemp = RDUtil.processReducerLoading(state,action,'getCurrentState',
                {
                  onRequest: (stateTempIn) => {
                    const orderId = action.data.id;
                    delete stateTempIn.stateOrder[orderId];
                    return stateTempIn;
                  },
                  onSuccess:(stateTempIn)=>{
                    const orderId = action.data.arg.id;
                    stateTempIn.stateOrder[orderId] = action.data.res.data;
                    return stateTempIn;
                  }
                })
      break;
    }

    case RDActionsTypes.FeedsSystem.getCurrentStateBike: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'getCurrentStateBike',
        {
          onRequest: (stateTempIn) => {
            const orderId = action.data.id;
            delete stateTempIn.stateOrder[orderId];
            return stateTempIn;
          },
          onSuccess: (stateTempIn) => {
            const orderId = action.data.arg.id;
            stateTempIn.stateOrder[orderId] = action.data.res.data;
            return stateTempIn;
          }
        })
      break;
    }

    case RDActionsTypes.FeedsSystem.clear:{
      stateTemp = RDUtil.processReducerLoading(state,action,'clear',
                {
                  onRequest:(stateTempIn)=>{
                    stateTempIn.feedInf={};
                    stateTempIn.listFeeds=[];
                    return stateTempIn;
                  }
                })
      break;
    }

    case RDActionsTypes.FeedsSystem.clearOrderSeaching:{
      stateTemp = RDUtil.processReducerLoading(state,action,'clearOrderSeaching',
                {
                  onRequest:(stateTempIn)=>{
                    stateTempIn.feedsOrderSystem=[];
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.FeedsSystem.listOrderSearching:{
      stateTemp = RDUtil.processReducerLoading(state,action,'listOrderSearching',
                {
                  onRequest: (stateTempIn)=>{
                    stateTempIn.feedsOrderSystem = [];

                    return stateTempIn;
                  },
                  onSuccess:(stateTempIn)=>{
                    action.data.res.data.forEach((feed) => {
                      stateTempIn.feedsOrderSystem.push(feed);
                    });
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.FeedsSystem.doneOrder:{
      stateTemp = RDUtil.processReducerLoading(state,action,'doneOrder',
                {
                  onRequest:(stateTempIn)=>{
                    for(let i = 0; i < stateTempIn.feedsOrderSystem.length; i++) {
                      if(stateTempIn.feedsOrderSystem[i]._id === action.data) {
                        stateTempIn.feedsOrderSystem.splice(i,1);
                        i--;
                      }
                    }
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.FeedsSystem.newOrder:{
      stateTemp = RDUtil.processReducerLoading(state,action,'newOrder',
                {
                  onRequest:(stateTempIn)=>{
                    action.data._id = action.data.id;
                    let isExists = false;
                    for(let i=0; i< stateTempIn.feedsOrderSystem.length; i++) {
                      if(stateTempIn.feedsOrderSystem[i]._id === action.data._id) {
                        isExists = true;
                        stateTempIn.feedsOrderSystem[i] = action.data;
                        break;
                      }
                    }

                    if(!isExists) {
                      stateTempIn.feedsOrderSystem.unshift(action.data);
                    }
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.FeedsSystem.changeReceiverInfo:{
      stateTemp = RDUtil.processReducerLoading(state,action,'changeReceiverInfo',
                {
                  onRequest: (stateTempIn) => {
                    stateTempIn.isListing = true
                    return stateTempIn;
                  },
                  onSuccess:(stateTempIn)=>{
                    const feedData = _.cloneDeep(stateTempIn.feedInf[action.data.arg.id].destination_places)
                    feedData[action.data.arg.index].receiver = action.data.arg.receiver;
                    stateTempIn.feedInf[action.data.arg.id].destination_places = feedData;
                    if(stateTempIn.isListing) {
                      stateTempIn.isListing = false
                    }
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.FeedsSystem.rating:{
      stateTemp = RDUtil.processReducerLoading(state,action,'rating',
                {
                  onSuccess:(stateTempIn)=>{
                    if(action.data.arg.orderId && stateTempIn.feedInf[action.data.arg.orderId]) {
                      stateTempIn.feedInf[action.data.arg.orderId].canRate = 0;
                      stateTempIn.feedInf[action.data.arg.orderId].rate = {
                        ...stateTempIn.feedInf[action.data.arg.orderId].rate,
                        mark: action.data.arg.mark
                      }
                    }
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.FeedsSystem.ratingStar:{
      stateTemp = RDUtil.processReducerLoading(state,action,'ratingStar',
                {
                  onRequest:(stateTempIn)=>{
                    if(action.data.id && stateTempIn.feedInf[action.data.id]) {
                      stateTempIn.feedInf[action.data.id].starRating = action.data.starRating
                    }
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.FeedsSystem.getNewOrderMessage:{
      stateTemp = RDUtil.processReducerLoading(state,action,'getNewOrderMessage',
                {
                  onSuccess:(stateTempIn)=>{
                    stateTempIn.listFeeds.forEach((orderId) => {
                      if(action.data.arg.mode && action.data.arg.mode === 'shipper') {
                        if(stateTempIn.feedInf[orderId].shop && _.includes(action.data.res.data,stateTempIn.feedInf[orderId].shop._id)) {
                          stateTempIn.feedInf[orderId].newMessage = true;
                        }
                      } else {
                        if(stateTempIn.feedInf[orderId].shipper && _.includes(action.data.res.data,stateTempIn.feedInf[orderId].shipper._id)) {
                          stateTempIn.feedInf[orderId].newMessage = true;
                        }
                      }
                    })
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.FeedsSystem.checkNewOrderMessage:{
      stateTemp = RDUtil.processReducerLoading(state,action,'checkNewOrderMessage',
                {
                  onRequest:(stateTempIn)=>{
                    if(stateTempIn.feedInf[action.data.id] && !stateTempIn.feedInf[action.data.id].newMessage) {
                      stateTempIn.feedInf[action.data.id].newMessage = true;
                    }
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.FeedsSystem.seenNewMessageOrder:{
      stateTemp = RDUtil.processReducerLoading(state,action,'seenNewMessageOrder',
                {
                  onRequest:(stateTempIn)=>{
                    stateTempIn.listFeeds.forEach((orderId) => {
                      if(action.data.mode && action.data.mode === 'shipper') {
                        if(stateTempIn.feedInf[orderId].shop && stateTempIn.feedInf[orderId].newMessage && action.data.senderId === stateTempIn.feedInf[orderId].shop._id) {
                          stateTempIn.feedInf[orderId].newMessage = false;
                        }
                      } else {
                        if(stateTempIn.feedInf[orderId].shipper && stateTempIn.feedInf[orderId].newMessage && action.data.senderId === stateTempIn.feedInf[orderId].shipper._id) {
                          stateTempIn.feedInf[orderId].newMessage = false;
                        }
                      }
                    })
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.FeedsSystem.addImageOrder:{
      stateTemp = RDUtil.processReducerLoading(state,action,'addImageOrder',
                {
                  onRequest:(stateTempIn)=>{
                    const objImage = action.data.imageObj;
                    const orderId = action.data.id;

                    let orderUpdateStr;
                    if (objImage.status === ORDER_STATUS.SHIPPING) {
                      orderUpdateStr = 'takeOrderInf';
                    } else if (objImage.status === ORDER_STATUS.DONE) {
                      orderUpdateStr = 'doneOrderInf';
                    } else if (objImage.status === ORDER_STATUS.DO_NOT_GET_GOODS) {
                      orderUpdateStr = 'cantTakeOrderInf';
                    } else if (objImage.status === ORDER_STATUS.TURNING_ORDER) {
                      orderUpdateStr = 'startReturningOrderInf';
                    } else if (objImage.status === ORDER_STATUS.TURNING_ORDER_DONE) {
                      orderUpdateStr = 'returnDoneOrderInf';
                    } else if (objImage.status === ORDER_STATUS.START_RETURN) {
                      orderUpdateStr = 'startReturnOrderInf';
                    } else if (objImage.status === ORDER_STATUS.FINISH_RETURN) {
                      orderUpdateStr = 'finishReturnOrderInf';
                    }

                    if (stateTempIn.feedInf[orderId][orderUpdateStr] && stateTempIn.feedInf[orderId][orderUpdateStr].images) {
                      stateTempIn.feedInf[orderId][orderUpdateStr].images.push(objImage);
                    } else if (!stateTempIn.feedInf[orderId][orderUpdateStr]) {
                      stateTempIn.feedInf[orderId][orderUpdateStr] = {
                        images: [
                          objImage
                        ]
                      };
                    } else if (!stateTempIn.feedInf[orderId][orderUpdateStr].images || stateTempIn.feedInf[orderId][orderUpdateStr].images.length === 0) {
                      stateTempIn.feedInf[orderId][orderUpdateStr].images = [];
                      stateTempIn.feedInf[orderId][orderUpdateStr].images.push(objImage);
                    }
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.FeedsSystem.getConfigImageOrderForShop:{
      stateTemp = RDUtil.processReducerLoading(state,action,'getConfigImageOrderForShop',
                {
                  onSuccess:(stateTempIn) => {
                    stateTempIn.configImageOrderForShop = action.data.res.data;
                    stateTempIn.configInsurance = action.data.res.configInsurance
                    return stateTempIn;
                  }
                })

      break;
    }
    case RDActionsTypes.FeedsSystem.updatePath:{
      stateTemp = RDUtil.processReducerLoading(state,action,'updatePath',
                {
                  onSuccess:(stateTempIn) => {

                    const index = action.data.arg.index
                    const orderId = action.data.arg.id
                    const status = action.data.arg.status

                    const feedData = _.cloneDeep(stateTempIn.feedInf[orderId].destination_places)

                    feedData[index].status = status;
                    stateTempIn.feedInf[orderId].destination_places = feedData;

                    return stateTempIn;
                  }
                })

      break;
    }
    case RDActionsTypes.FeedsSystem.setAuthenticateLater:{
      stateTemp = RDUtil.processReducerLoading(state,action,'setAuthenticateLater',
                {
                  onRequest:(stateTempIn)=>{
                    stateTempIn.isLater = 1
                    return stateTempIn;
                  }
                })

      break;
    }
    case RDActionsTypes.FeedsSystem.tipAfterOrderDone: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'tipAfterOrderDone',
        {
          onSuccess: (stateTempIn) => {
            if (action.data.arg.orderId && stateTempIn.feedInf[action.data.arg.orderId]) {
              stateTempIn.feedInf[action.data.arg.orderId].tipAfter = action.data.arg.moneyTip
            }

            return stateTempIn;
          }
        })
      break;
    }
    default:
      // Debug.log('ServerConnection:unknown type:'+action.type);
      break;
  }

  return stateTemp;

}


module.exports= FeedsSystem;
