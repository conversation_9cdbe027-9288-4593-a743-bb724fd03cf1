
// var RDActionsTypes = require( '../actions/RDActionsTypes');
var {ActionConst} = require( 'react-native-router-flux');
import { NavigationActions, StackActions } from 'react-navigation';
var Debug = require('../Util/Debug');

/**
 * Reducer for Navigator.
 * @param {Object} state .
 * @param {Object} action .
 * @returns {null} .
 */
function Navigator(state ={
      stack: ['LoginScreen'],
      currentScreen: {
        name: 'LoginScreen'
      },
    } , action) {

  // Debug.log(action)
  var stateTemp =state;
  switch (action.type) {
    case 'NAVIGATION_FOCUS':{
      stateTemp = Object.assign({}, state);
      stateTemp.navigating = false;
      if(!stateTemp.stack.length) {
        stateTemp.stack.push(action.routeName);
      }

      stateTemp.currentScreen.name = action.routeName;
      return  stateTemp ;
    }
    case 'STACK_CHANGE':{
      stateTemp = Object.assign({}, state);
      stateTemp.stack = action.stacks;
      return  stateTemp;
    }
    default:
      // Debug.log('ServerConnection:unknown type:'+action.type);
      return stateTemp
  }
}


module.exports= Navigator;
