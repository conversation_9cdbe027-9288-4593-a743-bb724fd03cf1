var _ = require('lodash');
// LIB
var {
  Platform,
} = require('react-native');
import { CommonActions } from '@react-navigation/native';
var {Actions, ActionConst} = require('react-native-router-flux');
var DeviceInfo = require('react-native-device-info');
var RNFS = require('react-native-fs');
// components
var Define = require('../Define');
var Debug = require('../Util/Debug');
var NotifyUtil = require('../Util/notify');

var RDActionsTypes = require( '../actions/RDActionsTypes');
var UserActions_MiddleWare = require( '../actions/UserActions_MiddleWare');
var {popupActions} = require('../components/popups/PopupManager');
import DefaultPopup from '../components/popups/DefaultPopup';
import EndSessionPopup from '../components/popups/EndSessionPopup';
var FadeDownDefaultPopup = require('../components/popups/FadeDownDefaultPopup');
import JoinGroupPopup from '../components/popups/JoinGroupPopup'

import CallNowPopup from '../components/popups/CallNowPopup'

//var
var {globalVariableManager} = require('../components/modules/GlobalVariableManager');

// var styles={
//   error:{
//     backgroundColor:'fff',
//     borderWidth:1,
//     borderColor:'#000',
//     borderRadius:4,
//     width:Define.constants.widthScreen*2/3,
//     alignItems:'center',
//   },
//   success:{
//     backgroundColor:'fff',
//     borderWidth:1,
//     borderColor:'#000',
//     borderRadius:4,
//     width:Define.constants.widthScreen*2/3,
//     alignItems:'center',
//   },
// }


/**
 * Reducer Tracker.
 * @param {Object} state .
 * @param {Object} action .
 * @returns {null} .
 */
function Tracker(state ={} , action) {
  // Debug.log('vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv',Debug.level.MARK);
  // Debug.log('Reducers:Tracker:'+action.type+':'+action.subtype+':',Debug.level.USER_TRACKER);

  switch (action.subtype) {
    case RDActionsTypes.constants.REQUEST_SUBTYPE.SUCCESS:
    switch (action.type) {
      case RDActionsTypes.Feeds.like:{
        popupActions.setRenderContentAndShow(
          DefaultPopup,
          {
            title:action.data.res.state ? 'Thích' : 'Đã thích',
            description:'Bạn '+ (action.data.res.state ? 'thích ' : 'đã thích ') + state.Feeds.mapId[action.data.arg.facebookId].facebook.name,
            description2:'(Lưu ý: dữ liệu được cập nhật vào bài đăng tới của shop)',
            onPressPopup:()=>{popupActions.popPopup()}
          });

        break;
      }
      case RDActionsTypes.Feeds.dislike:{
        popupActions.setRenderContentAndShow(
          DefaultPopup,
          {
            title:action.data.res.state ? 'Ghét' : 'Đã ghét',
            description:'Bạn '+ (action.data.res.state ? 'ghét ' : 'đã ghét ') + state.Feeds.mapId[action.data.arg.facebookId].facebook.name,
            description2:'(Lưu ý: dữ liệu được cập nhật vào bài đăng tới của shop)',
            onPressPopup:()=>{popupActions.popPopup()}
          });

        break;
      }
      default :
        break
      }
      break;
    case RDActionsTypes.constants.REQUEST_SUBTYPE.ERROR:
      Debug.log(JSON.stringify(action.data),Debug.level.DATA_ERROR);
      if (action.data.errObj && action.data.errObj.data && action.data.errObj.data.code == 1993) {
        // popupActions.setRenderContentAndShow(
        //   DefaultPopup,
        //   {
        //     title:'',
        //     description:'Phiên đăng nhập đã hết hạn',
        //     onPressPopup:()=>{popupActions.popPopup()}
        //   })
        if(globalVariableManager.reduxManager.state.User.memberInfo?.member?.memberToken) {
          popupActions.popAllPopup();
          setTimeout(()=>{
            globalVariableManager.reduxManager.dispatch(UserActions_MiddleWare.logout())
              .then(() => {
                this.props.navigation.dispatch(
                  CommonActions.reset({
                    index: 0,
                    routes: [{ name: 'LoginScreen' }],
                  })
                );
              })
              .catch(() => {
                this.props.navigation.dispatch(
                  CommonActions.reset({
                    index: 0,
                    routes: [{ name: 'LoginScreen' }],
                  })
                );
              })
          },200)
          popupCount = _.includes(popupActions.getPopupStack(), 'EndSessionPopup')
          if(!popupCount && action.data.errObj.data.message){
            popupActions.setRenderContentAndShow(
              EndSessionPopup,
              {
                title:action.data.errObj.data.message.head,
                description:action.data.errObj.data.message.body,
              })
          }
        }
      }
      // else if (action.data.errObj && action.data.errObj.message === 'Network Error') {
      //   NotifyUtil.pushAlertTopNotify({
      //     type: 'warning',
      //     content: 'Lỗi kết nối mạng, vui lòng kiểm tra lại. Xin cảm ơn',
      //     timeClose: 3000,
      //   })
      // }

      switch (action.type) {
        case RDActionsTypes.Feeds.comment:
          if (action.data.errObj && action.data.errObj.data) {
            switch (action.data.errObj.data.code) {
              case 100:
                popupActions.setRenderContentAndShow(JoinGroupPopup,{
                  feed_id: action.data.arg.feed_id
                })
                break;
              default:
                break;
            }
          }
        default:
          // display popup error
          // if (action.data) {
          //   if (Define.constants.debug) {
          //     popupActions.setRenderContentAndShow(
          //       DefaultPopup,
          //       {
          //         title:'ERROR:'+action.type,
          //         description:JSON.stringify(action.data),
          //         onPressPopup:()=>{popupActions.popPopup()}
          //       });
          //   }
          // }
      }

      break;

    default:
  }

  var info =undefined;
  if (action.data&&action.data.res&&action.data.res.message && action.data.res.message!=='callback timeout') {
    info = action.data.res;
  }
  else if (action.data &&action.data.message && action.data.message!=='callback timeout') {
    info =action.data;
  }
  else if(action.data && action.data.errObj && action.data.errObj.data && action.data.errObj.data.message ){
    info= action.data.errObj.data;
  }
  // else if(action.data && action.data.body){
  //   info= action.data;
  // }

  //
  //
  if (info !== undefined && info.code && info.message && info.code !== 1993) {
      if(info.message.phone) {
        popupActions.setRenderContentAndShow(
        CallNowPopup,
        {
          title:info.message.head,
          description:info.message.body,
          phone:info.message.phone
        })
      } else {
        popupActions.setRenderContentAndShow(
        DefaultPopup,
        {
          title:info.message.head,
          description:info.message.body,
          onPress:() => {
            popupActions.popPopup();
            if(info.link){
              globalVariableManager.navigatorManager.handleNavigator(info.link, info.extras || {})
            }
          },
          buttonTitle:'Xong'
        })
      }
}

  //
  // Debug.log('^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^',Debug.level.MARK);

  // firstRunTracker=false;
  globalVariableManager.reduxManager.setState(state);

  return state;
}



module.exports= Tracker;
