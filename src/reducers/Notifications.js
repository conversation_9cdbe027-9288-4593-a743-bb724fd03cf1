var _ = require('lodash')

var RDActionsTypes = require( '../actions/RDActionsTypes');

// components
var Define = require('../Define');
var Debug = require('../Util/Debug');
var Util = require('../Util/Util');
var RDUtil = require('./RDUtil');
// NOTE : if want to use promise of middleware action , this reducer must update state to a temp to use in then of promise
// =>no no no , only need update state variable from reduxManager in then funciton   (maybe because pointer changed)

function initLoading(){
  let retObj={};
  Object.keys(RDActionsTypes.Notifications).forEach((key)=>{
    if (key === 'constants') { return;}
    retObj[key] = {loading:0};
  })
  return retObj;
}

var notifyFormat={
  _id:'',
  title:'',
  description:'',
  link:'',
  icon:'',
  extras:{},
  notifiedAt:0,
}

function Notifications(state ={
                ...initLoading(),
                list: [
                ],
                count: 0
              } , action) {
  var stateTemp =state;
  switch (action.type) {
    case RDActionsTypes.Notifications.clear:{
      stateTemp = RDUtil.processReducerLoading(state,action,'add',
                {
                  onRequest: (stateTempIn) => {
                    stateTempIn.list = [];
                    return stateTempIn;
                  }
                })
      break;
    }

    case RDActionsTypes.Notifications.add:{
      stateTemp = RDUtil.processReducerLoading(state,action,'add',
                {
                  onRequest: (stateTempIn) => {
                    stateTempIn.list.unshift(action.data);
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.Notifications.removeAllNotify: {
      stateTemp = RDUtil.processReducerLoading(state,action,'removeAllNotify',
                {
                  onRequest: (stateTempIn) => {
                    stateTempIn.list = [];

                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.Notifications.notify: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'notify', {
        onSuccess: (stateTempIn) => {
          const newList = [];
          action.data.res.data.map(noti => {
            const notiObj = {
              _id: noti._id,
              category: _.get(noti, 'category', []),
              notifiedAt: noti.createdAt,
              title: _.get(noti, 'title', ''),
              description: _.get(noti, 'message', ''),
              icon: _.get(noti, 'data.image', ''),
              link: _.get(noti, 'data.link', ''),
              seen: _.get(noti, 'hasSeen', false),
              extras: _.get(noti, 'data.extras', {}),
              type: _.get(noti, 'type', ''),
              actionUrl: _.get(noti, 'data.actionUrl', '')
            };
            newList.push(notiObj);
          });
          stateTempIn.list = newList;
          return stateTempIn;
        }
      });
      break;
    }
    case RDActionsTypes.Notifications.refreshNotify: {
      stateTemp = RDUtil.processReducerLoading(state,action,'refreshNotify',
                {
                  onSuccess:(stateTempIn)=>{
                    const list = []
                    action.data.res.data.map(noti => {
                      const notiObj = {
                        _id: noti._id,
                        notifiedAt: noti.createdAt,
                        title: _.get(noti, 'title', ''),
                        description: _.get(noti, 'message', ''),
                        icon: _.get(noti, 'data.image', ''),
                        link: _.get(noti, 'data.link', ''),
                        seen: _.get(noti, 'hasSeen', false),
                        extras: _.get(noti, 'data.extras', {}),
                        type: _.get(noti, 'type', ''),
                        actionUrl: _.get(noti, 'data.actionUrl', '')
                      }

                      list.push(notiObj);
                    });
                    stateTempIn.list = list
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.Notifications.updateSeen: {
      stateTemp = RDUtil.processReducerLoading(state,action,'updateSeen',
                {
                  onRequest: (stateTempIn) => {
                    stateTempIn.list.map((noti, index) => {
                      if (noti._id === action.data.id) {
                        stateTempIn.list[index].seen = true;
                      }
                    })

                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.Notifications.countNotify: {
      stateTemp = RDUtil.processReducerLoading(state,action,'countNotify',
                {
                  onSuccess: (stateTempIn) => {
                    stateTempIn.count = action.data.res.data;

                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.Notifications.updateCount: {
      stateTemp = RDUtil.processReducerLoading(state,action,'updateCount',
                {
                  onRequest: (stateTempIn) => {
                    stateTempIn.list.map((noti, index) => {
                      if (noti._id === action.data.id && noti.type !== 'special') {
                        stateTempIn.count--;
                      }
                    })

                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.Notifications.seenAllNotify: {
      stateTemp = RDUtil.processReducerLoading(state,action,'seenAllNotify',
                {
                  onRequest: (stateTempIn) => {
                    stateTempIn.list.map(noti => {
                      noti.seen = true;
                    });
                    stateTempIn.count = 0;

                    return stateTempIn;
                  }
                })
      break;
    }
    default:
      // Debug.log('ServerConnection:unknown type:'+action.type);
      break;
  }

  return stateTemp;

}


module.exports = Notifications;
