var _ = require('lodash')

var RDActionsTypes = require( '../actions/RDActionsTypes');
var jsoncompress = require('jsoncompress');
// components
var Define = require('../Define');
var Debug = require('../Util/Debug');
var Util = require('../Util/Util');
var RDUtil = require('./RDUtil');

var {globalVariableManager} = require('../components/modules/GlobalVariableManager')
var locationManager = require('../components/modules/LocationManager')
// NOTE : if want to use promise of middleware action , this reducer must update state to a temp to use in then of promise
// =>no no no , only need update state variable from reduxManager in then funciton   (maybe because pointer changed)

function initLoading(){
  let retObj={};
  Object.keys(RDActionsTypes.Feeds).forEach((key)=>{
    if (key === 'constants') { return;}
    retObj[key] = {loading:0};
  })
  return retObj;
}

var feedFormat={
  message:'',
  from:{
    name:'',
    id:'',
    realId: '',
    totalPost: 1
  },
  created_time:'',
  messageForSearch:'',
  id:'',
  feed_id:'',
  location: {
    lat: 0,
    lng: 0
  },
  full_picture:'',
  phone: "",
  salary: 0,
  deposit: 0,
  isOpen: 0,
  type: 0
}

var feedSystemFormat = {
  _id: '',
  deposit: 0,
  salary: 0,
  note: '',
  phone: '',
  status: 0,
  created_time: 0,
  destination_places: [
  ],
  origin_place: {
    address: '',
    geometry: {
      ...Define.constants.defaultLocation
    }
  }
}

const templateWhole = {
  "message": "",
  "from": {
      "name": "",
      "id": "",
      "totalPost": 1,
      "realId": ""
  },
  "created_time": "",
  "id": "",
  "location": {
      "lat": 0,
      "lng": 0
  },
  "full_picture": "",
  "phone": "",
  "salary": 0,
  "deposit": 0,
  "isOpen": 0,
  "type": 0
}

var commentFormat = {
  "message": "",
  "from": {
    "name": "",
    "id": ""
  },
  "created_time": "",
}

function Feeds(state ={
                ...initLoading(),
                feedsSaved: [],
                feeds:[],
                comment:{},
                feedsSystemCreated: [],
                mapId: {}
              } , action) {
  var stateTemp =state;
  switch (action.type) {
    case RDActionsTypes.Feeds.getViaEvent: {
      stateTemp = RDUtil.processReducerLoading(state,action,'getViaEvent',
                {
                  onRequest:(stateTempIn)=>{
                    return state;
                  },
                  onSuccess:(stateTempIn)=>{
                    const currentTimeMil = Date.now();
                    action.data.res.feeds.forEach((feedTemp)=>{
                      feedTemp = jsoncompress.decompress(feedTemp, templateWhole);
                      feedTemp.messageForSearch = Util.change_alias(feedTemp.message);
                      feedTemp.receiver_time = currentTimeMil;
                      feedTemp.created_time_text = Util.formatDateFromNow(feedTemp.created_time);
                      stateTempIn.feeds.unshift(feedTemp);
                      if(feedTemp.type !== 0) {
                        feedTemp.internalId = feedTemp.id + Date.now()
                      }
                    });

                    stateTempIn.feeds = stateTempIn.feeds.slice(0,
                      globalVariableManager.reduxManager ? globalVariableManager.reduxManager.state.AppSetting.numberFeedsCache : 200)
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.Feeds.get:{
      stateTemp = RDUtil.processReducerLoading(state,action,'get',
                {
                  onRequest:(stateTempIn)=>{
                    stateTempIn.feeds = [];
                    return state;
                  },
                  onSuccess:(stateTempIn)=>{
                    const currentTimeMil = Date.now();
                    stateTempIn.feeds = [];
                    action.data.res.feeds.forEach((feedTemp)=>{
                      feedTemp = Util.dataProtectAndMap(feedTemp, feedFormat);
                      feedTemp.messageForSearch = Util.change_alias(feedTemp.message);
                      feedTemp.receiver_time = currentTimeMil;
                      feedTemp.created_time_text = Util.formatDateFromNow(feedTemp.created_time);
                      stateTempIn.feeds.push(feedTemp)
                    });

                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.Feeds.getFeedsSaved:{
      stateTemp = RDUtil.processReducerLoading(state,action,'getFeedsSaved',
                {
                  onRequest:(stateTempIn)=>{
                    if(!action.data.arg.from){
                      stateTempIn.feedsSaved = [];
                    }
                    return stateTempIn;
                  },
                  onSuccess:(stateTempIn)=>{
                    let updateMemberInfoDone ={};
                    action.data.res && action.data.res.feeds && action.data.res.feeds.forEach((current)=>{
                      // let id2Process = _.get(current,'from.memberInfo.facebook.id',current.from.id)
                      // if(id2Process && !updateMemberInfoDone[id2Process]) {
                      //   updateMemberInfoDone[id2Process] = true;
                      //   stateTempIn.mapId[id2Process] = Util.dataProtectAndMap(current.from.memberInfo, memberInfoRealId);
                      // }
                      stateTempIn.feedsSaved.push(Util.dataProtectAndMap(current,feedFormat))
                    })
                    // stateTempIn.feeds = stateTempIn.feeds.slice(0,50)
                    return stateTempIn;
                  }
                })

      break;
    }
    case RDActionsTypes.Feeds.getHistoryOrder:{
      stateTemp = RDUtil.processReducerLoading(state,action,'getHistoryOrder',
                {
                })
      break;
    }
    case RDActionsTypes.Feeds.deleteFeedSaved:{
      stateTemp = RDUtil.processReducerLoading(state,action,'deleteFeedSaved',
                {
                  onSuccess:(stateTempIn)=>{
                    let index = _.findIndex(stateTempIn.feedsSaved, {
                      feed_id: action.data.arg.feed_id
                    });

                    if(index !== -1) {
                      stateTempIn.feedsSaved.splice(index, 1);
                    }

                    return stateTempIn;
                  }
                })

      break;
    }
    case RDActionsTypes.Feeds.deleteAllFeedsSaved:{
      stateTemp = RDUtil.processReducerLoading(state,action,'deleteAllFeedsSaved',
                {
                  onSuccess:(stateTempIn)=>{
                    stateTempIn.feedsSaved = [];

                    return stateTempIn;
                  }
                })

      break;
    }
    case RDActionsTypes.Feeds.comment:{
      stateTemp = RDUtil.processReducerLoading(state,action,'comment',
                {
                  onRequest:(stateTempIn) => {
                    let obj = action.data.arg;
                    let extras = action.data.extras;

                    let messageObject = {
                      message: obj.message,
                      from: {
                        name: extras.name,
                        id: extras.id
                      },
                      created_time: extras.created_time,
                      id: extras.created_time
                    }
                    if(extras.type === 'normal') {
                      if (!stateTempIn.comment[obj.feed_id]) {
                        stateTempIn.comment[obj.feed_id]= [];
                      }
                      stateTempIn.comment[obj.feed_id].push(messageObject);
                    } else if (extras.type === 'reply') {
                      let index = _.findIndex(stateTempIn.comment[obj.feed_id], {
                        id: obj.comment_id
                      });
                      stateTempIn.comment[obj.feed_id][index].comments = stateTempIn.comment[obj.feed_id][index].comments || {};
                      stateTempIn.comment[obj.feed_id][index].comments.data = stateTempIn.comment[obj.feed_id][index].comments.data || [];
                      if(index !== -1) {
                        stateTempIn.comment[obj.feed_id][index].comments.data.push(messageObject);
                      }
                    }
                    return stateTempIn;
                  },
                  onSuccess:(stateTempIn)=>{
                    return stateTempIn;
                  },
                  onError: (stateTempIn)=>{
                    let obj = action.data.arg;
                    let extras = action.data.extras;
                    if(extras.type === 'normal') {
                      let index = _.findIndex(stateTempIn.comment[obj.feed_id], {
                        id: extras.created_time
                      });

                      if(index !== -1) {
                        stateTempIn.comment[obj.feed_id].splice(index, 1);
                      }
                    } else if (extras.type === 'reply') {
                      let index = _.findIndex(stateTempIn.comment[obj.feed_id], {
                        id: obj.comment_id
                      });

                      if(index !== -1) {
                        stateTempIn.comment[obj.feed_id][index].comments = stateTempIn.comment[obj.feed_id][index].comments || {};
                        stateTempIn.comment[obj.feed_id][index].comments.data = stateTempIn.comment[obj.feed_id][index].comments.data || [];
                        let indexMessage = _.findIndex(stateTempIn.comment[obj.feed_id][index].comments.data, {
                          id: extras.created_time
                        });
                        if(indexMessage !== -1) {
                          stateTempIn.comment[obj.feed_id][index].comments.data.splice(indexMessage, 1);
                        }
                      }
                    }

                    return stateTempIn;
                  },
                })

      break;
    }
    case RDActionsTypes.Feeds.getComments:{
      stateTemp = RDUtil.processReducerLoading(state,action,'getComments',
                {
                  onRequest: (stateTempIn) => {
                    return stateTempIn;
                  },
                  onSuccess:(stateTempIn)=>{
                    let data = [];
                    stateTempIn.comment[action.data.arg.feed_id] = [];
                    action.data.res.comments.data.forEach(comment => {
                      stateTempIn.comment[action.data.arg.feed_id].push(Util.dataProtectAndMap(comment, commentFormat))
                    });

                    return stateTempIn;
                  }
                })

      break;
    }

    case RDActionsTypes.Feeds.getFeedSystem:{
      stateTemp = RDUtil.processReducerLoading(state,action,'getFeedSystem',
                {
                  onRequest: (stateTempIn) => {
                    stateTempIn.feedsSystemCreated = []
                    return stateTempIn;
                  },
                  onSuccess:(stateTempIn)=>{
                    stateTempIn.feedsSystemCreated = action.data.res.feedsSystem.map((feedSystem) => {
                      const temp = Util.dataProtectAndMap(feedSystem, feedSystemFormat);
                      temp.error = feedSystem.error || null
                      temp.result = feedSystem.result || null
                      return temp
                    })

                    return stateTempIn;
                  }
                })

      break;
    }
    case RDActionsTypes.Feeds.like:{
      stateTemp = RDUtil.processReducerLoading(state,action,'like',
                {
                  onSuccess:(stateTempIn)=>{
                    if (!stateTempIn.mapId[action.data.arg.facebookId]) {
                      stateTempIn.mapId[action.data.arg.facebookId] = Util.dataProtectAndMap({}, memberInfoRealId);
                    }
                    if (action.data.res.state) {
                      stateTempIn.mapId[action.data.arg.facebookId].likes += 1;
                    }
                    return stateTempIn;
                  }
                })

      break;
    }
    case RDActionsTypes.Feeds.dislike:{
      stateTemp = RDUtil.processReducerLoading(state,action,'dislike',
                {
                  onSuccess:(stateTempIn)=>{
                    if (!stateTempIn.mapId[action.data.arg.facebookId]) {
                      stateTempIn.mapId[action.data.arg.facebookId] = Util.dataProtectAndMap({}, memberInfoRealId);
                    }
                    if (action.data.res.state) {
                      stateTempIn.mapId[action.data.arg.facebookId].dislikes += 1;
                    }
                    return stateTempIn;
                  }
                })

      break;
    }
    case RDActionsTypes.Feeds.clear:{
      stateTemp = RDUtil.processReducerLoading(state,action,'clear',
                {
                  onRequest: (stateTempIn) => {
                    stateTempIn.feedsSaved = [];
                    return stateTempIn;
                  }
                })

      break;
    }

    case RDActionsTypes.Feeds.clearNewFeed:{
      stateTemp = RDUtil.processReducerLoading(state,action,'clearNewFeed',
                {
                  onRequest: (stateTempIn) => {
                    stateTempIn.feeds = [];
                    return stateTempIn;
                  }
                })

      break;
    }

    default:
      // Debug.log('ServerConnection:unknown type:'+action.type);
      break;
  }

  return stateTemp;

}


module.exports= Feeds;
