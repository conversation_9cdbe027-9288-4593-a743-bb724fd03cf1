var _ = require('lodash');

var RDActionsTypes = require('../actions/RDActionsTypes');

// components
var Define = require('../Define');
var Debug = require('../Util/Debug');
var Util = require('../Util/Util');
var RDUtil = require('./RDUtil');
// NOTE : if want to use promise of middleware action , this reducer must update state to a temp to use in then of promise
// =>no no no , only need update state variable from reduxManager in then funciton   (maybe because pointer changed)

function initLoading() {
  let retObj = {};
  Object.keys(RDActionsTypes.IOCHB).forEach(key => {
    if (key === 'constants') {
      return;
    }
    retObj[key] = {loading: 0};
  });
  return retObj;
}

function IocHB(
  state = {
    ...initLoading(),
    attendanceStatus: {},
    leaveRequestList: [],
    attendanceStatistic: {},
    workSchedule: [],
    listOnDutyOfficer: {},
    listTotalOfficer: {},
    attendance: {},
    documentSummary: {},
    reportSummary: {},
    reportByArea: {},
    groupedSchedule: {},
    areaList: [],
    reportIncidentHighlight: {},
    detailIdentitySummary: {},
    detailDocumentSummary: {},
    detailProtectionSummary: {},
    detailLicensePlateSummary: {},
    reportListByArea: [],
    categoryList: [],
    reportListByCategory: [],
    latestIncident: {},
    otherIncident: {},
    missionList: [],
    missionDetail: {},
    missionLog: [],
    personnelList: [],
  },
  action,
) {
  var stateTemp = state;

  switch (action.type) {
    case RDActionsTypes.IOCHB.listPersonnel: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'listPersonnel', {
        onSuccess: stateTempIn => {
          let result = action.data.res.data || [];
          let arr = stateTempIn.personnelList || [];
          result.length &&
            result.map((item, index) => {
              if (_.some(arr, {_id: item._id})) {
                return;
              }
              arr.push(item);
            });
          stateTempIn.personnelList = arr;
          return stateTempIn;
        },
      });
      break;
    }
    case RDActionsTypes.IOCHB.clearListPersonnel: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'clearListPersonnel', {
        onRequest: stateTempIn => {
          stateTempIn.personnelList = [];
          return stateTempIn;
        },
      });
      break;
    }
    case RDActionsTypes.IOCHB.listMission: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'listMission', {
        onSuccess: stateTempIn => {
          let page = action.data.arg.page;
          let data = action.data.res.data;
          if (page > 1) {
            stateTempIn.missionList = stateTempIn.missionList.concat(data);
          } else {
            stateTempIn.missionList = data;
          }
          return stateTempIn;
        },
      });
      break;
    }
    case RDActionsTypes.IOCHB.clearListMission: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'clearListMission', {
        onRequest: stateTempIn => {
          stateTempIn.missionList = [];
          return stateTempIn;
        },
      });
      break;
    }
    case RDActionsTypes.IOCHB.getMission: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'getMission', {
        onRequest: stateTempIn => {
          stateTempIn.missionDetail = {};
          return stateTempIn;
        },
        onSuccess: stateTempIn => {
          let data = action.data.res.data;
          stateTempIn.missionDetail = data;
          if (stateTempIn.missionList.length) {
            if(_.some(stateTempIn.missionList, {_id: data._id})) {
              let index = _.findIndex(stateTempIn.missionList, {_id: data._id})
              stateTempIn.missionList[index] = data
            }
            else {
              stateTempIn.missionList.unshift(data)
            }
          }
          return stateTempIn;
        },
      });
      break;
    }
    case RDActionsTypes.IOCHB.getMissionLog: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'getMission', {
        onRequest: stateTempIn => {
          stateTempIn.missionLog = [];
          return stateTempIn;
        },
        onSuccess: stateTempIn => {
          let data = action.data.res.data;
          stateTempIn.missionLog = data;
          return stateTempIn;
        },
      });
      break;
    }
    case RDActionsTypes.IOCHB.attendanceStatus: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'attendanceStatus',
        {
          onSuccess: stateTempIn => {
            stateTempIn.attendanceStatus = action.data.res.data;
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IOCHB.attendanceStatistic: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'attendanceStatistic',
        {
          onSuccess: stateTempIn => {
            stateTempIn.attendanceStatistic = action.data.res.data;
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IOCHB.leaveRequestList: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'leaveRequestList',
        {
          onSuccess: stateTempIn => {
            let page = action.data.arg.page;
            let data = action.data.res.data.requests;
            if (page > 1) {
              stateTempIn.leaveRequestList =
                stateTempIn.leaveRequestList.concat(data);
            } else {
              stateTempIn.leaveRequestList = data;
            }
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IOCHB.getWorkSchedule: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'getWorkSchedule',
        {
          onSuccess: stateTempIn => {
            let data = action.data.res.data;
            stateTempIn.workSchedule = data;

            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IOCHB.listStatisticOnDutyOfficer: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'listStatisticOnDutyOfficer',
        {
          onSuccess: stateTempIn => {
            let data = action.data.res.data;
            stateTempIn.listOnDutyOfficer = data;

            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IOCHB.listStatisticTotalOfficer: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'listStatisticTotalOfficer',
        {
          onSuccess: stateTempIn => {
            let data = action.data.res.data;
            stateTempIn.listTotalOfficer = data;

            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IOCHB.listStatisticAttendance: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'listStatisticAttendance',
        {
          onSuccess: stateTempIn => {
            let data = action.data.res.data;
            stateTempIn.attendance = data;

            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IOCHB.listAttendanceStatistic: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'listAttendanceStatistic',
        {
          onSuccess: stateTempIn => {
            let data = action.data.res.data;
            stateTempIn.attendanceStatistic = data;

            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IOCHB.detailReportIdentitySummary: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'detailReportIdentitySummary',
        {
          onSuccess: stateTempIn => {
            let data = action.data.res.data;
            stateTempIn.detailIdentitySummary = data;

            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IOCHB.detailReportDocumentSummary: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'detailReportDocumentSummary',
        {
          onSuccess: stateTempIn => {
            let data = action.data.res.data;
            stateTempIn.detailDocumentSummary = data;

            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IOCHB.detailReportProtectionSummary: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'detailReportProtectionSummary',
        {
          onSuccess: stateTempIn => {
            let data = action.data.res.data;
            stateTempIn.detailProtectionSummary = data;

            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IOCHB.detailReportLicensePlateSummary: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'detailReportLicensePlateSummary',
        {
          onSuccess: stateTempIn => {
            let data = action.data.res.data;
            stateTempIn.detailLicensePlateSummary = data;

            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IOCHB.listStatisticDocumentSummary: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'listStatisticDocumentSummary',
        {
          onSuccess: stateTempIn => {
            let data = action.data.res.data;
            stateTempIn.documentSummary = data;

            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IOCHB.listStatisticReportSummary: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'listStatisticReportSummary',
        {
          onSuccess: stateTempIn => {
            let data = action.data.res.data;
            stateTempIn.reportSummary = data;

            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IOCHB.listStatisticReportByArea: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'listStatisticReportByArea',
        {
          onSuccess: stateTempIn => {
            let data = action.data.res.data;
            stateTempIn.reportByArea = data;

            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IOCHB.listGroupedSchedule: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'listGroupedSchedule',
        {
          onSuccess: stateTempIn => {
            let data = action.data.res.data;
            stateTempIn.groupedSchedule = data;

            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IOCHB.listArea: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'listArea', {
        onSuccess: stateTempIn => {
          let data = action.data.res.data;
          stateTempIn.areaList = data;

          return stateTempIn;
        },
      });
      break;
    }
    case RDActionsTypes.IOCHB.listReportIncidentHighlight: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'listReportIncidentHighlight',
        {
          onSuccess: stateTempIn => {
            let data = action.data.res.data;
            stateTempIn.reportIncidentHighlight = data;

            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IOCHB.listCategoryReportByArea: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'listCategoryReportByArea',
        {
          onSuccess: stateTempIn => {
            let data = action.data.res.data;
            if (!stateTempIn.reportListByArea.length) {
              stateTempIn.reportListByArea = [data];
            } else {
              stateTempIn.reportListByArea =
                stateTempIn.reportListByArea.concat(data);
            }
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IOCHB.clearListCategoryReportByArea: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'clearListCategoryReportByArea',
        {
          onRequest: stateTempIn => {
            stateTempIn.reportListByArea = [];
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IOCHB.listCategoryStatistic: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'listCategoryStatistic',
        {
          onSuccess: stateTempIn => {
            let data = action.data.res.data;
            stateTempIn.categoryList = data;
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IOCHB.listReportByCategory: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'listReportByCategory',
        {
          onSuccess: stateTempIn => {
            let data = action.data.res.data;
            if (!stateTempIn.reportListByCategory.length) {
              stateTempIn.reportListByCategory = [data];
            } else {
              stateTempIn.reportListByCategory =
                stateTempIn.reportListByCategory.concat(data);
            }
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IOCHB.clearListReportByCategory: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'clearListReportByCategory',
        {
          onRequest: stateTempIn => {
            stateTempIn.reportListByCategory = [];
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IOCHB.listLatestIncident: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'listLatestIncident',
        {
          onSuccess: stateTempIn => {
            let data = action.data.res.data;
            stateTempIn.latestIncident = data;
            return stateTempIn;
          },
        },
      );
      break;
    }
    case RDActionsTypes.IOCHB.listReportOtherIncident: {
      stateTemp = RDUtil.processReducerLoading(
        state,
        action,
        'listReportOtherIncident',
        {
          onSuccess: stateTempIn => {
            let data = action.data.res.data;
            stateTempIn.otherIncident = data;
            return stateTempIn;
          },
        },
      );
      break;
    }
    default:
      // Debug.log('ServerConnection:unknown type:'+action.type);
      break;
  }

  return stateTemp;
}

module.exports = IocHB;
