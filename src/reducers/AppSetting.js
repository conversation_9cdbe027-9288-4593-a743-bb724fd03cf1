var _ = require('lodash')


var RDActionsTypes = require( '../actions/RDActionsTypes');

// components
var Define = require('../Define');
var Debug = require('../Util/Debug');
var Util = require('../Util/Util');
var RDUtil = require('./RDUtil');
// NOTE : if want to use promise of middleware action , this reducer must update state to a temp to use in then of promise
// =>no no no , only need update state variable from reduxManager in then funciton   (maybe because pointer changed)

function initLoading(){
  let retObj={};
  Object.keys(RDActionsTypes.AppSetting).forEach((key)=>{
    if (key === 'constants') { return;}
    retObj[key] = {loading:0};
  })
  return retObj;
}

const HINT_STEP = {
  'PLACE_TOUR': 0,
  'DISTANCE_MONEY_TOUR': 1,
  'DONE': 2
}

function AppSetting(state ={
                ...initLoading(),

                currentHybridVersion:Define.constants.hybridVersion,
                mode: '',
                regionNew: '',
                lastLocation: null,
                lastTimeGetRegion: 0,
                changedQuickReceiverMess:false,
                quickReceiverMessArray:['☎','☎','☎'],
                actionPressOrder:'none',
                countTipsCash:0,
                countTipCart:0,
                showTipFeedsSaved:true,
                numberFeedsCache:200,
                numberFeedsDisplay:30,
                showAllLineFeed:true,
                keepScreenOn:true,
                onlyFacebookOrder:false,
                soundOS: true,
                setOpenFb:'Trong app',
                isShowTotalPosts:true,
                defaultOriginPlace: null,
                defaultOriginPlaces: [],
                hasSyncDefaultLocation: false,
                searchOptions:{
                  myLocationOn: true,
                  radius:3,
                  salary:0,
                  salaryOn:false,
                  deposit:0,
                  depositOn:false,
                },
                hintOrderStep: HINT_STEP.PLACE_TOUR,
                isHintPickLocation: 0,
                newFlags:{
                  sideMenuButton:true,
                  'Cài đặt':false,
                  SettingScreen_QuickReceiverMess:false,
                  SettingScreen_ShowAllLineFeed:false,
                  SettingScreen_KeepScreenOn:false,
                  SettingScreen_NumberFeedsDisplay:false,
                  SettingScreen_NumberFeedsCache:false,
                  BusinessTypeScreen:false,
                },
                countTipsImageOrder: 0,
                countSuggestLocation: 0,
                recentlyLocation:[],
                recentlyLocationBike: [],
                recentlyLocationHeyCare:[],
                recentlyOrigin:[],
                activeService: '',
                activeServiceShipper: '',
                authorization: null,
                openedPopupNoti: 0,
                openedNativeNotiPopup: 0,
                isMerchant: 0,
                listTextSearch: {
                  "609f759d0f5f34653f164ba5": [],
                  "613ef6b37b4adc4587d630e5": [],
                  '615c43506461254304f9712e': [],
                  '615ee6d19b619727c7c67100': [],
                  '615ee64d9b619727c7c670ff': [],
                  '615dd0859b619727c7c67072': [],
                  '5d4cea5468731c9493253bb9': []
                },
                currentLocation:{},
                timeGetLocation: 0,
                firstTimeGetLocation: true,
                messageChatBot:'',
                showTagStore: true,
                showTabInList: true,
                listOrderWait: [],
                senderInfo: {
                  senderAddr: {},
                  senderName: '',
                  senderPhone: ''
                },
                hideSoldout: 0,
                listGoods: [
                  'Thực Phẩm',
                  'Thời Trang',
                  'Hàng Tiêu Dùng',
                  'Gia Dụng',
                  'Phụ Kiện',
                  'Mỹ Phẩm',
                  'Khác'
                ],
                listErrandGoods: [
                  "Giấy tờ",
                  "Đồ dùng cá nhân",
                  "Thực Phẩm",
                  "Gia Dụng",
                  "Phụ Kiện",
                  "Mỹ Phẩm",
                  "Khác"
                ],
                updatedLocation: 0,
                openToolTipFirstTime: false,
                recentTool: [],
              } , action) {
  var stateTemp =state;
  switch (action.type) {
    case RDActionsTypes.User.getRegionByLatLng: {
      stateTemp = RDUtil.processReducerLoading(state,action,'getRegionByLatLng',
                {
                  onSuccess:(stateTempIn)=>{
                    stateTempIn.lastLocation = action.data.arg.location;
                    stateTempIn.regionNew = action.data.res.data;
                    stateTempIn.lastTimeGetRegion = Date.now();
                    return stateTempIn;
                  }
                })

      break;
    }
    case RDActionsTypes.AppSetting.setOpenToolTipFirstTime: {
      stateTemp = RDUtil.processReducerLoading(state,action,'setOpenToolTipFirstTime',
              {
                onRequest:(stateTempIn)=>{
                  stateTempIn.openToolTipFirstTime = action.data
                  return stateTempIn;
                }
              })
      break;
    }
    case RDActionsTypes.AppSetting.saveSetting:{
      stateTemp = RDUtil.processReducerLoading(state,action,'saveSetting',
                {
                  onRequest:(stateTempIn)=>{
                    return stateTempIn;
                  }

                })

      break;
    }
    case RDActionsTypes.AppSetting.recentStuff: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'recentStuff', {
        onRequest: stateTempIn => {
          stateTempIn.recentTool = [action.data].concat(stateTempIn.recentTool);
          return stateTempIn;
        },
      });
      break;
    }
    case RDActionsTypes.AppSetting.increaseHintOrder:{
      stateTemp = RDUtil.processReducerLoading(state,action,'increaseHintOrder',
                {
                  onRequest:(stateTempIn)=>{
                    stateTempIn.hintOrderStep++;
                    return stateTempIn;
                  }

                })

      break;
    }
    case RDActionsTypes.AppSetting.increaseHintPickLocation:{
      stateTemp = RDUtil.processReducerLoading(state,action,'increaseHintPickLocation',
                {
                  onRequest:(stateTempIn)=>{
                    stateTempIn.isHintPickLocation = 1;
                    return stateTempIn;
                  }

                })

      break;
    }
    case RDActionsTypes.AppSetting.setQuickReceiverMess:{
      stateTemp = RDUtil.processReducerLoading(state,action,'setQuickReceiverMess',
                {
                  onRequest:(stateTempIn)=>{
                    if (Array.isArray(action.data)) {
                      stateTempIn.changedQuickReceiverMess = true;
                      stateTempIn.quickReceiverMessArray = action.data;
                      stateTempIn.quickReceiverMessArray.forEach((message) => {
                        let startPointPhoneText = message.search('☎')
                        startPointPhoneText === -1 && (message += '☎');
                      })
                    }
                    return stateTempIn;
                  }

                })

      break;
    }
    case RDActionsTypes.AppSetting.setNumberFeedsCache:{
      stateTemp = RDUtil.processReducerLoading(state,action,'setNumberFeedsCache',
                {
                  onRequest:(stateTempIn)=>{
                    if (typeof(action.data)==='number') {
                      stateTempIn.numberFeedsCache = action.data;
                    }
                    return stateTempIn;
                  }

                })

      break;
    }
    case RDActionsTypes.AppSetting.setActionOrderOnpress:{
      stateTemp = RDUtil.processReducerLoading(state,action,'setActionOrderOnpress',
                {
                  onRequest:(stateTempIn)=>{
                    if (typeof(action.data)==='string') {
                      stateTempIn.actionPressOrder = action.data;
                    }
                    return stateTempIn;
                  }

                })

      break;
    }
    case RDActionsTypes.AppSetting.increaseTipsCash:{
      stateTemp = RDUtil.processReducerLoading(state,action,'increaseTipsCash',
                {
                  onRequest:(stateTempIn)=>{
                    stateTempIn.countTipsCash++;
                    return stateTempIn;
                  }

                })

      break;
    }
    case RDActionsTypes.AppSetting.increaseTipCart:{
      stateTemp = RDUtil.processReducerLoading(state,action,'increaseTipCart',
                {
                  onRequest:(stateTempIn)=>{
                    stateTempIn.countTipCart++;
                    return stateTempIn;
                  }

                })

      break;
    }
    case RDActionsTypes.AppSetting.setTipFeedsSave:{
      stateTemp = RDUtil.processReducerLoading(state,action,'setTipFeedsSave',
                {
                  onRequest:(stateTempIn)=>{
                    stateTempIn.showTipFeedsSaved = false;
                    return stateTempIn;
                  }

                })

      break;
    }
    case RDActionsTypes.AppSetting.setNumberFeedsDisplay:{
      stateTemp = RDUtil.processReducerLoading(state,action,'setNumberFeedsDisplay',
                {
                  onRequest:(stateTempIn)=>{
                    if (typeof(action.data)==='number') {
                      stateTempIn.numberFeedsDisplay = action.data;
                    }
                    return stateTempIn;
                  }

                })

      break;
    }
    case RDActionsTypes.AppSetting.setModeOpenFb:{
      stateTemp = RDUtil.processReducerLoading(state,action,'setModeOpenFb',
                {
                  onRequest:(stateTempIn)=>{
                    if (typeof(action.data)==='string') {
                      stateTempIn.setOpenFb = action.data;
                    }
                    return stateTempIn;
                  }

                })

      break;
    }
    case RDActionsTypes.AppSetting.showOnlyFacebookOrder:{
      stateTemp = RDUtil.processReducerLoading(state,action,'showOnlyFacebookOrder',
                {
                  onRequest:(stateTempIn)=>{
                    stateTempIn.onlyFacebookOrder = action.data ? true : false;
                    return stateTempIn;
                  }

                })

      break;
    }

    case RDActionsTypes.AppSetting.switchSoundOS:{
      stateTemp = RDUtil.processReducerLoading(state,action,'switchSoundOS',
                {
                  onRequest:(stateTempIn)=>{
                    stateTempIn.soundOS = action.data ? true : false;
                    return stateTempIn;
                  }

                })

      break;
    }

    case RDActionsTypes.AppSetting.showTotalPosts:{
      stateTemp = RDUtil.processReducerLoading(state,action,'showTotalPosts',
                {
                  onRequest:(stateTempIn)=>{
                    stateTempIn.isShowTotalPosts = action.data ? true : false;
                    return stateTempIn;
                  }

                })

      break;
    }
    case RDActionsTypes.AppSetting.setShowAllLineFeed:{
      stateTemp = RDUtil.processReducerLoading(state,action,'setShowAllLineFeed',
                {
                  onRequest:(stateTempIn)=>{
                    if (typeof(action.data)==='boolean') {
                      stateTempIn.showAllLineFeed = action.data;
                    }
                    return stateTempIn;
                  }

                })

      break;
    }
    case RDActionsTypes.AppSetting.setKeepScreenOn:{
      stateTemp = RDUtil.processReducerLoading(state,action,'setKeepScreenOn',
                {
                  onRequest:(stateTempIn)=>{
                    if (typeof(action.data)==='boolean') {
                      stateTempIn.keepScreenOn = action.data;
                    }
                    return stateTempIn;
                  }

                })

      break;
    }
    case RDActionsTypes.AppSetting.setSearchOptions:{
      stateTemp = RDUtil.processReducerLoading(state,action,'setSearchOptions',
                {
                  onRequest:(stateTempIn)=>{
                    // let dataTemp = Util.dataProtectAndMap
                    if (typeof(action.data)==='object') {
                      stateTempIn.searchOptions = _.merge(stateTempIn.searchOptions,action.data) ;
                    }
                    return stateTempIn;
                  }

                })

      break;
    }
    case RDActionsTypes.AppSetting.setNewFlags:{
      stateTemp = RDUtil.processReducerLoading(state,action,'setNewFlags',
                {
                  onRequest:(stateTempIn)=>{
                    // let dataTemp = Util.dataProtectAndMap
                    if (typeof(action.data)==='object') {
                      state.newFlags = _.merge(state.newFlags,action.data) ;
                    }
                    return state;
                  }

                })

      break;
    }
    case 'persist/REHYDRATE':{
      if (!action.payload.AppSetting) {
        break;
      }
      // clear loading
      Object.keys(RDActionsTypes.AppSetting).forEach((key)=>{
        if (key === 'constants') { return;}
        action.payload.AppSetting[key] = {loading:0};
      })
      // process newFlags
      if (state.currentHybridVersion !==  action.payload.AppSetting.currentHybridVersion) {
        // special merge ( only set false => true)
        if (!action.payload.AppSetting.newFlags) {
          action.payload.AppSetting.newFlags={};
        }
        Object.keys(state.newFlags).forEach((key)=>{
          if (state.newFlags[key]) {
            action.payload.AppSetting.newFlags[key] = true;
          }
        })
      }

      break;
    }
    case RDActionsTypes.AppSetting.setMode:{
      stateTemp = RDUtil.processReducerLoading(state,action,'setMode',
                {
                  onRequest:(stateTempIn)=>{
                    stateTempIn.mode = action.data.mode
                    return stateTempIn;
                  }

                })
      break;
    }
    case RDActionsTypes.AppSetting.addDefaultOrigin:{
      stateTemp = RDUtil.processReducerLoading(state,action,'addDefaultOrigin',
                {
                  onSuccess:(stateTempIn)=>{
                    if (!action.data.res.data?.name || !action.data.res.data?.location?.lat || !action.data.res.data?.location?.lng) {
                      return stateTempIn;
                    }

                    if(!stateTempIn.defaultOriginPlaces.length) {
                      action.data.res.data.active = 1;
                    }
                    stateTempIn.defaultOriginPlaces.unshift(action.data.res.data)
                    stateTempIn.defaultOriginPlaces = _.clone(stateTempIn.defaultOriginPlaces, true);
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.AppSetting.deleteDefaultOrigin:{
      stateTemp = RDUtil.processReducerLoading(state,action,'deleteDefaultOrigin',
                {
                  onSuccess:(stateTempIn)=>{
                    for (let i = 0; i < stateTempIn.defaultOriginPlaces.length; i++) {
                      if(stateTempIn.defaultOriginPlaces[i]._id && stateTempIn.defaultOriginPlaces[i]._id === action.data.arg.id) {
                        const deleteOriginLocation = stateTempIn.defaultOriginPlaces[i];
                        stateTempIn.defaultOriginPlaces.splice(i, 1);

                        if(deleteOriginLocation.active && stateTempIn.defaultOriginPlaces.length) {
                          stateTempIn.defaultOriginPlaces[0].active = 1;
                        }
                        break;
                      }
                    }
                    stateTempIn.defaultOriginPlaces = _.clone(stateTempIn.defaultOriginPlaces, true);
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.AppSetting.setDoneSyncDefaultLocation:{
      stateTemp = RDUtil.processReducerLoading(state,action,'setDoneSyncDefaultLocation',
                {
                  onRequest:(stateTempIn)=>{
                    stateTempIn.hasSyncDefaultLocation = true;
                    return stateTempIn
                  }
                })
      break;
    }
    case RDActionsTypes.AppSetting.modifyDefaultOrigin:{
      stateTemp = RDUtil.processReducerLoading(state,action,'modifyDefaultOrigin',
                {
                  onSuccess:(stateTempIn)=>{
                    const id = action.data.arg.id;
                    const data = action.data.arg;
                    for (let i = 0; i < stateTempIn.defaultOriginPlaces.length; i++) {
                      if(stateTempIn.defaultOriginPlaces[i]._id && stateTempIn.defaultOriginPlaces[i]._id === action.data.arg.id) {
                        if(data.name) {
                          stateTempIn.defaultOriginPlaces[i].name = data.name;
                        }

                        if(data.location) {
                          stateTempIn.defaultOriginPlaces[i].location = data.location;
                        }

                        if (data.nameDefault) {
                          stateTempIn.defaultOriginPlaces[i].nameDefault = data.nameDefault;
                        }

                        if (data.nameSender) {
                          stateTempIn.defaultOriginPlaces[i].nameSender = data.nameSender;
                        }

                        if (data.phoneSender) {
                          stateTempIn.defaultOriginPlaces[i].phoneSender = data.phoneSender;
                        }

                          stateTempIn.defaultOriginPlaces[i].subNameDefault = data.subNameDefault;
                          if(data.modifyRecentOrigin) {
                            var arrRecentlyOrigin = stateTempIn.recentlyOrigin
                            for(j = 0 ; j < arrRecentlyOrigin.length; j++){
                              if(stateTempIn.defaultOriginPlaces[i].name === arrRecentlyOrigin[j].name || _.isEqual(stateTempIn.defaultOriginPlaces[i].location, arrRecentlyOrigin[j].location)){
                                if(data.nameSender) {
                                  arrRecentlyOrigin[j].nameSender = data.nameSender;
                                }
                                if(data.phoneSender) {
                                  arrRecentlyOrigin[j].phoneSender = data.phoneSender;
                                }
                                if(data.subNameDefault) {
                                  arrRecentlyOrigin[j].subName = data.subNameDefault;
                                }
                              }
                            }
                          }
                      }
                    }

                    stateTempIn.defaultOriginPlaces = _.clone(stateTempIn.defaultOriginPlaces, true);
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.AppSetting.listDefaultLocation:{
      stateTemp = RDUtil.processReducerLoading(state,action,'listDefaultLocation',
                {
                  onSuccess:(stateTempIn)=>{
                    let indexActive = 0;
                    stateTempIn.defaultOriginPlaces.forEach((location, index) => {
                      if(location.active) {
                        indexActive = index
                      }
                    })

                    let data = action.data.res.data
                    data.forEach((location, index) => {
                      if(indexActive == index) {
                        location.active = 1;
                      } else {
                        location.active = 0;
                      }
                    })

                    stateTempIn.defaultOriginPlaces = data
                    stateTempIn.defaultOriginPlaces = _.clone(stateTempIn.defaultOriginPlaces, true);
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.AppSetting.modifyListDefaultLocation:{
      stateTemp = RDUtil.processReducerLoading(state,action,'modifyListDefaultLocation',
                {
                  onSuccess:(stateTempIn)=>{
                    let list = _.cloneDeep(action.data.arg?.newList)
                    stateTempIn.defaultOriginPlaces = list
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.AppSetting.unshiftDefaultLocation:{
      stateTemp = RDUtil.processReducerLoading(state,action,'unshiftDefaultLocation',
                {
                  onSuccess:(stateTempIn)=>{
                    const id = action.data.arg.id;
                    let defaultOriginPlaces = _.cloneDeep(stateTempIn.defaultOriginPlaces)
                    for (let index = 0; index < defaultOriginPlaces.length; index++) {
                      if(defaultOriginPlaces[index]._id === id) {
                        const location = _.cloneDeep(defaultOriginPlaces[index])
                        defaultOriginPlaces.splice(index, 1);
                        defaultOriginPlaces.unshift(location);
                      }
                    }
                    stateTempIn.defaultOriginPlaces = defaultOriginPlaces;
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.AppSetting.setActiveDefaultOrigin:{
      stateTemp = RDUtil.processReducerLoading(state,action,'setActiveDefaultOrigin',
                {
                  onRequest:(stateTempIn)=>{
                    const indexActive = action.data.index;

                    stateTempIn.defaultOriginPlaces.forEach((location, index) => {
                      location.active = 0;
                      if(index === indexActive) {
                        location.active = 1;
                      }
                    });

                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.AppSetting.setRegion:{
      stateTemp = RDUtil.processReducerLoading(state,action,'setRegion',
                {
                  onRequest:(stateTempIn)=>{
                    stateTempIn.region = action.data.region
                    return stateTempIn;
                  }

                })
      break;
    }
    case RDActionsTypes.AppSetting.increaseTipsImageOrder:{
      stateTemp = RDUtil.processReducerLoading(state,action,'increaseTipsImageOrder',
                {
                  onRequest:(stateTempIn)=>{
                    stateTempIn.countTipsImageOrder++;
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.AppSetting.addRecentlyLocationPick:{
      stateTemp = RDUtil.processReducerLoading(state,action,'addRecentlyLocationPick',
                {
                  onRequest:(stateTempIn)=>{
                    var arrRecentlyLocation = stateTempIn.recentlyLocation
                    for(i = 0 ; i < arrRecentlyLocation.length; i++){
                      if(action.data.name === arrRecentlyLocation[i].name || _.isEqual(action.data.location, arrRecentlyLocation[i].location)){
                        arrRecentlyLocation.splice(i, 1);
                        i--;
                      }
                    }
                    arrRecentlyLocation.unshift(action.data)
                    stateTempIn.recentlyLocation = _.clone(arrRecentlyLocation.slice(0,20), true)
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.AppSetting.addRecentlyLocationBikePick:{
      stateTemp = RDUtil.processReducerLoading(state,action,'addRecentlyLocationBikePick',
                {
                  onRequest:(stateTempIn)=>{
                    var arrRecentlyLocationBike = stateTempIn.recentlyLocationBike
                    for(i = 0 ; i < arrRecentlyLocationBike.length; i++){
                      if(action.data.name === arrRecentlyLocationBike[i].name || _.isEqual(action.data.location, arrRecentlyLocationBike[i].location)){
                        arrRecentlyLocationBike.splice(i, 1);
                        i--;
                      }
                    }
                    arrRecentlyLocationBike.unshift(action.data)
                    stateTempIn.recentlyLocationBike = _.clone(arrRecentlyLocationBike.slice(0,10), true)
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.AppSetting.addRecentlyLocationHeyCarePick:{
      stateTemp = RDUtil.processReducerLoading(state,action,'addRecentlyLocationHeyCarePick',
                {
                  onRequest:(stateTempIn)=>{
                    var arrRecentlyLocationHeyCare = stateTempIn.recentlyLocationHeyCare
                    for(i = 0 ; i < arrRecentlyLocationHeyCare.length; i++){
                      if(action.data.name === arrRecentlyLocationHeyCare[i].name || _.isEqual(action.data.location, arrRecentlyLocationHeyCare[i].location)){
                        arrRecentlyLocationHeyCare.splice(i, 1);
                        i--;
                      }
                    }
                    arrRecentlyLocationHeyCare.unshift(action.data)
                    stateTempIn.recentlyLocationHeyCare = _.clone(arrRecentlyLocationHeyCare.slice(0,10), true)
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.AppSetting.removeRecentlyLocationPick:{
      stateTemp = RDUtil.processReducerLoading(state,action,'removeRecentlyLocationPick',
                {
                  onRequest:(stateTempIn)=>{
                    stateTempIn.recentlyLocation = [];

                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.AppSetting.removeRecentlyLocationBikePick:{
      stateTemp = RDUtil.processReducerLoading(state,action,'removeRecentlyLocationBikePick',
                {
                  onRequest:(stateTempIn)=>{
                    stateTempIn.recentlyLocationBike = [];
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.AppSetting.removeRecentlyLocationHeyCarePick:{
      stateTemp = RDUtil.processReducerLoading(state,action,'removeRecentlyLocationHeyCarePick',
                {
                  onRequest:(stateTempIn)=>{
                    stateTempIn.recentlyLocationHeyCare = [];
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.AppSetting.addRecentlyOriginPick:{
      stateTemp = RDUtil.processReducerLoading(state,action,'addRecentlyOriginPick',
                {
                  onRequest:(stateTempIn)=>{
                    var arrRecentlyOrigin = stateTempIn.recentlyOrigin
                    for(i = 0 ; i < arrRecentlyOrigin.length; i++){
                      if(action.data.name === arrRecentlyOrigin[i].name || _.isEqual(action.data.location, arrRecentlyOrigin[i].location)){
                        arrRecentlyOrigin.splice(i, 1);
                        i--;
                      }
                    }
                    arrRecentlyOrigin.unshift(action.data)
                    stateTempIn.recentlyOrigin = _.clone(arrRecentlyOrigin.slice(0,3), true)
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.AppSetting.removeRecentlyOriginPick:{
      stateTemp = RDUtil.processReducerLoading(state,action,'removeRecentlyOriginPick',
                {
                  onRequest:(stateTempIn)=>{
                    stateTempIn.recentlyOrigin = [];

                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.AppSetting.changeAuthorizationStatus: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'changeAuthorizationStatus',
        {
          onRequest: (stateTempIn) => {
            stateTempIn.authorization = action.data.authorization;

            return stateTempIn;
          }
        })
      break;
    }
    case RDActionsTypes.AppSetting.chooseActiveService: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'chooseActiveService',
        {
          onRequest:(stateTempIn)=>{
            stateTempIn.activeService = action.data.id;
            stateTempIn.isMerchant = action.data.isMerchant
            return stateTempIn;
          }
        })

      break;
    }
    case RDActionsTypes.AppSetting.chooseActiveServiceForShip: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'chooseActiveServiceForShip',
        {
          onRequest:(stateTempIn)=>{
            stateTempIn.activeServiceShipper = action.data;
            return stateTempIn;
          }
        })

      break;
    }
    case RDActionsTypes.AppSetting.checkPopupPermissionNotify: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'checkPopupPermissionNotify',
        {
          onRequest:(stateTempIn)=>{
            stateTempIn.openedPopupNoti = action.data;
            return stateTempIn;
          }
        })

      break;
    }
    case RDActionsTypes.AppSetting.checkPopupNativeNotify: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'checkPopupNativeNotify',
        {
          onRequest:(stateTempIn)=>{
            stateTempIn.openedNativeNotiPopup = action.data;
            return stateTempIn;
          }
        })

      break;
    }

    case RDActionsTypes.AppSetting.recentSearchProduct: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'recentSearchProduct',
        {
          onRequest: (stateTempIn) => {
            if(action.data.serviceId && action.data.textSearch){
              var arrTextSearch = stateTempIn.listTextSearch[action.data.serviceId] || []

              if(!arrTextSearch.length || !arrTextSearch.includes(action.data.textSearch)){
                arrTextSearch.unshift(action.data.textSearch)
                stateTempIn.listTextSearch[action.data.serviceId] = _.clone(arrTextSearch.slice(0,5), true)
              }
            }
            return stateTempIn;
          }
        })

      break;
    }

    case RDActionsTypes.AppSetting.addCurrentLocation:{
      stateTemp = RDUtil.processReducerLoading(state,action,'addCurrentLocation',
                {
                  onRequest:(stateTempIn)=>{
                    if (action.data.location?.locationName
                      || action.data.location?.addressUser
                      || (action.data.location?.location?.lat && action.data.location?.location?.lng))
                      {
                        stateTempIn.currentLocation = action.data.location
                      }
                    if (action.data.time) {
                      stateTempIn.timeGetLocation = action.data.time
                    }
                    stateTempIn.firstTimeGetLocation = false
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.AppSetting.messageChatBot:{
      stateTemp = RDUtil.processReducerLoading(state,action,'messageChatBot',
                {
                  onRequest:(stateTempIn)=>{
                    stateTempIn.messageChatBot = action.data
                    return stateTempIn;
                  }
                })
      break;
    }
    case RDActionsTypes.AppSetting.changeTagStore:{
      stateTemp = RDUtil.processReducerLoading(state,action,'changeTagStore',
                {
                  onRequest:(stateTempIn)=>{
                    stateTempIn.showTagStore = false;

                    return stateTempIn;
                  }
                })
      break;
    }

    case RDActionsTypes.AppSetting.settingShowStore:{
      stateTemp = RDUtil.processReducerLoading(state,action,'settingShowStore',
        {
          onRequest: (stateTempIn) => {
            if (typeof (action.data) === 'boolean') {
              stateTempIn.showTabInList = action.data;
            }
            return stateTempIn;
          }

        })

      break;
    }

    case RDActionsTypes.AppSetting.addListOrderWait:{
      stateTemp = RDUtil.processReducerLoading(state,action,'addListOrderWait',
                {
                  onRequest:(stateTempIn)=>{
                    if(action.data){
                      var arrOrderWait = stateTempIn.listOrderWait || []

                      if(!arrOrderWait.includes(action.data)){
                        arrOrderWait.push(action.data)
                        stateTempIn.listOrderWait = _.clone(arrOrderWait, true)
                      }
                    }
                    return stateTempIn;
                  }
                })
      break;
    }

    case RDActionsTypes.AppSetting.removeListOrderWait:{
      stateTemp = RDUtil.processReducerLoading(state,action,'removeListOrderWait',
                {
                  onRequest:(stateTempIn)=>{
                    if(action.data){
                      var arrOrderWait = stateTempIn.listOrderWait || []
                      if(_.some(arrOrderWait, {id: action.data.id})){
                        arrOrderWait.splice(_.findIndex(arrOrderWait, function (o) { return o.id === action.data.id; }), 1)
                        stateTempIn.listOrderWait = _.clone(arrOrderWait, true)
                      }
                    } else{
                      var arrOrderWait = stateTempIn.listOrderWait || []
                      arrOrderWait.forEach((element, index) => {
                        if(element.time + 10 * 60 * 60 *1000  <= Date.now()){
                          arrOrderWait.splice(index, 1)
                        }
                      });
                      stateTempIn.listOrderWait = _.clone(arrOrderWait, true)
                    }
                    return stateTempIn;
                  }
                })
      break;
    }

    case RDActionsTypes.AppSetting.addSenderInf:{
      stateTemp = RDUtil.processReducerLoading(state,action,'addSenderInf',
                {
                  onRequest:(stateTempIn)=>{
                    if(!_.isEmpty(action.data) && action.data?.senderAddr?.name && action.data?.senderAddr?.location?.lat && action.data?.senderAddr?.location?.lng){
                      stateTempIn.senderInfo.senderAddr = action.data.senderAddr
                      stateTempIn.senderInfo.senderName = action.data.senderName
                      stateTempIn.senderInfo.senderPhone = action.data.senderPhone
                    }

                    return stateTempIn;
                  }

                })
      break;
    }

    case RDActionsTypes.AppSetting.changeProductDisplay:{
      stateTemp = RDUtil.processReducerLoading(state,action,'changeProductDisplay',
                {
                  onRequest:(stateTempIn)=>{
                    stateTempIn.hideSoldout = !stateTempIn.hideSoldout
                    return stateTempIn;
                  }
                })
      break;
    }

    case RDActionsTypes.AppSetting.addListGoods:{
      stateTemp = RDUtil.processReducerLoading(state,action,'addListGoods',
                {
                  onRequest:(stateTempIn)=>{
                    const goods = action.data;
                    const mergedGoods = _.union(stateTempIn.listGoods, goods);
                    stateTempIn.listGoods = mergedGoods;
                    return stateTempIn;
                  }
                })
      break;
    }

    case RDActionsTypes.AppSetting.addListErrandGoods:{
      stateTemp = RDUtil.processReducerLoading(state,action,'addListErrandGoods',
                {
                  onRequest:(stateTempIn)=>{
                    const goods = action.data;
                    const mergedGoods = _.union(stateTempIn.listErrandGoods, goods);
                    stateTempIn.listErrandGoods = mergedGoods;
                    return stateTempIn;
                  }
                })
      break;
    }


    case RDActionsTypes.AppSetting.addNewGoods:{
      stateTemp = RDUtil.processReducerLoading(state,action,'addNewGoods',
                {
                  onRequest:(stateTempIn)=>{
                    const goods = action.data;
                    const index = stateTempIn.listGoods.indexOf(goods);
                    if (index > -1) {
                      stateTempIn.listGoods.splice(index, 1);
                    }

                    stateTempIn.listGoods.unshift(goods);

                    if (stateTempIn.listGoods.length > 20) {
                      stateTempIn.listGoods.splice(stateTempIn.listGoods.length - 1, 1);
                    }

                    return stateTempIn;
                  }
                })
      break;
    }

    case RDActionsTypes.AppSetting.addNewErrandGoods:{
      stateTemp = RDUtil.processReducerLoading(state,action,'addNewErrandGoods',
                {
                  onRequest:(stateTempIn)=>{
                    const goods = action.data;
                    const index = stateTempIn.listErrandGoods.indexOf(goods);
                    if (index > -1) {
                      stateTempIn.listErrandGoods.splice(index, 1);
                    }

                    stateTempIn.listErrandGoods.unshift(goods);

                    if (stateTempIn.listErrandGoods.length > 20) {
                      stateTempIn.listErrandGoods.splice(stateTempIn.listErrandGoods.length - 1, 1);
                    }

                    return stateTempIn;
                  }
                })
      break;
    }

    case RDActionsTypes.AppSetting.changeUpdatedLocation:{
      stateTemp = RDUtil.processReducerLoading(state,action,'changeUpdatedLocation',
                {
                  onRequest:(stateTempIn)=>{
                    stateTempIn.updatedLocation = 1;
                    return stateTempIn;
                  }
                })
      break;
    }

    default:
      // Debug.log('ServerConnection:unknown type:'+action.type);
      break;
  }

  return stateTemp;

}


module.exports= AppSetting;
