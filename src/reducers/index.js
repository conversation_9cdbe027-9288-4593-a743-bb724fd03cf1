
// var Todo = require('./Todo');

import { createStore,applyMiddleware,compose, combineReducers  } from 'redux';

var Tracker = require('./Tracker');
var AppState = require('./AppState');
var Navigator = require('./Navigator');
var Store = require('./Store');
var Feeds = require('./Feeds');
var User = require('./User');
var AppSetting = require('./AppSetting');
var Tips = require('./Tips');
var Shipper = require('./Shipper')
var Package = require('./Package')
var FeedSystem = require('./FeedSystem')
var Notify = require('./Notify')
var Chat = require('./Chat')
var Notifications = require('./Notifications')
var IHeyU = require('./IHeyU')
var IocHB = require('./IocHB')

/**
 * Reducer index.
 * @param {Object} state .
 * @param {Object} action .
 * @returns {null} .
 */
export function createReducer(asyncReducers) {
  const funcInStore = combineReducers({
    AppState,
    Navigator,
    Store,
    Feeds,
    User,
    AppSetting,
    Tips,
    Shipper,
    Package,
    FeedSystem,
    Notify,
    Chat,
    Notifications,
    IHeyU,
    IocHB,
    ...asyncReducers
  })

  return (state = {}, action) => {
    const stateRet = funcInStore(state, action);

    Tracker(stateRet, action);

    return stateRet;
  }
}

let storeInstance;
export function setStoreInstance (data) {
  storeInstance = data;
  storeInstance.asyncReducers = {};
}

export function injectAsyncReducer (name, asyncReducer) {
  storeInstance.asyncReducers[name] = asyncReducer;
  storeInstance.replaceReducer(createReducer(storeInstance.asyncReducers));
}
