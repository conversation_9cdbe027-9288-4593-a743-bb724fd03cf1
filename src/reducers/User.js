var _ = require('lodash');

var RDActionsTypes = require('../actions/RDActionsTypes');

// components
var Define = require('../Define');
var Debug = require('../Util/Debug');
var Util = require('../Util/Util');
var RDUtil = require('./RDUtil');
// NOTE : if want to use promise of middleware action , this reducer must update state to a temp to use in then of promise
// =>no no no , only need update state variable from reduxManager in then funciton   (maybe because pointer changed)

function initLoading() {
  let retObj = {};
  Object.keys(RDActionsTypes.User).forEach(key => {
    if (key === 'constants') {
      return;
    }
    retObj[key] = {loading: 0};
  });
  return retObj;
}

var memberInfoFormat = {
  member: {
    facebook: {
      id: '',
      name: '',
      birthday: '',
      picture: '',
      email: '',
      token: '',
    },
    shop: {
      isAuthen: 0,
    },
    _id: '',
    memberToken: '',
    phone: '',
    os_version: {},
    name: '',
    address: '',
    email: '',
    birthday: {},

    // v2
    type: 0,
    likes: 0,
    dislikes: 0,
    coints: 0,
    expireTime: 0,
    isExpire: true,
    blockUtil: 0,
    createdAt: 0,
    updatedAt: 0,
  },
  endAddr: {},
  listDraftPetition: []
};

function User(
  state = {
    ...initLoading(),
    memberInfo: {},
    lastTracking: 0,
    tooltipCount: 0,
    tooltipVisible: true,
    memberToken: ''
  },
  action,
) {
  var stateTemp = state;

  switch (action.type) {
    case RDActionsTypes.User.setToolTipCount: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'setToolTipCount',{
        onRequest:(stateTempIn)=>{
          stateTempIn.tooltipCount = stateTempIn.tooltipCount + 1
          if (stateTempIn.tooltipCount > 2){
            stateTempIn.tooltipVisible = false
          }
          return stateTempIn;
        }
      })
      break;
    }
    case RDActionsTypes.User.saveDraftPetition: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'saveDraftPetition', {
        onRequest: stateTempIn => {
          var arr = stateTempIn.listDraftPetition || []
          arr.push(action.data)
          stateTempIn.listDraftPetition = _.cloneDeep(arr)
          return stateTempIn;
        },
      });
      break;
    }
    case RDActionsTypes.User.updateDraftPetition: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'updateDraftPetition', {
        onRequest: stateTempIn => {
          var arr = stateTempIn.listDraftPetition || []
          if (arr.length) {
            arr.map((item, index) => {
              if (item._id === action.data._id) {
                arr[index] = action.data
              }
            })
          }
          stateTempIn.listDraftPetition = _.cloneDeep(arr)
          return stateTempIn;
        },
      });
      break;
    }
    case RDActionsTypes.User.removeDraftPetition: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'removeDraftPetition', {
        onRequest: stateTempIn => {
          var arr = stateTempIn.listDraftPetition || []
          if (arr.length) {
            arr.map((item, index) => {
              if (item._id === action.data._id) {
                arr.splice(index, 1)
              }
            })
          }
          stateTempIn.listDraftPetition = _.cloneDeep(arr)
          return stateTempIn;
        },
      });
      break;
    }
    case RDActionsTypes.User.login: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'login', {
        onSuccess: stateTempIn => {
          stateTempIn.memberInfo = action.data.res.data;
          stateTempIn.memberInfo.memberToken = action.data.res.data.token
          stateTempIn.memberToken = action.data.res.data.token
          return stateTempIn
        }
      });
      break;
    }
    case RDActionsTypes.User.register: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'register', {
        onSuccess: stateTempIn => {
          stateTempIn.memberInfo = action.data.res.data;
          return stateTempIn
        }
      });
      break;
    }
    case RDActionsTypes.User.resetPassword: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'resetPassword', {
        onSuccess: stateTempIn => {
          stateTempIn.memberInfo = action.data.res.data;
          return stateTempIn
        }
      });
      break;
    }
    case RDActionsTypes.User.logout: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'logout', {
        onSuccess: stateTempIn => {
          stateTempIn.memberInfo = {};
          return stateTempIn;
        },
      });
      break;
    }
    case RDActionsTypes.User.requireLogin: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'requireLogin', {
        onRequest: stateTempIn => {
          stateTempIn.memberInfo = {};
          return stateTempIn;
        },
      });
      break;
    }
    case RDActionsTypes.User.updateProfile: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'updateProfile', {
        onSuccess: stateTempIn => {
          stateTempIn.memberInfo.name = action.data.res.data.name;
          stateTempIn.memberInfo.birthday = action.data.res.data.birthday
          stateTempIn.memberInfo.idCard = action.data.res.data.idCard
          stateTempIn.memberInfo.avatar = action.data.res.data.avatar
          stateTempIn.memberInfo.email = action.data.res.data.email

          return stateTempIn;
        },
      });
      break;
    }
    case RDActionsTypes.User.get: {
      stateTemp = RDUtil.processReducerLoading(state, action, 'get',
        {
          onSuccess: (stateTempIn) => {
            const memberInfo = Util.dataProtectAndMap(action.data.res, memberInfoFormat);
            stateTempIn.memberInfo = memberInfo.data;
            stateTempIn.memberInfo.memberToken = stateTempIn.memberToken
            return stateTempIn;
          },
          onError: (stateTempIn) => {
            return stateTempIn;
          }
        })
      break;
    }
    case RDActionsTypes.User.trackingAction:{
      stateTemp = RDUtil.processReducerLoading(state,action,'trackingAction',
        {
          onSuccess:(stateTempIn) => {
            stateTempIn.lastTracking = Date.now();
            return stateTempIn;
          },
          onError: (stateTempIn) => {
            return stateTempIn;
          }
        })

      break;
    }

    case 'persist/REHYDRATE': {
      // clear loading
      if (!action.payload.User) {
        break;
      }
      Object.keys(RDActionsTypes.User).forEach(key => {
        if (key === 'constants') {
          return;
        }
        action.payload.User[key] = {loading: 0};
      });
    }

    default:
      // Debug.log('ServerConnection:unknown type:'+action.type);
      break;
  }

  return stateTemp;
}

module.exports = User;
