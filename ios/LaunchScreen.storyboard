<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="ipad12_9rounded" orientation="portrait" layout="fullscreen" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="1024" height="1366"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" image="splashscreen" id="EiJ-18-YaL">
                                <rect key="frame" x="0.0" y="0.0" width="1024" height="1366"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <color key="tintColor" red="0.17047074270000001" green="0.39788821749999997" blue="0.82315209659999999" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                            </imageView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Bcu-3y-fUS"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="EiJ-18-YaL" firstAttribute="top" secondItem="Bcu-3y-fUS" secondAttribute="top" id="Jf3-kx-bD0"/>
                            <constraint firstItem="Bcu-3y-fUS" firstAttribute="trailing" secondItem="EiJ-18-YaL" secondAttribute="trailing" id="Teg-vo-l75"/>
                            <constraint firstItem="EiJ-18-YaL" firstAttribute="leading" secondItem="Bcu-3y-fUS" secondAttribute="leading" id="tjH-an-2XZ"/>
                            <constraint firstItem="Bcu-3y-fUS" firstAttribute="bottom" secondItem="EiJ-18-YaL" secondAttribute="bottom" id="zgs-pV-xaB"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="50.45454545454546" y="373.43096234309627"/>
        </scene>
    </scenes>
    <color key="tintColor" red="0.0" green="0.47843137250000001" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
    <resources>
        <image name="splashscreen" width="414" height="896"/>
    </resources>
</document>
