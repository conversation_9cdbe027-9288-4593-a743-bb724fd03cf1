<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Wow Thợ</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fb1913555415398264</string>
				<string>wowworker</string>
				<string>MOMOTPSC20180416</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>CodePushDeploymentKey</key>
	<string>$(CODEPUSH_KEY)</string>
	<key>CodePushServerURL</key>
	<string>https://codepush.heyu.asia</string>
	<key>FacebookAppID</key>
	<string>1913555415398264</string>
	<key>FacebookDisplayName</key>
	<string>Wow Thợ</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>zalopay</string>
		<string>zalopay.api.v2</string>
		<string>zalo</string>
		<string>fbapi</string>
		<string>fb-messenger-share-api</string>
		<string>fbauth2</string>
		<string>fbshareextension</string>
		<string>comgooglemaps</string>
		<string>VIMOWallet</string>
		<string>momo</string>
		<string>tickboxvietnam</string>
		<string>fb</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>localhost</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>$(PRODUCT_NAME) truy cập camera để chụp ảnh phản ánh hiện trường</string>
	<key>NSContactsUsageDescription</key>
	<string>$(PRODUCT_NAME) muốn truy cập vào danh bạ để nhập thông tin người nhận hàng từ danh bạ</string>
	<key>NSFaceIDUsageDescription</key>
	<string>Ứng dụng dùng Face ID để đăng nhập nhanh và an toàn.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Để $(PRODUCT_NAME) trả lời câu hỏi của bạn tốt nhất dựa trên vị trí hiện tại của bạn. Chúng tôi xin quyền vị trí của bạn để đảm bảo trợ lý sẽ có những câu trả lời rõ ràng và hiệu quả. Vui lòng chọn Đồng ý. Xin cảm ơn.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Để $(PRODUCT_NAME) trả lời câu hỏi của bạn tốt nhất dựa trên vị trí hiện tại của bạn. Chúng tôi xin quyền vị trí của bạn để đảm bảo trợ lý sẽ có những câu trả lời rõ ràng và hiệu quả. Vui lòng chọn Đồng ý. Xin cảm ơn.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Để $(PRODUCT_NAME) trả lời câu hỏi của bạn tốt nhất dựa trên vị trí hiện tại của bạn. Chúng tôi xin quyền vị trí của bạn để đảm bảo trợ lý sẽ có những câu trả lời rõ ràng và hiệu quả. Vui lòng chọn Đồng ý. Xin cảm ơn.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>$(PRODUCT_NAME) truy cập micro để phục vụ cho chức năng chuyển giọng nói thành văn bản</string>
	<key>NSMotionUsageDescription</key>
	<string>$(PRODUCT_NAME) cần quyền truy cập cảm biến chuyển động để cung cấp tính năng tốt hơn.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>$(PRODUCT_NAME) muốn truy cập vào thư mục ảnh để bạn có thể chọn hình ảnh gửi phản ánh</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>$(PRODUCT_NAME) truy cập nhận diện giọng nói để phục vụ cho chức năng chuyển giọng nói thành văn bản</string>
	<key>UIAppFonts</key>
	<array>
		<string>MaterialCommunityIcons.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>Ionicons.ttf</string>
		<string>Fontisto.ttf</string>
		<string>FontAwesome.ttf</string>
		<string>BeVietnam-Bold.ttf</string>
		<string>BeVietnam-BoldItalic.ttf</string>
		<string>BeVietnam-ExtraBold.ttf</string>
		<string>BeVietnam-ExtraBoldItalic.ttf</string>
		<string>BeVietnam-Italic.ttf</string>
		<string>BeVietnam-Light.ttf</string>
		<string>BeVietnam-LightItalic.ttf</string>
		<string>BeVietnam-Medium.ttf</string>
		<string>BeVietnam-MediumItalic.ttf</string>
		<string>BeVietnam-Regular.ttf</string>
		<string>BeVietnam-SemiBold.ttf</string>
		<string>BeVietnam-SemiBoldItalic.ttf</string>
		<string>BeVietnam-Thin.ttf</string>
		<string>BeVietnam-ThinItalic.ttf</string>
		<string>AntDesign.ttf</string>
		<string>Entypo.ttf</string>
		<string>EvilIcons.ttf</string>
		<string>Feather.ttf</string>
		<string>FontAwesome5_Brands.ttf</string>
		<string>FontAwesome5_Regular.ttf</string>
		<string>FontAwesome5_Solid.ttf</string>
		<string>Foundation.ttf</string>
		<string>Octicons.ttf</string>
		<string>Roboto_medium.ttf</string>
		<string>Roboto.ttf</string>
		<string>rubicon-icon-font.ttf</string>
		<string>SimpleLineIcons.ttf</string>
		<string>Zocial.ttf</string>
		<string>HeyUVietnam-Regular.ttf</string>
		<string>HeyUVietnam-SemiBold.ttf</string>
		<string>HeyUVietnam-Bold.ttf</string>
		<string>HeyUVietnam-Italic.ttf</string>
		<string>HeyUVietnam-Light.ttf</string>
		<string>HeyUVietnam-LightItalic.ttf</string>
		<string>HeyUVietnam-Medium.ttf</string>
		<string>HeyUVietnam-MediumItalic.ttf</string>
		<string>HeyUVietnam-SemiBoldItalic.ttf</string>
		<string>HeyUVietnam-ThinI.ttf</string>
		<string>HeyUVietnam-ThinItalic.ttf</string>
		<string>heyu.ttf</string>
		<string>PenguinBoldIcon.ttf</string>
		<string>PenguinLinearIcon.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>BootSplash</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UIStatusBarHidden</key>
	<false/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
