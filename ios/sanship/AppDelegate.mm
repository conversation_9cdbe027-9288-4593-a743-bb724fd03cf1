/**
 * Copyright (c) 2015-present, Facebook, Inc.
 * All rights reserved.
 *
 * This source code is licensed under the BSD-style license found in the
 * LICENSE file in the root directory of this source tree. An additional grant
 * of patent rights can be found in the PATENTS file in the same directory.
 */

#import "AppDelegate.h"
#import <React/RCTBundleURLProvider.h>
#import <React/RCTRootView.h>
#import <RNCPushNotificationIOS.h>
#import "RNCallKeep.h"
#import <PushKit/PushKit.h>
#import <React/RCTLinkingManager.h>
#import <CodePush/CodePush.h>
#import "Wow_Worker-Swift.h"
#import <zpdk/zpdk.h>
#import <GoogleMaps/GoogleMaps.h>
#import <UserNotifications/UserNotifications.h>
#import "RNBootSplash.h"
#import <AVFoundation/AVFoundation.h>

@implementation AppDelegate
NSDictionary* initialLaunchOptions;
//UIAlertView* alertView = nil;

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions
{
  NSError *error = nil;
  AVAudioSession *session = [AVAudioSession sharedInstance];
  [session setCategory:AVAudioSessionCategoryPlayback
           withOptions:AVAudioSessionCategoryOptionMixWithOthers
                 error:&error];
  if (error) {
    NSLog(@"[AudioSession] Error setting category: %@", error);
  }
  [session setActive:YES error:&error];
  if (error) {
    NSLog(@"[AudioSession] Error activating session: %@", error);
  }
  if (@available(iOS 14, *)) {
    UIDatePicker *picker = [UIDatePicker appearance];
    picker.preferredDatePickerStyle = UIDatePickerStyleWheels;
  }
  [GMSServices provideAPIKey:@"AIzaSyBIidO3qxiwdjdX_GT1fLRvfGM8E8D4WIc"];
  RCTBridge *bridge = [[RCTBridge alloc] initWithDelegate:self launchOptions:launchOptions];
  RCTRootView *rootView = [[RCTRootView alloc] initWithBridge:bridge
                                                   moduleName:@"WowWorker"
                                            initialProperties:launchOptions];

  rootView.backgroundColor =  [UIColor whiteColor];

  self.window = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
  UIViewController *rootViewController = [UIViewController new];
  rootViewController.view = rootView;
  self.window.rootViewController = rootViewController;
  [self.window makeKeyAndVisible];

  [RNBootSplash initWithStoryboard:@"BootSplash" rootView:rootViewController.view];

//  [[ZaloPaySDK sharedInstance] initWithAppId:1272 uriScheme:@"wowworker://" environment: ZPZPIEnvironment_Production];
//  Dynamic *t = [Dynamic new];
//  UIView *animationView = [t createAnimationViewWithRootView:rootView lottieName:@"animationLoginScreen"];
//  animationView.backgroundColor = [UIColor whiteColor];

  // register LottieSplashScreen to RNSplashScreen
//  [RNSplashScreen showLottieSplash:animationView inRootView:rootView];

  // play
//  [t playWithAnimationView:animationView];

  // If you want the animation layout to be forced to remove when hide is called, use this code
//  [RNSplashScreen setAnimationFinished:true];

  // Define UNUserNotificationCenter
  UNUserNotificationCenter *center = [UNUserNotificationCenter currentNotificationCenter];
  center.delegate = self;

  return YES;
}

- (void)enableMicSession {
  NSError *error = nil;
  AVAudioSession *session = [AVAudioSession sharedInstance];
  [session setCategory:AVAudioSessionCategoryPlayAndRecord
           withOptions:AVAudioSessionCategoryOptionDefaultToSpeaker
                 error:&error];
  if (error) {
    NSLog(@"[AudioSession] Error setting mic category: %@", error);
  }
  [session setActive:YES error:&error];
  if (error) {
    NSLog(@"[AudioSession] Error activating mic session: %@", error);
  }
}

- (void)releaseMicSession {
  NSError *error = nil;
  AVAudioSession *session = [AVAudioSession sharedInstance];

  BOOL success = [session setActive:NO withOptions:AVAudioSessionSetActiveOptionNotifyOthersOnDeactivation error:&error];
  if (!success || error) {
    NSLog(@"[AudioSession] Error deactivating mic session: %@", error.localizedDescription);
  }

  dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
    NSError *err2 = nil;
    [session setCategory:AVAudioSessionCategoryPlayback
             withOptions:AVAudioSessionCategoryOptionMixWithOthers
                   error:&err2];
    if (err2) {
      NSLog(@"[AudioSession] Error resetting playback category: %@", err2.localizedDescription);
    }
    [session setActive:YES error:&err2];
  });
}

//Called when a notification is delivered to a foreground app.
-(void)userNotificationCenter:(UNUserNotificationCenter *)center willPresentNotification:(UNNotification *)notification withCompletionHandler:(void (^)(UNNotificationPresentationOptions options))completionHandler
{
  NSDictionary *userInfo = notification.request.content.userInfo;
  //Foreground
  NSLog(@"APP_PUSH from foreground %@", userInfo);

  [RNCPushNotificationIOS didReceiveRemoteNotification:userInfo
  fetchCompletionHandler:^void (UIBackgroundFetchResult result){}];
//  completionHandler(UNNotificationPresentationOptionAlert);
}

//- (BOOL)application:(UIApplication *)app openURL:(NSURL *)url options:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options{
//  return [[ZaloPaySDK sharedInstance] application:app openURL:url sourceApplication:@"vn.com.vng.zalopay" annotation:nil];
//}

- (NSURL *)sourceURLForBridge:(RCTBridge *)bridge
{
#if DEBUG
  return [[RCTBundleURLProvider sharedSettings] jsBundleURLForBundleRoot:@"index"];
#else
  return [CodePush bundleURL];
#endif
}

- (BOOL)application:(UIApplication *)application openURL:(NSURL *)url sourceApplication:(NSString *)sourceApplication annotation:(id)annotation {
  return [RCTLinkingManager application:application openURL:url
               sourceApplication:sourceApplication annotation:annotation];
}

/* This section is for push notification*/

// Required to register for notifications
- (void)application:(UIApplication *)application didRegisterUserNotificationSettings:(UIUserNotificationSettings *)notificationSettings
{
  [RNCPushNotificationIOS didRegisterUserNotificationSettings:notificationSettings];
}

// Required for the register event.
- (void)application:(UIApplication *)application didRegisterForRemoteNotificationsWithDeviceToken:(NSData *)deviceToken
{
  [RNCPushNotificationIOS didRegisterForRemoteNotificationsWithDeviceToken:deviceToken];
}
// Required for the notification event. You must call the completion handler after handling the remote notification.
- (void)application:(UIApplication *)application didReceiveRemoteNotification:(NSDictionary *)userInfo
fetchCompletionHandler:(void (^)(UIBackgroundFetchResult))completionHandler
{
  NSLog(@"APP_PUSH from background %@", userInfo);
  [RNCPushNotificationIOS didReceiveRemoteNotification:userInfo fetchCompletionHandler:completionHandler];
}
// Required for the registrationError event.
- (void)application:(UIApplication *)application didFailToRegisterForRemoteNotificationsWithError:(NSError *)error
{
  [RNCPushNotificationIOS didFailToRegisterForRemoteNotificationsWithError:error];
}
// Required for localNotification event
- (void)userNotificationCenter:(UNUserNotificationCenter *)center
didReceiveNotificationResponse:(UNNotificationResponse *)response
         withCompletionHandler:(void (^)(void))completionHandler
{
  NSLog(@"APP_PUSH from background 22");
  [RNCPushNotificationIOS didReceiveNotificationResponse:response];
}
/* End push notification */


/* others */

// Get launch image
-(UIImage *)bgImage:(UIImage *)img
{
  CGFloat scale = [[UIScreen mainScreen]scale];
  CGSize newSize = _window.frame.size;
  UIGraphicsBeginImageContextWithOptions(newSize, YES, scale);
  [img drawInRect:CGRectMake(0,0,newSize.width,newSize.height)];
  UIImage* newImage = UIGraphicsGetImageFromCurrentImageContext();
  UIGraphicsEndImageContext();
  return newImage;
}

- (NSString *)splashImageNameForOrientation: (UIInterfaceOrientation)orientation {
  CGSize viewSize = self.window.bounds.size;
  NSString* viewOrientation = @"Portrait";
  if ((orientation == UIInterfaceOrientationLandscapeLeft) || (orientation == UIInterfaceOrientationLandscapeRight)) {
    viewSize = CGSizeMake(viewSize.height, viewSize.width);
    viewOrientation = @"Landscape";
  }

  NSArray* imagesDict = [[[NSBundle mainBundle] infoDictionary] valueForKey:@"UILaunchImages"];
  for (NSDictionary* dict in imagesDict) {
    CGSize imageSize = CGSizeFromString(dict[@"UILaunchImageSize"]);
    if (CGSizeEqualToSize(imageSize, viewSize) && [viewOrientation isEqualToString:dict[@"UILaunchImageOrientation"]])
      return dict[@"UILaunchImageName"];
  }
  return nil;
}

-(UIColor*) getUIColorLaunchImage {
  UIInterfaceOrientation orientation = [[UIApplication sharedApplication] statusBarOrientation];
  NSString* launchImageName = [self splashImageNameForOrientation: orientation];
  NSLog(@"launchImageName: %@", launchImageName);
  UIColor *image;
  if(launchImageName != nil) {
    image = [UIColor colorWithPatternImage:[self bgImage: [UIImage imageNamed:launchImageName]]];
  } else {
    image = [[UIColor alloc] initWithRed:27.0/255.0f green:27.0/255.0f blue:27.0/255.0f alpha: 1.0f];
  }

  return image;
}

// Callkeep
- (BOOL)application:(UIApplication *)application
continueUserActivity:(NSUserActivity *)userActivity
 restorationHandler:(void(^)(NSArray * __nullable restorableObjects))restorationHandler
{
  return [RNCallKeep application:application
           continueUserActivity:userActivity
             restorationHandler:restorationHandler];
}

/* Add PushKit delegate method */

// Handle updated push credentials
- (void)pushRegistry:(PKPushRegistry *)registry didUpdatePushCredentials:(PKPushCredentials *)credentials forType:(NSString *)type {
  // Register VoIP push token (a property of PKPushCredentials) with server
//  [RNVoipPushNotificationManager didUpdatePushCredentials:credentials forType:(NSString *)type];
}

- (void)pushRegistry:(PKPushRegistry *)registry didInvalidatePushTokenForType:(PKPushType)type
{
  // --- The system calls this method when a previously provided push token is no longer valid for use. No action is necessary on your part to reregister the push type. Instead, use this method to notify your server not to send push notifications using the matching push token.
}

// Handle incoming pushes
- (void)pushRegistry:(PKPushRegistry *)registry didReceiveIncomingPushWithPayload:(PKPushPayload *)payload forType:(NSString *)type withCompletionHandler:(void (^)(void))completion {
  // Process the received push
//  [RNVoipPushNotificationManager didReceiveIncomingPushWithPayload:payload forType:(NSString *)type];

  NSString *uuid = [payload.dictionaryPayload valueForKeyPath:@"extras.roomId"];
  NSString *handle = payload.dictionaryPayload[@"phone"];

  [RNCallKeep reportNewIncomingCall: uuid
                                 handle: handle
                             handleType: @"generic"
                               hasVideo: NO
                    localizedCallerName: @"callerName"
                        supportsHolding: YES
                           supportsDTMF: YES
                       supportsGrouping: YES
                     supportsUngrouping: YES
                            fromPushKit: YES
                                payload: nil
                  withCompletionHandler: completion];

}

@end
