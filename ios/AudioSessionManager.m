#import "AudioSessionManager.h"
#import "AppDelegate.h"
#import <UIKit/UIKit.h>

@implementation AudioSessionManager

RCT_EXPORT_MODULE(); // 🔥 <PERSON><PERSON><PERSON> này bắt buộc để React Native nhận module

RCT_EXPORT_METHOD(enableMicSession) {
  dispatch_async(dispatch_get_main_queue(), ^{
    AppDelegate *delegate = (AppDelegate *)[UIApplication sharedApplication].delegate;
    [delegate enableMicSession];
  });
}

RCT_EXPORT_METHOD(releaseMicSession) {
  dispatch_async(dispatch_get_main_queue(), ^{
    AppDelegate *delegate = (AppDelegate *)[UIApplication sharedApplication].delegate;
    [delegate releaseMicSession];
  });
}

@end
