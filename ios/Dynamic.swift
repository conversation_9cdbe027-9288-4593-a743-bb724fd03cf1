import UIKit
import Foundation
import Lottie

@objc class Dynamic: NSObject {
  
  @objc func createAnimationView(rootView: UIView, lottieName: String) -> LottieAnimationView {
    
    let animationView = LottieAnimationView(name: lottieName)

    animationView.frame = rootView.frame
    animationView.center = rootView.center
    animationView.backgroundColor = UIColor.white;
    return animationView;
  }
  
  
  @objc func play(animationView: LottieAnimationView) {
    
    let imageView = UIImageView();
    
    imageView.center = CGPoint(x: animationView.frame.size.width/2 - 84, y: animationView.frame.size.height/16)
    imageView.image = UIImage(named: "logoLoginHeyU");
    imageView.frame.size = CGSize(width: 168, height: 168)
    imageView.contentMode = .scaleAspectFit
      
    animationView.addSubview(imageView);
    
    animationView.play(
      completion: { (success) in
        RNSplashScreen.setAnimationFinished(true)
      }
    );
  }
}
