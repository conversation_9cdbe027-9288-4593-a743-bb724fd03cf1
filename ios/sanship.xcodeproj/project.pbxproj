// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		00E356F31AD99517003FC87E /* sanshipTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E356F21AD99517003FC87E /* sanshipTests.m */; };
		0223126FEECA4ED58BD5D98B /* PenguinLinearIcon.ttf in Resources */ = {isa = PBXBuildFile; fileRef = CE1D0FCB3F8540F09C9F00C4 /* PenguinLinearIcon.ttf */; };
		03199F8D24FE49BB0053B053 /* new_move_order.aiff in Resources */ = {isa = PBXBuildFile; fileRef = 03199F8C24FE49BB0053B053 /* new_move_order.aiff */; };
		11211904D9F441CF90B36529 /* HeyUVietnam-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 205D82E977C44A9A974BD6AA /* HeyUVietnam-SemiBold.ttf */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		2136100BD03143FD958257BF /* HeyUVietnam-MediumItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E80258E7D06A446AA5131722 /* HeyUVietnam-MediumItalic.ttf */; };
		2D02E4BF1E0B4AB3006451C7 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		2DCD954D1E0B4F2C00145EB5 /* sanshipTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E356F21AD99517003FC87E /* sanshipTests.m */; };
		3382D6BD268EEABB00E74E99 /* animationLoginScreen.json in Resources */ = {isa = PBXBuildFile; fileRef = 3382D6BC268EEABB00E74E99 /* animationLoginScreen.json */; };
		33DB82462687015800F9292C /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 33DB82452687015800F9292C /* LaunchScreen.storyboard */; };
		33F8ACDC267DE94C009DC270 /* Dynamic.swift in Sources */ = {isa = PBXBuildFile; fileRef = 33F8ACDB267DE94C009DC270 /* Dynamic.swift */; };
		4252535C5EF9F7ADE10F0C7D /* libPods-sanship-sanshipTests.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 25E3568D8F1A4DCF68E8C5F4 /* libPods-sanship-sanshipTests.a */; };
		504AFD4BB6D94D8EB7AF9AB7 /* HeyUVietnam-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 31E763B382534CDC9A226980 /* HeyUVietnam-Medium.ttf */; };
		55E19A496F684255AD888FD0 /* HeyUVietnam-LightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 83F1BDA150994EE5A33E8F59 /* HeyUVietnam-LightItalic.ttf */; };
		5E3DE0302DE4478100AF2F4A /* AudioSessionManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 5E3DE02F2DE4478100AF2F4A /* AudioSessionManager.m */; };
		732FA94EC86648DBB87FB2D5 /* HeyUVietnam-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C6A7D298D41F44DBADEEDFC5 /* HeyUVietnam-Bold.ttf */; };
		7BD7988001CB4B7C82FBD785 /* heyu.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C185AE4F508F4E38A68790C0 /* heyu.ttf */; };
		8987C927BE5A4B59A3542996 /* HeyUVietnam-SemiBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 5A07E5803A9F4C899E134E44 /* HeyUVietnam-SemiBoldItalic.ttf */; };
		9F5B1A0C8A6D44A58CD78A10 /* Colors.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 9A99F047C02C4BA6A82F6098 /* Colors.xcassets */; };
		A27F4F16E07DF45B9031009D /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = ED61C16FCA9D78AB81DDBF99 /* PrivacyInfo.xcprivacy */; };
		ADB5696603B94AE9B2E85C3F /* HeyUVietnam-ThinItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = D1D480036651450DBE3975B6 /* HeyUVietnam-ThinItalic.ttf */; };
		AE4445E1FBA244F6B40C1E5C /* HeyUVietnam-ThinI.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C79548869CC048A7B2AB44C9 /* HeyUVietnam-ThinI.ttf */; };
		AFF9D9EFAA9D4185ABFD2A38 /* HeyUVietnam-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 5518F94B5A494C9BBD6B285E /* HeyUVietnam-Regular.ttf */; };
		BF33939CA4214EEF81039F12 /* HeyUVietnam-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 429BEC116F8D406C9087AE53 /* HeyUVietnam-Light.ttf */; };
		C30EE43FA78243398D85EF98 /* PenguinBoldIcon.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C1AFD28882BB42789D0062CD /* PenguinBoldIcon.ttf */; };
		C32C786178F4F3B7F3531D65 /* libPods-sanship.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 7B6263953B551980F4545BC8 /* libPods-sanship.a */; };
		CE9348B1F0AD450C9FECB056 /* BootSplash.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 47EAB736F93F473F805CEA34 /* BootSplash.storyboard */; };
		E372834A23E563B700E3E8AA /* incallmanager_ringback.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = E372834723E563B700E3E8AA /* incallmanager_ringback.mp3 */; };
		E372834B23E563B700E3E8AA /* new_order.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = E372834823E563B700E3E8AA /* new_order.mp3 */; };
		E372834E23E5645500E3E8AA /* message.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = E372834C23E5645500E3E8AA /* message.mp3 */; };
		E372834F23E5645500E3E8AA /* newcall.aiff in Resources */ = {isa = PBXBuildFile; fileRef = E372834D23E5645500E3E8AA /* newcall.aiff */; };
		E37399782BDA653B00058949 /* SwiftUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = E37399772BDA653B00058949 /* SwiftUI.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		E37A5EED23D5E82B0062657C /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = E37A5EEC23D5E82A0062657C /* Images.xcassets */; };
		E3844A4823E3E79B00309FE0 /* sanship.swift in Sources */ = {isa = PBXBuildFile; fileRef = E3844A4723E3E79B00309FE0 /* sanship.swift */; };
		E3A32224277B0EE100B90FB7 /* LinkPresentation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = E3A32223277B0EE100B90FB7 /* LinkPresentation.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		E3E14AFD2E4B1D69004FCD29 /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = E3E14AFC2E4B1D69004FCD29 /* AppDelegate.mm */; };
		E3EC9817260C7A5000E870CA /* PayZaloBridge.m in Sources */ = {isa = PBXBuildFile; fileRef = E3EC9815260C7A5000E870CA /* PayZaloBridge.m */; };
		E3EC9818260C7CED00E870CA /* zpdk.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = E3EC9813260C7A1900E870CA /* zpdk.framework */; };
		ED62B68F270D387D00EBF9D0 /* sound_heyu.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = ED62B68E270D387D00EBF9D0 /* sound_heyu.mp3 */; };
		F0CC4D6CED644B9C9A29D8AF /* HeyUVietnam-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 5B43007DD78B4914B3ECF7DD /* HeyUVietnam-Italic.ttf */; };
		F7F120A97D9B14EE38164CA5 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = ED61C16FCA9D78AB81DDBF99 /* PrivacyInfo.xcprivacy */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		00E356F41AD99517003FC87E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 13B07F861A680F5B00A75B9A;
			remoteInfo = sanship;
		};
		2D02E4911E0B4A5D006451C7 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 2D02E47A1E0B4A5D006451C7;
			remoteInfo = "sanship-tvOS";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		00E356EE1AD99517003FC87E /* sanshipTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = sanshipTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		00E356F11AD99517003FC87E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		00E356F21AD99517003FC87E /* sanshipTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = sanshipTests.m; sourceTree = "<group>"; };
		01504B89FDF943DD971D269D /* BeVietnam-ThinItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "BeVietnam-ThinItalic.ttf"; path = "../assets/fonts/BeVietnam-ThinItalic.ttf"; sourceTree = "<group>"; };
		03199F8C24FE49BB0053B053 /* new_move_order.aiff */ = {isa = PBXFileReference; explicitFileType = audio.aiff; path = new_move_order.aiff; sourceTree = "<group>"; };
		048035E030D54910B6BA2418 /* rubicon-icon-font.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "rubicon-icon-font.ttf"; path = "../node_modules/native-base/Fonts/rubicon-icon-font.ttf"; sourceTree = "<group>"; };
		04BC7BB3A71149609AEF9485 /* MaterialCommunityIcons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = MaterialCommunityIcons.ttf; path = "../node_modules/native-base/Fonts/MaterialCommunityIcons.ttf"; sourceTree = "<group>"; };
		0D3CBB7CEA874A7188C57582 /* FontAwesome5_Solid.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = FontAwesome5_Solid.ttf; path = "../node_modules/native-base/Fonts/FontAwesome5_Solid.ttf"; sourceTree = "<group>"; };
		134A3CB56CD04B789C58A72A /* FontAwesome5_Solid.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = FontAwesome5_Solid.ttf; path = "../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Solid.ttf"; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* Wow Worker.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "Wow Worker.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = sanship/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = sanship/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = sanship/main.m; sourceTree = "<group>"; };
		13B9B0947D91403F9AC38CC3 /* FontAwesome5_Brands.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = FontAwesome5_Brands.ttf; path = "../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Brands.ttf"; sourceTree = "<group>"; };
		152552AD4438486EA2EF43CC /* Feather.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Feather.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Feather.ttf"; sourceTree = "<group>"; };
		154F85AF25064CE0B1567ED2 /* FontAwesome.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = FontAwesome.ttf; path = "../node_modules/native-base/Fonts/FontAwesome.ttf"; sourceTree = "<group>"; };
		1CD3E82DA6B34CBCACAEAC4F /* FontAwesome5_Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = FontAwesome5_Regular.ttf; path = "../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Regular.ttf"; sourceTree = "<group>"; };
		205D82E977C44A9A974BD6AA /* HeyUVietnam-SemiBold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "HeyUVietnam-SemiBold.ttf"; path = "../assets/fonts/HeyUVietnam-SemiBold.ttf"; sourceTree = "<group>"; };
		20BC6D0131044EFDB07B9A9E /* BeVietnam-Medium.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "BeVietnam-Medium.ttf"; path = "../assets/fonts/BeVietnam-Medium.ttf"; sourceTree = "<group>"; };
		22E0E60203D044359E3E2583 /* Octicons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Octicons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Octicons.ttf"; sourceTree = "<group>"; };
		25E3568D8F1A4DCF68E8C5F4 /* libPods-sanship-sanshipTests.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-sanship-sanshipTests.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		26E1F868B63D46CE956C85C5 /* MaterialIcons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = MaterialIcons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/MaterialIcons.ttf"; sourceTree = "<group>"; };
		2D02E47B1E0B4A5D006451C7 /* sanship-tvOS.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "sanship-tvOS.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		2D02E4901E0B4A5D006451C7 /* sanship-tvOSTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "sanship-tvOSTests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		31E763B382534CDC9A226980 /* HeyUVietnam-Medium.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "HeyUVietnam-Medium.ttf"; path = "../assets/fonts/HeyUVietnam-Medium.ttf"; sourceTree = "<group>"; };
		3382D6BC268EEABB00E74E99 /* animationLoginScreen.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; name = animationLoginScreen.json; path = ../assets/Animation/animationLoginScreen.json; sourceTree = "<group>"; };
		33DB82452687015800F9292C /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = LaunchScreen.storyboard; sourceTree = "<group>"; };
		33F8ACDB267DE94C009DC270 /* Dynamic.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Dynamic.swift; sourceTree = "<group>"; };
		3533D997B1894FF2A797F41B /* EvilIcons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = EvilIcons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/EvilIcons.ttf"; sourceTree = "<group>"; };
		411C68969BE348C89FB31CEE /* BeVietnam-Thin.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "BeVietnam-Thin.ttf"; path = "../assets/fonts/BeVietnam-Thin.ttf"; sourceTree = "<group>"; };
		429BEC116F8D406C9087AE53 /* HeyUVietnam-Light.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "HeyUVietnam-Light.ttf"; path = "../assets/fonts/HeyUVietnam-Light.ttf"; sourceTree = "<group>"; };
		438B8344A46942988DADB5BC /* Roboto_medium.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Roboto_medium.ttf; path = "../node_modules/native-base/Fonts/Roboto_medium.ttf"; sourceTree = "<group>"; };
		46D326B7D01348A19EC40A7A /* SimpleLineIcons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = SimpleLineIcons.ttf; path = "../node_modules/native-base/Fonts/SimpleLineIcons.ttf"; sourceTree = "<group>"; };
		47EAB736F93F473F805CEA34 /* BootSplash.storyboard */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = BootSplash.storyboard; path = sanship/BootSplash.storyboard; sourceTree = "<group>"; };
		483D818A68FE95992F373E9B /* Pods-sanship-sanshipTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-sanship-sanshipTests.debug.xcconfig"; path = "Target Support Files/Pods-sanship-sanshipTests/Pods-sanship-sanshipTests.debug.xcconfig"; sourceTree = "<group>"; };
		517A7329FF728037E03582EB /* Pods-sanship.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-sanship.release.xcconfig"; path = "Target Support Files/Pods-sanship/Pods-sanship.release.xcconfig"; sourceTree = "<group>"; };
		54DFF252381042EFB71D0F0B /* BeVietnam-Bold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "BeVietnam-Bold.ttf"; path = "../assets/fonts/BeVietnam-Bold.ttf"; sourceTree = "<group>"; };
		5518F94B5A494C9BBD6B285E /* HeyUVietnam-Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "HeyUVietnam-Regular.ttf"; path = "../assets/fonts/HeyUVietnam-Regular.ttf"; sourceTree = "<group>"; };
		585006A942D0443CAC829408 /* Feather.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Feather.ttf; path = "../node_modules/native-base/Fonts/Feather.ttf"; sourceTree = "<group>"; };
		5A07E5803A9F4C899E134E44 /* HeyUVietnam-SemiBoldItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "HeyUVietnam-SemiBoldItalic.ttf"; path = "../assets/fonts/HeyUVietnam-SemiBoldItalic.ttf"; sourceTree = "<group>"; };
		5B43007DD78B4914B3ECF7DD /* HeyUVietnam-Italic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "HeyUVietnam-Italic.ttf"; path = "../assets/fonts/HeyUVietnam-Italic.ttf"; sourceTree = "<group>"; };
		5C1C7133BA454CB1A25A005D /* BeVietnam-Italic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "BeVietnam-Italic.ttf"; path = "../assets/fonts/BeVietnam-Italic.ttf"; sourceTree = "<group>"; };
		5E3DE02E2DE4478100AF2F4A /* AudioSessionManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AudioSessionManager.h; sourceTree = "<group>"; };
		5E3DE02F2DE4478100AF2F4A /* AudioSessionManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AudioSessionManager.m; sourceTree = "<group>"; };
		6A60E48F12294884B98BA975 /* Ionicons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Ionicons.ttf; path = "../node_modules/native-base/Fonts/Ionicons.ttf"; sourceTree = "<group>"; };
		7016DDA123804B15A6A7F6F1 /* Foundation.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Foundation.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Foundation.ttf"; sourceTree = "<group>"; };
		719103D3B019400E991AA405 /* BeVietnam-ExtraBold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "BeVietnam-ExtraBold.ttf"; path = "../assets/fonts/BeVietnam-ExtraBold.ttf"; sourceTree = "<group>"; };
		75FA1CF825F64B10A892D02F /* BeVietnam-SemiBold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "BeVietnam-SemiBold.ttf"; path = "../assets/fonts/BeVietnam-SemiBold.ttf"; sourceTree = "<group>"; };
		7A4949CED7584DD587FAC5F4 /* Fontisto.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Fontisto.ttf; path = "../node_modules/native-base/Fonts/Fontisto.ttf"; sourceTree = "<group>"; };
		7B6263953B551980F4545BC8 /* libPods-sanship.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-sanship.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		82C6FB3BA4634F608C85AF23 /* BeVietnam-SemiBoldItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "BeVietnam-SemiBoldItalic.ttf"; path = "../assets/fonts/BeVietnam-SemiBoldItalic.ttf"; sourceTree = "<group>"; };
		83F1BDA150994EE5A33E8F59 /* HeyUVietnam-LightItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "HeyUVietnam-LightItalic.ttf"; path = "../assets/fonts/HeyUVietnam-LightItalic.ttf"; sourceTree = "<group>"; };
		897F84C3F0AC471587D00985 /* Fontisto.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Fontisto.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Fontisto.ttf"; sourceTree = "<group>"; };
		8C8A50F969454B748EC5585D /* Ionicons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Ionicons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Ionicons.ttf"; sourceTree = "<group>"; };
		8DACE7FBF9634CC19A4862D5 /* BeVietnam-Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "BeVietnam-Regular.ttf"; path = "../assets/fonts/BeVietnam-Regular.ttf"; sourceTree = "<group>"; };
		9694E3BE03A84B1CA5406614 /* MaterialCommunityIcons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = MaterialCommunityIcons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/MaterialCommunityIcons.ttf"; sourceTree = "<group>"; };
		9744B429504842FFBEDC8274 /* Roboto.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Roboto.ttf; path = "../node_modules/native-base/Fonts/Roboto.ttf"; sourceTree = "<group>"; };
		97C8E1F95667057C58D59E35 /* Pods-sanship-sanshipTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-sanship-sanshipTests.release.xcconfig"; path = "Target Support Files/Pods-sanship-sanshipTests/Pods-sanship-sanshipTests.release.xcconfig"; sourceTree = "<group>"; };
		9A99F047C02C4BA6A82F6098 /* Colors.xcassets */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = folder.assetcatalog; name = Colors.xcassets; path = sanship/Colors.xcassets; sourceTree = "<group>"; };
		9DA96D91091D416DB7D729A8 /* AntDesign.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = AntDesign.ttf; path = "../node_modules/react-native-vector-icons/Fonts/AntDesign.ttf"; sourceTree = "<group>"; };
		A04F87105F3144D7888D3BB6 /* FontAwesome5_Brands.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = FontAwesome5_Brands.ttf; path = "../node_modules/native-base/Fonts/FontAwesome5_Brands.ttf"; sourceTree = "<group>"; };
		A2954B0365854770AA763705 /* BeVietnam-ExtraBoldItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "BeVietnam-ExtraBoldItalic.ttf"; path = "../assets/fonts/BeVietnam-ExtraBoldItalic.ttf"; sourceTree = "<group>"; };
		B6791F99F98946488D961B7F /* Zocial.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Zocial.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Zocial.ttf"; sourceTree = "<group>"; };
		B76901C5C62F42E891899AE6 /* Entypo.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Entypo.ttf; path = "../node_modules/native-base/Fonts/Entypo.ttf"; sourceTree = "<group>"; };
		B9F689793088400290E28754 /* Octicons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Octicons.ttf; path = "../node_modules/native-base/Fonts/Octicons.ttf"; sourceTree = "<group>"; };
		BFC23E262AD6447E8EF46721 /* BeVietnam-Light.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "BeVietnam-Light.ttf"; path = "../assets/fonts/BeVietnam-Light.ttf"; sourceTree = "<group>"; };
		C07BDF43AE624E368ED6DC15 /* Zocial.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Zocial.ttf; path = "../node_modules/native-base/Fonts/Zocial.ttf"; sourceTree = "<group>"; };
		C185AE4F508F4E38A68790C0 /* heyu.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = heyu.ttf; path = ../assets/fonts/heyu.ttf; sourceTree = "<group>"; };
		C1AFD28882BB42789D0062CD /* PenguinBoldIcon.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = PenguinBoldIcon.ttf; path = ../assets/fonts/PenguinBoldIcon.ttf; sourceTree = "<group>"; };
		C6A7D298D41F44DBADEEDFC5 /* HeyUVietnam-Bold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "HeyUVietnam-Bold.ttf"; path = "../assets/fonts/HeyUVietnam-Bold.ttf"; sourceTree = "<group>"; };
		C79548869CC048A7B2AB44C9 /* HeyUVietnam-ThinI.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "HeyUVietnam-ThinI.ttf"; path = "../assets/fonts/HeyUVietnam-ThinI.ttf"; sourceTree = "<group>"; };
		C805855EC8CD4D65BFA5FFD3 /* BeVietnam-BoldItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "BeVietnam-BoldItalic.ttf"; path = "../assets/fonts/BeVietnam-BoldItalic.ttf"; sourceTree = "<group>"; };
		CA56584CB49D4AA497C063FC /* BeVietnam-LightItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "BeVietnam-LightItalic.ttf"; path = "../assets/fonts/BeVietnam-LightItalic.ttf"; sourceTree = "<group>"; };
		CAEA3E7592864011B06BC371 /* MaterialIcons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = MaterialIcons.ttf; path = "../node_modules/native-base/Fonts/MaterialIcons.ttf"; sourceTree = "<group>"; };
		CE1D0FCB3F8540F09C9F00C4 /* PenguinLinearIcon.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = PenguinLinearIcon.ttf; path = ../assets/fonts/PenguinLinearIcon.ttf; sourceTree = "<group>"; };
		D1D480036651450DBE3975B6 /* HeyUVietnam-ThinItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "HeyUVietnam-ThinItalic.ttf"; path = "../assets/fonts/HeyUVietnam-ThinItalic.ttf"; sourceTree = "<group>"; };
		D4D60DFAD9C54A9DBB694247 /* EvilIcons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = EvilIcons.ttf; path = "../node_modules/native-base/Fonts/EvilIcons.ttf"; sourceTree = "<group>"; };
		D8EB8B320BA442A6BF2F1CB2 /* Foundation.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Foundation.ttf; path = "../node_modules/native-base/Fonts/Foundation.ttf"; sourceTree = "<group>"; };
		DA8176FFBF5B4A0886EF0544 /* FontAwesome5_Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = FontAwesome5_Regular.ttf; path = "../node_modules/native-base/Fonts/FontAwesome5_Regular.ttf"; sourceTree = "<group>"; };
		DA912FB283034095BB19D569 /* FontAwesome.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = FontAwesome.ttf; path = "../node_modules/react-native-vector-icons/Fonts/FontAwesome.ttf"; sourceTree = "<group>"; };
		DB897C3A0CA84E52A9AA6C45 /* AntDesign.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = AntDesign.ttf; path = "../node_modules/native-base/Fonts/AntDesign.ttf"; sourceTree = "<group>"; };
		DBA15D49B1F449177C1A23DE /* Pods-sanship.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-sanship.debug.xcconfig"; path = "Target Support Files/Pods-sanship/Pods-sanship.debug.xcconfig"; sourceTree = "<group>"; };
		E33AE1B729E6893000F52972 /* sound_heyu.wav */ = {isa = PBXFileReference; lastKnownFileType = audio.wav; path = sound_heyu.wav; sourceTree = "<group>"; };
		E372834723E563B700E3E8AA /* incallmanager_ringback.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = incallmanager_ringback.mp3; sourceTree = "<group>"; };
		E372834823E563B700E3E8AA /* new_order.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = new_order.mp3; sourceTree = "<group>"; };
		E372834C23E5645500E3E8AA /* message.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = message.mp3; sourceTree = "<group>"; };
		E372834D23E5645500E3E8AA /* newcall.aiff */ = {isa = PBXFileReference; lastKnownFileType = audio.aiff; path = newcall.aiff; sourceTree = "<group>"; };
		E37399772BDA653B00058949 /* SwiftUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SwiftUI.framework; path = System/Library/Frameworks/SwiftUI.framework; sourceTree = SDKROOT; };
		E37A5EEC23D5E82A0062657C /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = sanship/Images.xcassets; sourceTree = "<group>"; };
		E3844A4623E3E79A00309FE0 /* sanship-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "sanship-Bridging-Header.h"; sourceTree = "<group>"; };
		E3844A4723E3E79B00309FE0 /* sanship.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = sanship.swift; sourceTree = "<group>"; };
		E39C66E823CC6EAA00B4C7F6 /* sanship.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = sanship.entitlements; path = sanship/sanship.entitlements; sourceTree = "<group>"; };
		E3A32223277B0EE100B90FB7 /* LinkPresentation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = LinkPresentation.framework; path = System/Library/Frameworks/LinkPresentation.framework; sourceTree = SDKROOT; };
		E3E14AFC2E4B1D69004FCD29 /* AppDelegate.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = AppDelegate.mm; path = sanship/AppDelegate.mm; sourceTree = "<group>"; };
		E3EC9813260C7A1900E870CA /* zpdk.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = zpdk.framework; sourceTree = "<group>"; };
		E3EC9815260C7A5000E870CA /* PayZaloBridge.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PayZaloBridge.m; sourceTree = "<group>"; };
		E3EC9816260C7A5000E870CA /* PayZaloBridge.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PayZaloBridge.h; sourceTree = "<group>"; };
		E6CBC940BC8343F49F37AC4E /* Entypo.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Entypo.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Entypo.ttf"; sourceTree = "<group>"; };
		E80258E7D06A446AA5131722 /* HeyUVietnam-MediumItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "HeyUVietnam-MediumItalic.ttf"; path = "../assets/fonts/HeyUVietnam-MediumItalic.ttf"; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		ED2971642150620600B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = Platforms/AppleTVOS.platform/Developer/SDKs/AppleTVOS12.0.sdk/System/Library/Frameworks/JavaScriptCore.framework; sourceTree = DEVELOPER_DIR; };
		ED61C16FCA9D78AB81DDBF99 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xml; name = PrivacyInfo.xcprivacy; path = sanship/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		ED62B68E270D387D00EBF9D0 /* sound_heyu.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = sound_heyu.mp3; sourceTree = "<group>"; };
		F1A29D4B982048EFA0DFB2F3 /* SimpleLineIcons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = SimpleLineIcons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/SimpleLineIcons.ttf"; sourceTree = "<group>"; };
		FE9D643B69E84A1ABAFE8556 /* BeVietnam-MediumItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "BeVietnam-MediumItalic.ttf"; path = "../assets/fonts/BeVietnam-MediumItalic.ttf"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		00E356EB1AD99517003FC87E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4252535C5EF9F7ADE10F0C7D /* libPods-sanship-sanshipTests.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E37399782BDA653B00058949 /* SwiftUI.framework in Frameworks */,
				E3EC9818260C7CED00E870CA /* zpdk.framework in Frameworks */,
				E3A32224277B0EE100B90FB7 /* LinkPresentation.framework in Frameworks */,
				C32C786178F4F3B7F3531D65 /* libPods-sanship.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D02E4781E0B4A5D006451C7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D02E48D1E0B4A5D006451C7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00E356EF1AD99517003FC87E /* sanshipTests */ = {
			isa = PBXGroup;
			children = (
				00E356F21AD99517003FC87E /* sanshipTests.m */,
				00E356F01AD99517003FC87E /* Supporting Files */,
			);
			path = sanshipTests;
			sourceTree = "<group>";
		};
		00E356F01AD99517003FC87E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				00E356F11AD99517003FC87E /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		0D7E9A01EA1248E7BF44FCEC /* Resources */ = {
			isa = PBXGroup;
			children = (
				9DA96D91091D416DB7D729A8 /* AntDesign.ttf */,
				E6CBC940BC8343F49F37AC4E /* Entypo.ttf */,
				3533D997B1894FF2A797F41B /* EvilIcons.ttf */,
				152552AD4438486EA2EF43CC /* Feather.ttf */,
				DA912FB283034095BB19D569 /* FontAwesome.ttf */,
				13B9B0947D91403F9AC38CC3 /* FontAwesome5_Brands.ttf */,
				1CD3E82DA6B34CBCACAEAC4F /* FontAwesome5_Regular.ttf */,
				134A3CB56CD04B789C58A72A /* FontAwesome5_Solid.ttf */,
				897F84C3F0AC471587D00985 /* Fontisto.ttf */,
				7016DDA123804B15A6A7F6F1 /* Foundation.ttf */,
				8C8A50F969454B748EC5585D /* Ionicons.ttf */,
				9694E3BE03A84B1CA5406614 /* MaterialCommunityIcons.ttf */,
				26E1F868B63D46CE956C85C5 /* MaterialIcons.ttf */,
				22E0E60203D044359E3E2583 /* Octicons.ttf */,
				F1A29D4B982048EFA0DFB2F3 /* SimpleLineIcons.ttf */,
				B6791F99F98946488D961B7F /* Zocial.ttf */,
				54DFF252381042EFB71D0F0B /* BeVietnam-Bold.ttf */,
				C805855EC8CD4D65BFA5FFD3 /* BeVietnam-BoldItalic.ttf */,
				719103D3B019400E991AA405 /* BeVietnam-ExtraBold.ttf */,
				A2954B0365854770AA763705 /* BeVietnam-ExtraBoldItalic.ttf */,
				5C1C7133BA454CB1A25A005D /* BeVietnam-Italic.ttf */,
				BFC23E262AD6447E8EF46721 /* BeVietnam-Light.ttf */,
				CA56584CB49D4AA497C063FC /* BeVietnam-LightItalic.ttf */,
				20BC6D0131044EFDB07B9A9E /* BeVietnam-Medium.ttf */,
				FE9D643B69E84A1ABAFE8556 /* BeVietnam-MediumItalic.ttf */,
				8DACE7FBF9634CC19A4862D5 /* BeVietnam-Regular.ttf */,
				75FA1CF825F64B10A892D02F /* BeVietnam-SemiBold.ttf */,
				82C6FB3BA4634F608C85AF23 /* BeVietnam-SemiBoldItalic.ttf */,
				411C68969BE348C89FB31CEE /* BeVietnam-Thin.ttf */,
				01504B89FDF943DD971D269D /* BeVietnam-ThinItalic.ttf */,
				DB897C3A0CA84E52A9AA6C45 /* AntDesign.ttf */,
				B76901C5C62F42E891899AE6 /* Entypo.ttf */,
				D4D60DFAD9C54A9DBB694247 /* EvilIcons.ttf */,
				585006A942D0443CAC829408 /* Feather.ttf */,
				154F85AF25064CE0B1567ED2 /* FontAwesome.ttf */,
				A04F87105F3144D7888D3BB6 /* FontAwesome5_Brands.ttf */,
				DA8176FFBF5B4A0886EF0544 /* FontAwesome5_Regular.ttf */,
				0D3CBB7CEA874A7188C57582 /* FontAwesome5_Solid.ttf */,
				7A4949CED7584DD587FAC5F4 /* Fontisto.ttf */,
				D8EB8B320BA442A6BF2F1CB2 /* Foundation.ttf */,
				6A60E48F12294884B98BA975 /* Ionicons.ttf */,
				04BC7BB3A71149609AEF9485 /* MaterialCommunityIcons.ttf */,
				CAEA3E7592864011B06BC371 /* MaterialIcons.ttf */,
				B9F689793088400290E28754 /* Octicons.ttf */,
				438B8344A46942988DADB5BC /* Roboto_medium.ttf */,
				9744B429504842FFBEDC8274 /* Roboto.ttf */,
				048035E030D54910B6BA2418 /* rubicon-icon-font.ttf */,
				46D326B7D01348A19EC40A7A /* SimpleLineIcons.ttf */,
				C07BDF43AE624E368ED6DC15 /* Zocial.ttf */,
				5518F94B5A494C9BBD6B285E /* HeyUVietnam-Regular.ttf */,
				205D82E977C44A9A974BD6AA /* HeyUVietnam-SemiBold.ttf */,
				C6A7D298D41F44DBADEEDFC5 /* HeyUVietnam-Bold.ttf */,
				5B43007DD78B4914B3ECF7DD /* HeyUVietnam-Italic.ttf */,
				429BEC116F8D406C9087AE53 /* HeyUVietnam-Light.ttf */,
				83F1BDA150994EE5A33E8F59 /* HeyUVietnam-LightItalic.ttf */,
				31E763B382534CDC9A226980 /* HeyUVietnam-Medium.ttf */,
				E80258E7D06A446AA5131722 /* HeyUVietnam-MediumItalic.ttf */,
				5A07E5803A9F4C899E134E44 /* HeyUVietnam-SemiBoldItalic.ttf */,
				C79548869CC048A7B2AB44C9 /* HeyUVietnam-ThinI.ttf */,
				D1D480036651450DBE3975B6 /* HeyUVietnam-ThinItalic.ttf */,
				C185AE4F508F4E38A68790C0 /* heyu.ttf */,
				C1AFD28882BB42789D0062CD /* PenguinBoldIcon.ttf */,
				CE1D0FCB3F8540F09C9F00C4 /* PenguinLinearIcon.ttf */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* sanship */ = {
			isa = PBXGroup;
			children = (
				5E3DE02E2DE4478100AF2F4A /* AudioSessionManager.h */,
				5E3DE02F2DE4478100AF2F4A /* AudioSessionManager.m */,
				E33AE1B729E6893000F52972 /* sound_heyu.wav */,
				ED62B68E270D387D00EBF9D0 /* sound_heyu.mp3 */,
				E3EC9816260C7A5000E870CA /* PayZaloBridge.h */,
				E3EC9815260C7A5000E870CA /* PayZaloBridge.m */,
				03199F8C24FE49BB0053B053 /* new_move_order.aiff */,
				E372834C23E5645500E3E8AA /* message.mp3 */,
				E372834D23E5645500E3E8AA /* newcall.aiff */,
				E372834723E563B700E3E8AA /* incallmanager_ringback.mp3 */,
				E372834823E563B700E3E8AA /* new_order.mp3 */,
				E39C66E823CC6EAA00B4C7F6 /* sanship.entitlements */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				E3E14AFC2E4B1D69004FCD29 /* AppDelegate.mm */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				13B07FB71A68108700A75B9A /* main.m */,
				E37A5EEC23D5E82A0062657C /* Images.xcassets */,
				33DB82452687015800F9292C /* LaunchScreen.storyboard */,
				33F8ACDB267DE94C009DC270 /* Dynamic.swift */,
				47EAB736F93F473F805CEA34 /* BootSplash.storyboard */,
				9A99F047C02C4BA6A82F6098 /* Colors.xcassets */,
				ED61C16FCA9D78AB81DDBF99 /* PrivacyInfo.xcprivacy */,
			);
			name = sanship;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				E37399772BDA653B00058949 /* SwiftUI.framework */,
				E3A32223277B0EE100B90FB7 /* LinkPresentation.framework */,
				E3EC9813260C7A1900E870CA /* zpdk.framework */,
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				ED2971642150620600B7C4FE /* JavaScriptCore.framework */,
				7B6263953B551980F4545BC8 /* libPods-sanship.a */,
				25E3568D8F1A4DCF68E8C5F4 /* libPods-sanship-sanshipTests.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				3382D6BC268EEABB00E74E99 /* animationLoginScreen.json */,
				E3844A4723E3E79B00309FE0 /* sanship.swift */,
				13B07FAE1A68108700A75B9A /* sanship */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				00E356EF1AD99517003FC87E /* sanshipTests */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				FF2C3D69585E9A957B5BBEED /* Pods */,
				0D7E9A01EA1248E7BF44FCEC /* Resources */,
				E3844A4623E3E79A00309FE0 /* sanship-Bridging-Header.h */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* Wow Worker.app */,
				00E356EE1AD99517003FC87E /* sanshipTests.xctest */,
				2D02E47B1E0B4A5D006451C7 /* sanship-tvOS.app */,
				2D02E4901E0B4A5D006451C7 /* sanship-tvOSTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		FF2C3D69585E9A957B5BBEED /* Pods */ = {
			isa = PBXGroup;
			children = (
				DBA15D49B1F449177C1A23DE /* Pods-sanship.debug.xcconfig */,
				517A7329FF728037E03582EB /* Pods-sanship.release.xcconfig */,
				483D818A68FE95992F373E9B /* Pods-sanship-sanshipTests.debug.xcconfig */,
				97C8E1F95667057C58D59E35 /* Pods-sanship-sanshipTests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		00E356ED1AD99517003FC87E /* sanshipTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "sanshipTests" */;
			buildPhases = (
				C8E6891DFB466AD4566F57BC /* [CP] Check Pods Manifest.lock */,
				00E356EA1AD99517003FC87E /* Sources */,
				00E356EB1AD99517003FC87E /* Frameworks */,
				00E356EC1AD99517003FC87E /* Resources */,
				DA199076B68F60F2403B6136 /* [CP] Embed Pods Frameworks */,
				846F4B91ABEAF3CD32E5909B /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				00E356F51AD99517003FC87E /* PBXTargetDependency */,
			);
			name = sanshipTests;
			productName = sanshipTests;
			productReference = 00E356EE1AD99517003FC87E /* sanshipTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		13B07F861A680F5B00A75B9A /* sanship */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "sanship" */;
			buildPhases = (
				3882AA493222AC17C4616233 /* [CP] Check Pods Manifest.lock */,
				FD10A7F022414F080027D42C /* Start Packager */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				159EF98EAA63FED616E6F04F /* [CP] Embed Pods Frameworks */,
				CF5F603F03DA72E403A821F3 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = sanship;
			productName = sanship;
			productReference = 13B07F961A680F5B00A75B9A /* Wow Worker.app */;
			productType = "com.apple.product-type.application";
		};
		2D02E47A1E0B4A5D006451C7 /* sanship-tvOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2D02E4BA1E0B4A5E006451C7 /* Build configuration list for PBXNativeTarget "sanship-tvOS" */;
			buildPhases = (
				FD10A7F122414F3F0027D42C /* Start Packager */,
				2D02E4771E0B4A5D006451C7 /* Sources */,
				2D02E4781E0B4A5D006451C7 /* Frameworks */,
				2D02E4791E0B4A5D006451C7 /* Resources */,
				2D02E4CB1E0B4B27006451C7 /* Bundle React Native Code And Images */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "sanship-tvOS";
			productName = "sanship-tvOS";
			productReference = 2D02E47B1E0B4A5D006451C7 /* sanship-tvOS.app */;
			productType = "com.apple.product-type.application";
		};
		2D02E48F1E0B4A5D006451C7 /* sanship-tvOSTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2D02E4BB1E0B4A5E006451C7 /* Build configuration list for PBXNativeTarget "sanship-tvOSTests" */;
			buildPhases = (
				2D02E48C1E0B4A5D006451C7 /* Sources */,
				2D02E48D1E0B4A5D006451C7 /* Frameworks */,
				2D02E48E1E0B4A5D006451C7 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				2D02E4921E0B4A5D006451C7 /* PBXTargetDependency */,
			);
			name = "sanship-tvOSTests";
			productName = "sanship-tvOSTests";
			productReference = 2D02E4901E0B4A5D006451C7 /* sanship-tvOSTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 940;
				ORGANIZATIONNAME = Facebook;
				TargetAttributes = {
					00E356ED1AD99517003FC87E = {
						CreatedOnToolsVersion = 6.2;
						DevelopmentTeam = 7DBZQUF389;
						TestTargetID = 13B07F861A680F5B00A75B9A;
					};
					13B07F861A680F5B00A75B9A = {
						DevelopmentTeam = 7DBZQUF389;
						LastSwiftMigration = 1010;
						SystemCapabilities = {
							com.apple.BackgroundModes = {
								enabled = 1;
							};
							com.apple.Keychain = {
								enabled = 1;
							};
							com.apple.Push = {
								enabled = 1;
							};
						};
					};
					2D02E47A1E0B4A5D006451C7 = {
						CreatedOnToolsVersion = 8.2.1;
						ProvisioningStyle = Automatic;
					};
					2D02E48F1E0B4A5D006451C7 = {
						CreatedOnToolsVersion = 8.2.1;
						ProvisioningStyle = Automatic;
						TestTargetID = 2D02E47A1E0B4A5D006451C7;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "sanship" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
				Base,
				vi,
				de,
				he,
				ar,
				el,
				ja,
				uk,
				zh_CN,
				nb,
				es,
				da,
				it,
				sk,
				pt_PT,
				ms,
				sv,
				cs,
				ko,
				hu,
				tr,
				pl,
				zh_TW,
				en_GB,
				ru,
				fr,
				fi,
				id,
				nl,
				th,
				pt,
				ro,
				hr,
				ca,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* sanship */,
				00E356ED1AD99517003FC87E /* sanshipTests */,
				2D02E47A1E0B4A5D006451C7 /* sanship-tvOS */,
				2D02E48F1E0B4A5D006451C7 /* sanship-tvOSTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		00E356EC1AD99517003FC87E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E372834B23E563B700E3E8AA /* new_order.mp3 in Resources */,
				03199F8D24FE49BB0053B053 /* new_move_order.aiff in Resources */,
				3382D6BD268EEABB00E74E99 /* animationLoginScreen.json in Resources */,
				E372834A23E563B700E3E8AA /* incallmanager_ringback.mp3 in Resources */,
				E372834E23E5645500E3E8AA /* message.mp3 in Resources */,
				E372834F23E5645500E3E8AA /* newcall.aiff in Resources */,
				E37A5EED23D5E82B0062657C /* Images.xcassets in Resources */,
				ED62B68F270D387D00EBF9D0 /* sound_heyu.mp3 in Resources */,
				33DB82462687015800F9292C /* LaunchScreen.storyboard in Resources */,
				AFF9D9EFAA9D4185ABFD2A38 /* HeyUVietnam-Regular.ttf in Resources */,
				11211904D9F441CF90B36529 /* HeyUVietnam-SemiBold.ttf in Resources */,
				732FA94EC86648DBB87FB2D5 /* HeyUVietnam-Bold.ttf in Resources */,
				F0CC4D6CED644B9C9A29D8AF /* HeyUVietnam-Italic.ttf in Resources */,
				BF33939CA4214EEF81039F12 /* HeyUVietnam-Light.ttf in Resources */,
				55E19A496F684255AD888FD0 /* HeyUVietnam-LightItalic.ttf in Resources */,
				504AFD4BB6D94D8EB7AF9AB7 /* HeyUVietnam-Medium.ttf in Resources */,
				2136100BD03143FD958257BF /* HeyUVietnam-MediumItalic.ttf in Resources */,
				8987C927BE5A4B59A3542996 /* HeyUVietnam-SemiBoldItalic.ttf in Resources */,
				AE4445E1FBA244F6B40C1E5C /* HeyUVietnam-ThinI.ttf in Resources */,
				ADB5696603B94AE9B2E85C3F /* HeyUVietnam-ThinItalic.ttf in Resources */,
				7BD7988001CB4B7C82FBD785 /* heyu.ttf in Resources */,
				C30EE43FA78243398D85EF98 /* PenguinBoldIcon.ttf in Resources */,
				0223126FEECA4ED58BD5D98B /* PenguinLinearIcon.ttf in Resources */,
				CE9348B1F0AD450C9FECB056 /* BootSplash.storyboard in Resources */,
				9F5B1A0C8A6D44A58CD78A10 /* Colors.xcassets in Resources */,
				F7F120A97D9B14EE38164CA5 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D02E4791E0B4A5D006451C7 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A27F4F16E07DF45B9031009D /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D02E48E1E0B4A5D006451C7 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 8;
			files = (
			);
			inputPaths = (
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 1;
			shellPath = /bin/sh;
			shellScript = "export NODE_BINARY=node\n../node_modules/react-native/scripts/react-native-xcode.sh\n";
		};
		159EF98EAA63FED616E6F04F /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-sanship/Pods-sanship-frameworks.sh",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/JitsiWebRTC/WebRTC.framework/WebRTC",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/hermes-engine/Pre-built/hermes.framework/hermes",
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/WebRTC.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/hermes.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-sanship/Pods-sanship-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		2D02E4CB1E0B4B27006451C7 /* Bundle React Native Code And Images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Bundle React Native Code And Images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export NODE_BINARY=node\n../node_modules/react-native/scripts/react-native-xcode.sh";
		};
		3882AA493222AC17C4616233 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-sanship-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		846F4B91ABEAF3CD32E5909B /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-sanship-sanshipTests/Pods-sanship-sanshipTests-resources.sh",
				"${PODS_ROOT}/GoogleMaps/Maps/Frameworks/GoogleMaps.xcframework/ios-arm64/GoogleMaps.framework/Resources/GoogleMaps.bundle",
				"${PODS_ROOT}/GoogleMaps/Maps/Frameworks/GoogleMaps.xcframework/ios-arm64_x86_64-simulator/GoogleMaps.framework/Resources/GoogleMaps.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/RCT-Folly/RCT-Folly_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/RNImageCropPicker/RNImageCropPickerPrivacyInfo.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/RNImageCropPicker/QBImagePicker.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/RNSVG/RNSVGFilters.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/React-Core/React-Core_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/React-cxxreact/React-cxxreact_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/TOCropViewController/TOCropViewControllerBundle.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/boost/boost_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/glog/glog_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/lottie-ios/LottiePrivacyInfo.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/lottie-react-native/Lottie_React_Native_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/react-native-blob-util/ReactNativeBlobUtilPrivacyInfo.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/react-native-cameraroll/RNCameraRollPrivacyInfo.bundle",
				"${PODS_ROOT}/../../node_modules/@react-native-vector-icons/entypo/fonts/Entypo.ttf",
				"${PODS_ROOT}/../../node_modules/@react-native-vector-icons/fontisto/fonts/Fontisto.ttf",
				"${PODS_ROOT}/../../node_modules/@react-native-vector-icons/ionicons/fonts/Ionicons.ttf",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GoogleMaps.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RCT-Folly_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RNImageCropPickerPrivacyInfo.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/QBImagePicker.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RNSVGFilters.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/React-Core_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/React-cxxreact_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/TOCropViewControllerBundle.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/boost_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/glog_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/LottiePrivacyInfo.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Lottie_React_Native_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/ReactNativeBlobUtilPrivacyInfo.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RNCameraRollPrivacyInfo.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Entypo.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Fontisto.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Ionicons.ttf",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-sanship-sanshipTests/Pods-sanship-sanshipTests-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		C8E6891DFB466AD4566F57BC /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-sanship-sanshipTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		CF5F603F03DA72E403A821F3 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-sanship/Pods-sanship-resources.sh",
				"${PODS_ROOT}/GoogleMaps/Maps/Frameworks/GoogleMaps.xcframework/ios-arm64/GoogleMaps.framework/Resources/GoogleMaps.bundle",
				"${PODS_ROOT}/GoogleMaps/Maps/Frameworks/GoogleMaps.xcframework/ios-arm64_x86_64-simulator/GoogleMaps.framework/Resources/GoogleMaps.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/RCT-Folly/RCT-Folly_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/RNImageCropPicker/RNImageCropPickerPrivacyInfo.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/RNImageCropPicker/QBImagePicker.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/RNSVG/RNSVGFilters.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/React-Core/React-Core_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/React-cxxreact/React-cxxreact_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/TOCropViewController/TOCropViewControllerBundle.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/boost/boost_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/glog/glog_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/lottie-ios/LottiePrivacyInfo.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/lottie-react-native/Lottie_React_Native_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/react-native-blob-util/ReactNativeBlobUtilPrivacyInfo.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/react-native-cameraroll/RNCameraRollPrivacyInfo.bundle",
				"${PODS_ROOT}/../../node_modules/@react-native-vector-icons/entypo/fonts/Entypo.ttf",
				"${PODS_ROOT}/../../node_modules/@react-native-vector-icons/fontisto/fonts/Fontisto.ttf",
				"${PODS_ROOT}/../../node_modules/@react-native-vector-icons/ionicons/fonts/Ionicons.ttf",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GoogleMaps.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RCT-Folly_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RNImageCropPickerPrivacyInfo.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/QBImagePicker.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RNSVGFilters.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/React-Core_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/React-cxxreact_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/TOCropViewControllerBundle.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/boost_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/glog_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/LottiePrivacyInfo.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Lottie_React_Native_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/ReactNativeBlobUtilPrivacyInfo.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RNCameraRollPrivacyInfo.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Entypo.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Fontisto.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Ionicons.ttf",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-sanship/Pods-sanship-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		DA199076B68F60F2403B6136 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-sanship-sanshipTests/Pods-sanship-sanshipTests-frameworks.sh",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/JitsiWebRTC/WebRTC.framework/WebRTC",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/hermes-engine/Pre-built/hermes.framework/hermes",
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/WebRTC.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/hermes.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-sanship-sanshipTests/Pods-sanship-sanshipTests-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		FD10A7F022414F080027D42C /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > \"${SRCROOT}/../node_modules/react-native/scripts/.packager.env\"\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open \"$SRCROOT/../node_modules/react-native/scripts/launchPackager.command\" || echo \"Can't start packager automatically\"\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
		FD10A7F122414F3F0027D42C /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > \"${SRCROOT}/../node_modules/react-native/scripts/.packager.env\"\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open \"$SRCROOT/../node_modules/react-native/scripts/launchPackager.command\" || echo \"Can't start packager automatically\"\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		00E356EA1AD99517003FC87E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				00E356F31AD99517003FC87E /* sanshipTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E3EC9817260C7A5000E870CA /* PayZaloBridge.m in Sources */,
				5E3DE0302DE4478100AF2F4A /* AudioSessionManager.m in Sources */,
				33F8ACDC267DE94C009DC270 /* Dynamic.swift in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
				E3E14AFD2E4B1D69004FCD29 /* AppDelegate.mm in Sources */,
				E3844A4823E3E79B00309FE0 /* sanship.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D02E4771E0B4A5D006451C7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2D02E4BF1E0B4AB3006451C7 /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D02E48C1E0B4A5D006451C7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2DCD954D1E0B4F2C00145EB5 /* sanshipTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		00E356F51AD99517003FC87E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 13B07F861A680F5B00A75B9A /* sanship */;
			targetProxy = 00E356F41AD99517003FC87E /* PBXContainerItemProxy */;
		};
		2D02E4921E0B4A5D006451C7 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 2D02E47A1E0B4A5D006451C7 /* sanship-tvOS */;
			targetProxy = 2D02E4911E0B4A5D006451C7 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		00E356F61AD99517003FC87E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 483D818A68FE95992F373E9B /* Pods-sanship-sanshipTests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				DEVELOPMENT_TEAM = 7DBZQUF389;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = sanshipTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Wow Worker.app/Wow Worker";
			};
			name = Debug;
		};
		00E356F71AD99517003FC87E /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 97C8E1F95667057C58D59E35 /* Pods-sanship-sanshipTests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				COPY_PHASE_STRIP = NO;
				DEVELOPMENT_TEAM = 7DBZQUF389;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
				);
				INFOPLIST_FILE = sanshipTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Wow Worker.app/Wow Worker";
			};
			name = Release;
		};
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DBA15D49B1F449177C1A23DE /* Pods-sanship.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODEPUSH_KEY = 2vUX3laIRTlZzkclTUdtaeyoT1oX4ksvOXqog;
				CODE_SIGN_ENTITLEMENTS = sanship/sanship.entitlements;
				CURRENT_PROJECT_VERSION = 100011;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = 7DBZQUF389;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
				);
				INFOPLIST_FILE = sanship/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Wow Thợ";
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				MARKETING_VERSION = 1.0.11;
				OTHER_CFLAGS = (
					"$(inherited)",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/Google-Maps-iOS-Utils/GoogleMapsUtils.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/RNAudioRecorderPlayer/RNAudioRecorderPlayer.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/lottie-ios/Lottie.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/lottie-react-native/lottie_react_native.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-audio-waveform/react_native_audio_waveform.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-document-picker/react_native_document_picker.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-geolocation-service/react_native_geolocation_service.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-video/react_native_video.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/DoubleConversion/DoubleConversion.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/RCTDeprecation/RCTDeprecation.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/RCTFabric/React-RCTFabric.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/RCTRuntime/React-RCTRuntime.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/RCTTypeSafety/RCTTypeSafety.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/RNBootSplash/RNBootSplash.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/RNReanimated/RNReanimated.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/React/React-Core.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/ReactAppDependencyProvider/ReactAppDependencyProvider.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/ReactCodegen/ReactCodegen.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/ReactCommon/ReactCommon.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/React_Fabric/React-Fabric.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/React_FabricComponents/React-FabricComponents.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/React_NativeModulesApple/React-NativeModulesApple.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/React_RCTAppDelegate/React-RCTAppDelegate.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/SSZipArchive/SSZipArchive.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/folly/RCT-Folly.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/glog/glog.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/jsi/React-jsi.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/jsinspector_modern/React-jsinspector.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/jsinspector_modern_tracing/React-jsinspectortracing.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/react_debug/React-debug.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/react_featureflags/React-featureflags.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/react_nativemodule_defaults/React-defaultsnativemodule.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/react_nativemodule_dom/React-domnativemodule.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/react_nativemodule_featureflags/React-featureflagsnativemodule.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/react_nativemodule_idlecallbacks/React-idlecallbacksnativemodule.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/react_nativemodule_microtasks/React-microtasksnativemodule.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/react_renderer_css/React-renderercss.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/react_renderer_debug/React-rendererdebug.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/react_renderer_graphics/React-graphics.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/react_renderer_imagemanager/React-ImageManager.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/react_runtime/React-jsitooling.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/react_utils/React-utils.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/reacthermes/React-hermes.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/yoga/Yoga.modulemap\"",
					"$(inherited)",
					"$(inherited)",
					"-DRCT_NEW_ARCH_ENABLED",
					"-DREACT_NATIVE_MINOR_VERSION=79",
					"-DREANIMATED_VERSION=3.19.0",
					"$(inherited)",
					"-DREACT_NATIVE_MINOR_VERSION=79",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
					"-ld_classic",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.heyu.worker;
				PRODUCT_NAME = "Wow Worker";
				SWIFT_OBJC_BRIDGING_HEADER = "sanship-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 4.2;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 517A7329FF728037E03582EB /* Pods-sanship.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODEPUSH_KEY = eP5GKTNlifGyJG1KT8wXafmLdOlo4ksvOXqog;
				CODE_SIGN_ENTITLEMENTS = sanship/sanship.entitlements;
				CURRENT_PROJECT_VERSION = 100011;
				DEVELOPMENT_TEAM = 7DBZQUF389;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
				);
				INFOPLIST_FILE = sanship/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Wow Thợ";
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				MARKETING_VERSION = 1.0.11;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
					"-ld_classic",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.heyu.worker;
				PRODUCT_NAME = "Wow Worker";
				SWIFT_OBJC_BRIDGING_HEADER = "sanship-Bridging-Header.h";
				SWIFT_VERSION = 4.2;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		2D02E4971E0B4A5E006451C7 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "App Icon & Top Shelf Image";
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_TESTABILITY = YES;
				GCC_NO_COMMON_BLOCKS = YES;
				INFOPLIST_FILE = "sanship-tvOS/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.facebook.REACT.sanship-tvOS";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				TARGETED_DEVICE_FAMILY = 3;
				TVOS_DEPLOYMENT_TARGET = 9.2;
			};
			name = Debug;
		};
		2D02E4981E0B4A5E006451C7 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "App Icon & Top Shelf Image";
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				GCC_NO_COMMON_BLOCKS = YES;
				INFOPLIST_FILE = "sanship-tvOS/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.facebook.REACT.sanship-tvOS";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				TARGETED_DEVICE_FAMILY = 3;
				TVOS_DEPLOYMENT_TARGET = 9.2;
			};
			name = Release;
		};
		2D02E4991E0B4A5E006451C7 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_TESTABILITY = YES;
				GCC_NO_COMMON_BLOCKS = YES;
				INFOPLIST_FILE = "sanship-tvOSTests/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.facebook.REACT.sanship-tvOSTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/sanship-tvOS.app/sanship-tvOS";
				TVOS_DEPLOYMENT_TARGET = 10.1;
			};
			name = Debug;
		};
		2D02E49A1E0B4A5E006451C7 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				GCC_NO_COMMON_BLOCKS = YES;
				INFOPLIST_FILE = "sanship-tvOSTests/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.facebook.REACT.sanship-tvOSTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/sanship-tvOS.app/sanship-tvOS";
				TVOS_DEPLOYMENT_TARGET = 10.1;
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODEPUSH_KEY = 2vUX3laIRTlZzkclTUdtaeyoT1oX4ksvOXqog;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = "$(inherited)  ";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) DEBUG";
				USE_HERMES = true;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODEPUSH_KEY = eP5GKTNlifGyJG1KT8wXafmLdOlo4ksvOXqog;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = "";
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_LDFLAGS = "$(inherited)  ";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "sanshipTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00E356F61AD99517003FC87E /* Debug */,
				00E356F71AD99517003FC87E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "sanship" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2D02E4BA1E0B4A5E006451C7 /* Build configuration list for PBXNativeTarget "sanship-tvOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2D02E4971E0B4A5E006451C7 /* Debug */,
				2D02E4981E0B4A5E006451C7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2D02E4BB1E0B4A5E006451C7 /* Build configuration list for PBXNativeTarget "sanship-tvOSTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2D02E4991E0B4A5E006451C7 /* Debug */,
				2D02E49A1E0B4A5E006451C7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "sanship" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
