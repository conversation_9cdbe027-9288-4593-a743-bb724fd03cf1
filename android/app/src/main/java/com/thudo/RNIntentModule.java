package com.thudo;

import android.app.Activity;
import android.content.Intent;
import android.os.Build;
import android.provider.Settings;
import androidx.core.app.NotificationManagerCompat;
import android.util.Log;
import android.net.Uri;
import android.util.Log;
import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.ReadableMapKeySetIterator;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.bridge.ActivityEventListener;
import com.facebook.react.bridge.BaseActivityEventListener;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @file ....java
 * @brief ....java source file.
 * <p>
 * File/module comments.
 * @mobile 01684499886
 * @note No Note at the moment
 * @bug No known bugs.
 * <p>
 * <pre>
 * MODIFICATION HISTORY:
 *
 * Ver   Who  	  Date       Changes
 * ----- --------- ---------- -----------------------------------------------
 * 1.00  phuongtq   3/9/2016 First release
 *
 *
 * </pre>
 */

public class RNIntentModule extends ReactContextBaseJavaModule {
    private ReactContext _reactContext;
    private Callback mCallback;
    private static final int FILE_SELECT_CODE = 20190903;
    private static final String TAG = RNIntentModule.class.getSimpleName();
    public RNIntentModule(ReactApplicationContext reactContext){
        super(reactContext);
        _reactContext  =reactContext;

    }
    @Override
    public String getName(){
        return "RNIntent";
    }

    @Override
    public Map<String, Object> getConstants() {
        final Map<String, Object> constants = new HashMap<>();
        constants.put("unknow", "SanShip2017!Q@W#E$R%T^Y");
        return constants;
    }

    @ReactMethod
    public void getIntentExtra(String key, Promise promise){
        try {
            WritableMap map = Arguments.createMap();
            Log.d("RNIntentModule", "getIntentExtra:" + key + ":" + getCurrentActivity().getIntent().getStringExtra(key));
            map.putString(key, getCurrentActivity().getIntent().getStringExtra(key) );
            promise.resolve(map);
        }
        catch(Exception e){
            promise.reject(e);
        }
    }

    @ReactMethod
    public void openIntent(ReadableMap options,String type ,Callback callback){

        ReadableMapKeySetIterator iterator= options.keySetIterator();

        Intent intent = new Intent(Intent.ACTION_VIEW);
        try {
            intent.setType(type);
            while (true) {
                if (iterator.hasNextKey()) {
                    String key = iterator.nextKey();
                    Log.d("RNIntentModule", "openIntent: " + key);
                    intent.putExtra(key, options.getString(key));
                } else {
                    break;
                }
            }
            getCurrentActivity().startActivity(intent);
            callback.invoke("OK");
        }
        catch(Exception ex){
            callback.invoke("ERR" + ex);
        }
    }

    @ReactMethod
    public void makeCall(String phoneNumberString, Boolean phoneAppOnly) {
      //Needs permission "android.permission.CALL_PHONE"
      Intent sendIntent = new Intent(Intent.ACTION_CALL, Uri.parse("tel:" + phoneNumberString.replaceAll("#", "%23").trim()));
      sendIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
      if (phoneAppOnly) {
          sendIntent.setPackage("com.android.server.telecom");
      }

      //Check that an app exists to receive the intent
      if (sendIntent.resolveActivity(this._reactContext.getPackageManager()) != null) {
        try {
          this._reactContext.startActivity(sendIntent);
        } catch(SecurityException ex) {
          Log.d(TAG, ex.getMessage());

          this.sendPhoneDial(phoneNumberString, phoneAppOnly);
        }
      }
    }
    @ReactMethod
    public void sendPhoneDial(String phoneNumberString, Boolean phoneAppOnly) {
      Intent sendIntent = new Intent(Intent.ACTION_DIAL, Uri.parse("tel:" + phoneNumberString.trim()));
      sendIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
      if (phoneAppOnly) {
          sendIntent.setPackage("com.android.server.telecom");
      }

      //Check that an app exists to receive the intent
      if (sendIntent.resolveActivity(this._reactContext.getPackageManager()) != null) {
        this._reactContext.startActivity(sendIntent);
      }
    }
    @ReactMethod
    public void moveTaskToBack(){
        if (getCurrentActivity() != null)
            getCurrentActivity().moveTaskToBack(true);

    }

    @ReactMethod
    public void exit(){
        Activity activity = getCurrentActivity();
        activity.finish();
        System.exit(0);
    }

    @ReactMethod
    public void checkAllowNotify(Callback callback){
        boolean notifyEnable = NotificationManagerCompat.from(this._reactContext).areNotificationsEnabled();
        callback.invoke(notifyEnable);
    }

    @ReactMethod
    public void openPermissionSetting(){

        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        Uri uri = Uri.fromParts("package", this._reactContext.getPackageName(), null);
        intent.setData(uri);
        this._reactContext.startActivity(intent);

    }

    @ReactMethod
    public void openNotifyPermissionSetting(){

        if (android.os.Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            Intent intent = new Intent();
            intent.setAction("android.settings.APP_NOTIFICATION_SETTINGS");
            intent.putExtra("app_package", this._reactContext.getPackageName());
            intent.putExtra("app_uid", this._reactContext.getApplicationInfo().uid);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            this._reactContext.startActivity(intent);
        } else if (android.os.Build.VERSION.SDK_INT == Build.VERSION_CODES.KITKAT) {
            Intent intent = new Intent();
            intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
            intent.addCategory(Intent.CATEGORY_DEFAULT);
            intent.setData(Uri.parse("package:" + this._reactContext.getPackageName()));
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            this._reactContext.startActivity(intent);
        }

    }
    private final ActivityEventListener mActivityEventListener = new BaseActivityEventListener() {
      @Override
      public void onActivityResult(Activity activity, int requestCode, int resultCode, Intent data) {
          if (requestCode == FILE_SELECT_CODE && data!=null) {
              Uri uri = data.getData();
              mCallback.invoke(uri.getPath());
          }
      }
    };

}