package com.thudo;

import android.app.Activity;
import android.content.Intent;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.BaseActivityEventListener;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.modules.core.DeviceEventManagerModule;

import vn.zalopay.sdk.listeners.PayOrderListener;
import vn.zalopay.sdk.listeners.ConfirmAutoDebitListener;
import vn.zalopay.sdk.ZaloPayError;
import vn.zalopay.sdk.ZaloPaySDK;

public class ZPModule extends ReactContextBaseJavaModule {
    private ReactApplicationContext mReactContext;
    final String PAYMENTSUCCESS = "1";
    final String PAYMENTFAILED = "-1";
    final String PAYMENTCANCELED = "4";

    PayOrderListener listener = new PayOrderListener() {
        @Override
        public void onPaymentSucceeded(String transactionId, String transToken, String appTransID) {
            // Handle Success
            WritableMap params = Arguments.createMap();
            params.putString("transactionId", transactionId);
            params.putString("transToken", transToken);
            params.putString("appTransID", appTransID);
            params.putString("returnCode", PAYMENTSUCCESS);

            sendEvent(mReactContext, "EventPayZalo", params);
        }

        @Override
        public void onPaymentCanceled(String transToken, String appTransID) {
            // Handle Cancel
            WritableMap params = Arguments.createMap();
            params.putString("returnCode",  PAYMENTCANCELED);
            params.putString("zpTranstoken", transToken);
            params.putString("appTransID", appTransID);

            sendEvent(mReactContext, "EventPayZalo", params);
        }

        @Override
        public void onPaymentError(ZaloPayError zaloPayError, String transToken, String appTransID) {
            // Handle Error
            WritableMap params = Arguments.createMap();
            params.putString("returnCode",  PAYMENTFAILED);
            params.putString("zpTranstoken", transToken);
            params.putString("appTransID", appTransID);

            sendEvent(mReactContext, "EventPayZalo", params);
        }
    };

    ConfirmAutoDebitListener confirmAutoDebitListener = new ConfirmAutoDebitListener() {

        @Override
        public void onConfirmSucceeded(String binding_token) {
            // Handle Success
            WritableMap params = Arguments.createMap();
            params.putString("binding_token", binding_token);
            params.putString("returnCode", PAYMENTSUCCESS);

            sendEvent(mReactContext, "EventPayZaloBind", params);
        }
        @Override
        public void onConfirmError(ZaloPayError zaloPayError, String binding_token) {

            WritableMap params = Arguments.createMap();
            params.putString("returnCode",  PAYMENTFAILED);
            params.putString("binding_token", binding_token);

            sendEvent(mReactContext, "EventPayZaloBind", params);
        }
    };

    BaseActivityEventListener activityEventListener = new BaseActivityEventListener(){
        @Override
        public void onNewIntent(Intent intent) {
            super.onNewIntent(intent);
        }
    };

    public ZPModule(ReactApplicationContext reactContext) {
        super(reactContext);
        mReactContext = reactContext;
        reactContext.addActivityEventListener(activityEventListener);
    }

    @Override
    public String getName() {
        return "PayZaloBridge";
    }

    @ReactMethod
    public void payOrder(String zpTransToken) {
        Activity currentActivity = getCurrentActivity();
        ZaloPaySDK.getInstance().payOrder(currentActivity, zpTransToken, "iochongbangvietnam://", listener);
    }

    @ReactMethod
    public void confirmAutoDebit(String binding_token) {
        Activity currentActivity = getCurrentActivity();
        ZaloPaySDK.getInstance().confirmAutoDebit(currentActivity, binding_token, "iochongbangvietnam://", confirmAutoDebitListener);
    }

    @ReactMethod
    public void installApp() {
        ZaloPaySDK.getInstance().navigateToZaloOnStore(mReactContext);
    }

    private void sendEvent(ReactContext reactContext, String eventName, WritableMap params) {
        reactContext.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                .emit(eventName, params);
    }


    @ReactMethod
    public void addListener(String eventName) {
        // Keep: Required for RN built in Event Emitter Calls.
    }

    @ReactMethod
    public void removeListeners(Integer count) {
        // Keep: Required for RN built in Event Emitter Calls.
    }
}
