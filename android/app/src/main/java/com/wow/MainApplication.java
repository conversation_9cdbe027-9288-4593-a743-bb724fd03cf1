
package com.wowworker;

import android.app.Application;

import android.os.AsyncTask;
import android.os.SystemClock;
import androidx.multidex.MultiDexApplication;
import android.widget.Toast;

import com.facebook.CallbackManager;
import com.facebook.FacebookSdk;
import com.facebook.appevents.AppEventsLogger;
import com.facebook.react.ReactApplication;

import com.facebook.react.ReactNativeHost;
import com.facebook.react.ReactPackage;
import com.thudo.FullLog;
import com.thudo.RNBridgeReloaderPackage;
import com.thudo.RNIntentPackage;

import java.util.Arrays;
import java.util.List;
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint;
import com.facebook.react.defaults.DefaultReactNativeHost;
import vn.zalopay.sdk.Environment;
import vn.zalopay.sdk.ZaloPaySDK;
import com.thudo.PayZaloBridge;
import com.facebook.react.PackageList;
import com.facebook.soloader.SoLoader;
import com.facebook.react.soloader.OpenSourceMergedSoMapping;

import com.microsoft.codepush.react.CodePush;




public class MainApplication extends MultiDexApplication implements ReactApplication {

  private static CallbackManager mCallbackManager = CallbackManager.Factory.create();

  protected static CallbackManager getCallbackManager() {
    return mCallbackManager;
  }

  @Override
  public void onCreate() {
    // If you opted-in for the New Architecture, we enable the TurboModule system
    // ReactFeatureFlags.useTurboModules = BuildConfig.IS_NEW_ARCHITECTURE_ENABLED;

    FacebookSdk.sdkInitialize(getApplicationContext());
    // If you want to use AppEventsLogger to log events.
    AppEventsLogger.activateApp(this);

    ZaloPaySDK.init(1272, Environment.PRODUCTION);
    super.onCreate();
    try {
      SoLoader.init(this, /* native exopackage */ OpenSourceMergedSoMapping.INSTANCE);
    }
    catch (Exception e){
      SoLoader.init(this, /* native exopackage */ false);
    }
    if (BuildConfig.IS_NEW_ARCHITECTURE_ENABLED) {
      // If you opted-in for the New Architecture, we load the native entry point for this app.
      DefaultNewArchitectureEntryPoint.load();
    }
    // ReactNativeFlipper.initializeFlipper(this, getReactNativeHost().getReactInstanceManager());
  }

  private final ReactNativeHost mReactNativeHost = new DefaultReactNativeHost(this) {
    @Override
    public boolean getUseDeveloperSupport() {
      return BuildConfig.DEBUG;
    }

    /**
     * Returns the name of the main module. Determines the URL used to fetch the JS bundle
     * from the packager server. It is only used when dev support is enabled.
     * This is the first file to be executed once the {@link ReactInstanceManager} is created.
     * e.g. "index.android"
     */
     @Override
    protected String getJSMainModuleName() {
      return "index";
    }

    @Override
    protected boolean isNewArchEnabled() {
      return BuildConfig.IS_NEW_ARCHITECTURE_ENABLED;
    }

    @Override
    protected Boolean isHermesEnabled() {
      return BuildConfig.IS_HERMES_ENABLED;
    }
    // 2. Override the getJSBundleFile method in order to let
    // the CodePush runtime determine where to get the JS
    // bundle location from on each app start

    @Override
    protected String getJSBundleFile() {
      return CodePush.getJSBundleFile();
    }

    protected List<ReactPackage> getPackages() {
     @SuppressWarnings("UnnecessaryLocalVariable")
     List<ReactPackage> packages = new PackageList(this).getPackages();
     // Packages that cannot be autolinked yet can be added manually here, for example:
     packages.add(new PayZaloBridge());
     packages.add(new RNIntentPackage());
     return packages;
   }
  };

  @Override
  public ReactNativeHost getReactNativeHost() {
    return mReactNativeHost;
  }
};
  /**
 * Loads Flipper in React Native templates.
 *
 * @param context
 */
