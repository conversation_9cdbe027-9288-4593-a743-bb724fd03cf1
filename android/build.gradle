// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 24
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "27.1.12297006"
        kotlinVersion = "2.0.21"
    }
    repositories {
        google()
        mavenCentral()

    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath('com.google.gms:google-services:4.4.2')
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}
apply plugin: "com.facebook.react.rootproject"
def REACT_NATIVE_VERSION = new File(['node', '--print',"JSON.parse(require('fs').readFileSync(require.resolve('react-native/package.json'), 'utf-8')).version"].execute(null, rootDir).text.trim())

ext {
    compileSdkVersion = 35
    targetSdkVersion = 35
    minSdkVersion = 24
    buildToolsVersion = "35.0.0"
    supportLibVersion = "28.0.0"
    googlePlayServicesVersion = "12.0.1"
    oreoEXPERIMENTAL = "yes"
    gradle3EXPERIMENTAL = "yes"
    playServicesLocationVersion = "21.0.1"
}
